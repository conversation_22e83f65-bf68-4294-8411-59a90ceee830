{% extends "base_auth.html" %}

{% block title %}SEO Optimization - Shopify AI Control{% endblock %}

{% block content %}
<h1 class="mb-4"><i class="fas fa-search me-2"></i>SEO Optimization</h1>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">SEO Settings</h5>
                
                <form id="seo-config-form">
                    <div class="mb-3">
                        <label class="form-label">Target Keywords</label>
                        <textarea class="form-control" name="keywords" rows="3" 
                                  placeholder="Enter target keywords separated by commas">{{ config.config_data.keywords if config else '' }}</textarea>
                        <div class="form-text">AI will optimize your content around these keywords</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">SEO Strategy</label>
                        <select class="form-select" name="strategy">
                            <option value="balanced" {% if config and config.config_data.strategy == 'balanced' %}selected{% endif %}>Balanced</option>
                            <option value="aggressive" {% if config and config.config_data.strategy == 'aggressive' %}selected{% endif %}>Aggressive</option>
                            <option value="conservative" {% if config and config.config_data.strategy == 'conservative' %}selected{% endif %}>Conservative</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="auto_optimize_titles" 
                                   {% if config and config.config_data.auto_optimize_titles %}checked{% endif %}>
                            <label class="form-check-label">
                                Automatically optimize product titles
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="auto_optimize_descriptions" 
                                   {% if config and config.config_data.auto_optimize_descriptions %}checked{% endif %}>
                            <label class="form-check-label">
                                Automatically optimize product descriptions
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="generate_meta_tags" 
                                   {% if config and config.config_data.generate_meta_tags %}checked{% endif %}>
                            <label class="form-check-label">
                                Generate meta tags automatically
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Quick Actions</h5>
                <button class="btn btn-outline-primary me-2" id="analyze-seo">
                    <i class="fas fa-chart-line me-1"></i> Analyze Current SEO
                </button>
                <button class="btn btn-outline-success" id="optimize-all">
                    <i class="fas fa-magic me-1"></i> Optimize All Products
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">SEO Score</h5>
                <div class="text-center">
                    <div class="display-1 text-primary">--</div>
                    <p class="text-muted">Run analysis to see your score</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">Tips</h5>
                <ul class="small">
                    <li>Use specific, relevant keywords</li>
                    <li>Keep product titles under 60 characters</li>
                    <li>Write unique descriptions for each product</li>
                    <li>Include keywords naturally in descriptions</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('seo-config-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const config = {
        keywords: formData.get('keywords'),
        strategy: formData.get('strategy'),
        auto_optimize_titles: formData.get('auto_optimize_titles') === 'on',
        auto_optimize_descriptions: formData.get('auto_optimize_descriptions') === 'on',
        generate_meta_tags: formData.get('generate_meta_tags') === 'on'
    };
    
    const response = await fetch('/ai/module/seo/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
    });
    
    if (response.ok) {
        alert('SEO settings saved successfully!');
    } else {
        alert('Failed to save settings');
    }
});

document.getElementById('analyze-seo').addEventListener('click', function() {
    alert('SEO analysis would be performed here');
});

document.getElementById('optimize-all').addEventListener('click', function() {
    if (confirm('This will optimize all products using AI. Continue?')) {
        alert('Product optimization would start here');
    }
});
</script>
{% endblock %}