{% extends "base_auth.html" %}

{% block title %}Store Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Your Shopify Stores</h5>
                    <a href="{{ url_for('shopify.connect') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Store
                    </a>
                </div>
                <div class="card-body">
                    {% if user_shops %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Store</th>
                                        <th>Domain</th>
                                        <th>Automation Schedule</th>
                                        <th>Status</th>
                                        <th>Current</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user_shop in user_shops %}
                                    <tr {% if user_shop.is_current %}class="table-primary"{% endif %}>
                                        <td>
                                            <strong>{{ user_shop.shop.shop_name or 'Unnamed Store' }}</strong>
                                            {% if user_shop.shop.owner %}
                                                <br><small class="text-muted">Owner: {{ user_shop.shop.owner }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <code>{{ user_shop.shop.shop_domain }}</code>
                                            {% if user_shop.shop.email %}
                                                <br><small class="text-muted">{{ user_shop.shop.email }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user_shop.automation_enabled %}
                                                <span class="badge badge-success">
                                                    {{ '%02d:%02d'|format(user_shop.automation_hour, user_shop.automation_minute) }} UTC
                                                </span>
                                            {% else %}
                                                <span class="badge badge-secondary">Disabled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user_shop.shop.shopify_access_token %}
                                                <span class="badge badge-success">Connected</span>
                                            {% else %}
                                                <span class="badge badge-danger">Disconnected</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user_shop.is_current %}
                                                <span class="badge badge-primary">Current</span>
                                            {% else %}
                                                <form method="POST" action="{{ url_for('auth.switch_store') }}" style="display: inline;">
                                                    <input type="hidden" name="shop_id" value="{{ user_shop.shop_id }}">
                                                    <button type="submit" class="btn btn-sm btn-outline-primary">
                                                        Switch To
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary" 
                                                        data-toggle="modal" 
                                                        data-target="#scheduleModal{{ user_shop.id }}">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                {% if not user_shop.is_current %}
                                                <form method="POST" action="{{ url_for('auth.remove_store') }}" style="display: inline;">
                                                    <input type="hidden" name="shop_id" value="{{ user_shop.shop_id }}">
                                                    <button type="submit" class="btn btn-outline-danger" 
                                                            onclick="return confirm('Are you sure you want to remove this store?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-store fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No stores connected</h5>
                            <p class="text-muted">Connect your first Shopify store to get started.</p>
                            <a href="{{ url_for('shopify.connect') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Connect Store
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Modals -->
{% for user_shop in user_shops %}
<div class="modal fade" id="scheduleModal{{ user_shop.id }}" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Automation Schedule - {{ user_shop.shop.shop_name }}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ url_for('auth.update_schedule') }}">
                <div class="modal-body">
                    <input type="hidden" name="user_shop_id" value="{{ user_shop.id }}">
                    
                    <div class="form-group">
                        <label for="automation_enabled{{ user_shop.id }}">Enable Automation</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" 
                                   id="automation_enabled{{ user_shop.id }}" 
                                   name="automation_enabled" 
                                   value="1"
                                   {% if user_shop.automation_enabled %}checked{% endif %}>
                            <label class="form-check-label" for="automation_enabled{{ user_shop.id }}">
                                Run daily automation tasks
                            </label>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="automation_hour{{ user_shop.id }}">Hour (UTC)</label>
                                <select class="form-control" name="automation_hour" id="automation_hour{{ user_shop.id }}">
                                    {% for hour in range(24) %}
                                    <option value="{{ hour }}" {% if hour == user_shop.automation_hour %}selected{% endif %}>
                                        {{ '%02d'|format(hour) }}:00 UTC
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="automation_minute{{ user_shop.id }}">Minute</label>
                                <select class="form-control" name="automation_minute" id="automation_minute{{ user_shop.id }}">
                                    {% for minute in [0, 15, 30, 45] %}
                                    <option value="{{ minute }}" {% if minute == user_shop.automation_minute %}selected{% endif %}>
                                        :{{ '%02d'|format(minute) }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            Each store runs at a different time to distribute load. Choose a time when your store has low traffic.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Schedule</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}