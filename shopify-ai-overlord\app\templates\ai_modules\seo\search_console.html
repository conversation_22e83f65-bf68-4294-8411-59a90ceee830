{% extends "base_auth.html" %}

{% block title %}Search Console - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    /* Data table styling */
    .search-console-table {
        font-size: 0.9rem;
    }
    
    .search-console-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .search-console-table td {
        vertical-align: middle;
    }
    
    .url-handle {
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        color: #495057;
        background-color: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
    }
    
    .type-badge {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .language-badge {
        font-size: 0.75rem;
        margin-right: 4px;
    }
    
    .translation-row {
        background-color: #f8f9fa;
    }
    
    .translation-indicator {
        font-size: 0.7rem;
        opacity: 0.8;
    }
    
    .search-box {
        max-width: 400px;
    }
    
    /* Make table responsive */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Copy button styling */
    .copy-btn {
        cursor: pointer;
        opacity: 0.6;
        transition: opacity 0.2s;
    }
    
    .copy-btn:hover {
        opacity: 1;
    }
    
    /* Stats cards */
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.products {
        border-left-color: #28a745;
    }
    
    .stats-card.collections {
        border-left-color: #17a2b8;
    }
    
    .stats-card.total {
        border-left-color: #007bff;
    }
    
    /* Performance metrics styling */
    .performance-metrics {
        min-width: 180px;
    }
    
    .performance-metrics .badge {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-search me-2"></i>Search Console</h1>
        <div>
            {% if google_oauth %}
                <div class="btn-group me-2" role="group">
                    <button class="btn btn-success" disabled>
                        <i class="fab fa-google me-2"></i>Connected: {{ google_oauth.google_email }}
                    </button>
                    <button class="btn btn-outline-primary" onclick="fetchMetrics()">
                        <i class="fas fa-sync-alt me-1"></i>Update Metrics
                    </button>
                    <form action="{{ url_for('google_oauth.google_disconnect') }}" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to disconnect your Google account?');">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                </div>
            {% else %}
                <a href="{{ url_for('google_oauth.google_auth') }}" class="btn btn-primary me-2">
                    <i class="fab fa-google me-2"></i>Connect to Google
                </a>
            {% endif %}
            <button class="btn btn-outline-primary" id="export-csv">
                <i class="fas fa-download me-2"></i>Export CSV
            </button>
            <button class="btn btn-outline-secondary" id="copy-all-urls">
                <i class="fas fa-copy me-2"></i>Copy All URLs
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Track Your SEO Performance:</strong> This page shows all URLs that have been optimized by the AI system. 
        Use these URLs to set up tracking in Google Search Console and monitor their performance over time.
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card products">
                <div class="card-body">
                    <h5 class="card-title text-muted">Optimized Products</h5>
                    <h2 class="mb-0">{{ urls|selectattr('type', 'equalto', 'Product')|selectattr('is_translation', 'equalto', false)|list|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card collections">
                <div class="card-body">
                    <h5 class="card-title text-muted">Created Collections</h5>
                    <h2 class="mb-0">{{ urls|selectattr('type', 'equalto', 'Collection')|selectattr('is_translation', 'equalto', false)|list|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card">
                <div class="card-body">
                    <h5 class="card-title text-muted">Translated Pages</h5>
                    <h2 class="mb-0">{{ urls|selectattr('is_translation', 'equalto', true)|list|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card total">
                <div class="card-body">
                    <h5 class="card-title text-muted">Total URLs</h5>
                    <h2 class="mb-0">{{ total_urls }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6 mb-3 mb-md-0">
                    <div class="input-group search-box">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="search-input" placeholder="Search URLs, titles, or handles...">
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="btn-group" role="group" aria-label="Filter by type">
                        <button type="button" class="btn btn-outline-secondary filter-btn active" data-filter="all">All</button>
                        <button type="button" class="btn btn-outline-secondary filter-btn" data-filter="Product">Products</button>
                        <button type="button" class="btn btn-outline-secondary filter-btn" data-filter="Collection">Collections</button>
                        <button type="button" class="btn btn-outline-secondary filter-btn" data-filter="translation">Translations</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- URLs Table -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover search-console-table mb-0">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Title</th>
                            <th>Handle/Slug</th>
                            <th>URL Path</th>
                            <th>Languages</th>
                            <th>Performance</th>
                            <th>Date Optimized</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="urls-tbody">
                        {% for url in urls %}
                        <tr class="{% if url.is_translation %}translation-row{% endif %}" 
                            data-type="{{ url.type }}" 
                            data-is-translation="{{ url.is_translation|lower }}"
                            data-searchable="{{ url.title|lower }} {{ url.handle|lower }} {{ url.url|lower }}">
                            <td>
                                <span class="badge type-badge {% if url.type == 'Product' %}bg-success{% else %}bg-info{% endif %}">
                                    {{ url.type }}
                                </span>
                                {% if url.is_translation %}
                                    <span class="translation-indicator text-muted d-block">
                                        <i class="fas fa-language"></i> Translation
                                    </span>
                                {% endif %}
                            </td>
                            <td>{{ url.title }}</td>
                            <td>
                                <span class="url-handle">{{ url.handle }}</span>
                                <i class="fas fa-copy copy-btn ms-2" data-clipboard-text="{{ url.handle }}" title="Copy handle"></i>
                            </td>
                            <td>
                                <code>{{ url.url }}</code>
                                <i class="fas fa-copy copy-btn ms-2" data-clipboard-text="{{ url.url }}" title="Copy URL path"></i>
                            </td>
                            <td>
                                {% if url.languages %}
                                    {% for lang in url.languages %}
                                        <span class="badge bg-secondary language-badge">{{ lang|upper }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if url.metrics.has_data %}
                                    <div class="performance-metrics">
                                        <div class="d-flex align-items-center mb-1">
                                            <span class="text-primary fw-bold me-2">{{ url.metrics.clicks }}</span>
                                            <small class="text-muted">clicks</small>
                                            {% if url.metrics.clicks_change > 0 %}
                                                <span class="badge bg-success ms-2">+{{ url.metrics.clicks_change }}</span>
                                            {% elif url.metrics.clicks_change < 0 %}
                                                <span class="badge bg-danger ms-2">{{ url.metrics.clicks_change }}</span>
                                            {% endif %}
                                        </div>
                                        <div class="d-flex align-items-center mb-1">
                                            <span class="text-info fw-bold me-2">{{ url.metrics.impressions }}</span>
                                            <small class="text-muted">impressions</small>
                                            {% if url.metrics.impressions_change > 0 %}
                                                <span class="badge bg-success ms-2">+{{ url.metrics.impressions_change }}</span>
                                            {% elif url.metrics.impressions_change < 0 %}
                                                <span class="badge bg-danger ms-2">{{ url.metrics.impressions_change }}</span>
                                            {% endif %}
                                        </div>
                                        <div class="small text-muted">
                                            CTR: {{ url.metrics.ctr }}% | Pos: {{ url.metrics.position }}
                                        </div>
                                    </div>
                                {% else %}
                                    <span class="text-muted small">No data yet</span>
                                {% endif %}
                            </td>
                            <td>{{ url.date_optimized.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if url.full_url %}
                                <a href="{{ url.full_url }}" target="_blank" class="btn btn-sm btn-outline-primary" title="View on store">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% if not urls %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No optimized URLs yet. Start by optimizing products or creating collections.</p>
                    <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-primary mt-2">
                        <i class="fas fa-box me-2"></i>Optimize Products
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>How to Use This Data</h5>
        </div>
        <div class="card-body">
            <ol>
                <li><strong>Set up Google Search Console:</strong> Add and verify your Shopify store domain.</li>
                <li><strong>Submit Sitemap:</strong> Submit your sitemap URL: <code>https://{{ shop_domain }}/sitemap.xml</code></li>
                <li><strong>Monitor Performance:</strong> Track clicks, impressions, and rankings for these optimized URLs.</li>
                <li><strong>Request Indexing:</strong> Use the URL Inspection tool to request indexing for new or updated pages.</li>
                <li><strong>Track International Performance:</strong> Use the performance report to see how translated pages perform in different regions.</li>
            </ol>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    const tbody = document.getElementById('urls-tbody');
    const rows = tbody.getElementsByTagName('tr');
    
    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        
        for (let row of rows) {
            const searchable = row.getAttribute('data-searchable');
            if (searchable.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
    
    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active state
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const filter = this.getAttribute('data-filter');
            
            for (let row of rows) {
                const rowType = row.getAttribute('data-type');
                const isTranslation = row.getAttribute('data-is-translation') === 'true';
                
                if (filter === 'all') {
                    row.style.display = '';
                } else if (filter === 'translation' && isTranslation) {
                    row.style.display = '';
                } else if (filter !== 'translation' && rowType === filter && !isTranslation) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });
    });
    
    // Copy functionality
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const textToCopy = this.getAttribute('data-clipboard-text');
            navigator.clipboard.writeText(textToCopy).then(() => {
                // Show feedback
                const originalClass = this.className;
                this.className = 'fas fa-check text-success ms-2';
                setTimeout(() => {
                    this.className = originalClass;
                }, 1000);
            });
        });
    });
    
    // Copy all URLs
    document.getElementById('copy-all-urls').addEventListener('click', function() {
        const urls = [];
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
        
        visibleRows.forEach(row => {
            const cells = row.getElementsByTagName('td');
            if (cells.length > 3) {
                const url = cells[3].querySelector('code').textContent;
                urls.push('https://{{ shop_domain }}' + url);
            }
        });
        
        navigator.clipboard.writeText(urls.join('\n')).then(() => {
            this.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-copy me-2"></i>Copy All URLs';
            }, 2000);
        });
    });
    
    // Export CSV
    document.getElementById('export-csv').addEventListener('click', function() {
        const csvContent = [];
        csvContent.push(['Type', 'Title', 'Handle', 'URL', 'Full URL', 'Languages', 'Date Optimized']);
        
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
        
        visibleRows.forEach(row => {
            const cells = row.getElementsByTagName('td');
            if (cells.length > 0) {
                const type = cells[0].textContent.trim();
                const title = cells[1].textContent.trim();
                const handle = cells[2].querySelector('.url-handle').textContent.trim();
                const url = cells[3].querySelector('code').textContent.trim();
                const fullUrl = 'https://{{ shop_domain }}' + url;
                const languages = Array.from(cells[4].querySelectorAll('.language-badge'))
                    .map(badge => badge.textContent.trim()).join(', ') || 'None';
                const date = cells[5].textContent.trim();
                
                csvContent.push([type, title, handle, url, fullUrl, languages, date]);
            }
        });
        
        // Convert to CSV string
        const csv = csvContent.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
        
        // Download
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'search_console_urls_' + new Date().toISOString().split('T')[0] + '.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    });
});

// Fetch metrics function
function fetchMetrics() {
    const button = document.querySelector('button[onclick="fetchMetrics()"]');
    const originalHtml = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    
    fetch('{{ url_for("ai_modules.update_search_console_metrics") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            button.innerHTML = '<i class="fas fa-check me-1"></i>Update Started';
            
            // Check task status periodically
            if (data.task_id) {
                checkTaskStatus(data.task_id);
            }
            
            // Reset button after a few seconds
            setTimeout(() => {
                button.innerHTML = originalHtml;
                button.disabled = false;
            }, 3000);
        } else {
            // Show error
            alert(data.error || 'Failed to update metrics');
            button.innerHTML = originalHtml;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to update metrics');
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

// Check task status
function checkTaskStatus(taskId) {
    const checkInterval = setInterval(() => {
        fetch(`{{ url_for("ai_modules.get_task_status", task_id="") }}${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.state === 'SUCCESS') {
                    clearInterval(checkInterval);
                    // Reload the page to show updated metrics
                    window.location.reload();
                } else if (data.state === 'FAILURE') {
                    clearInterval(checkInterval);
                    console.error('Task failed:', data.info);
                }
            })
            .catch(error => {
                console.error('Error checking task status:', error);
                clearInterval(checkInterval);
            });
    }, 5000); // Check every 5 seconds
    
    // Stop checking after 5 minutes
    setTimeout(() => clearInterval(checkInterval), 300000);
}
</script>
{% endblock %}