"""Add access control tables

Revision ID: 8c660d7ca5ec
Revises: c0ba407f5fad
Create Date: 2025-05-28 13:09:03.617944

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8c660d7ca5ec'
down_revision = 'c0ba407f5fad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('access_keys',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.<PERSON>an(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('access_keys', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_access_keys_code'), ['code'], unique=True)

    op.create_table('user_settings',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('access_key_id', sa.String(length=36), nullable=False),
    sa.Column('shopify_api_key', sa.String(length=255), nullable=True),
    sa.Column('shopify_api_secret', sa.String(length=255), nullable=True),
    sa.Column('shopify_access_token', sa.String(length=255), nullable=True),
    sa.Column('shopify_shop_domain', sa.String(length=255), nullable=True),
    sa.Column('anthropic_api_key', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['access_key_id'], ['access_keys.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('access_key_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_settings')
    with op.batch_alter_table('access_keys', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_access_keys_code'))

    op.drop_table('access_keys')
    # ### end Alembic commands ###
