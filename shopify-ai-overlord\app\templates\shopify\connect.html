{% extends "base.html" %}

{% block title %}Connect Your Store - Shopify AI Control{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Connect Your Shopify Store</h2>
                
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Custom App Setup:</strong> You need to create a custom app in your Shopify admin first.
                </div>
                
                <form action="{{ url_for('shopify.connect') }}" method="POST">
                    <div class="mb-3">
                        <label for="shop_domain" class="form-label">Store Domain</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shop_domain" name="shop_domain" 
                                   placeholder="your-store" required>
                            <span class="input-group-text">.myshopify.com</span>
                        </div>
                        <div class="form-text">Enter your Shopify store domain without the .myshopify.com part</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="access_token" class="form-label">Admin API Access Token</label>
                        <input type="password" class="form-control" id="access_token" name="access_token" 
                               placeholder="shpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx" required>
                        <div class="form-text">Your custom app's Admin API access token from Shopify admin</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="anthropic_api_key" class="form-label">Anthropic API Key</label>
                        <input type="password" class="form-control" id="anthropic_api_key" name="anthropic_api_key" 
                               placeholder="sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx" required>
                        <div class="form-text">Your Anthropic API key for Claude AI optimization</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="store_actual_live_url" class="form-label">Store Live URL (Optional)</label>
                        <input type="url" class="form-control" id="store_actual_live_url" name="store_actual_live_url" 
                               placeholder="https://www.yourdomain.com">
                        <div class="form-text">Your store's actual domain (if different from .myshopify.com). Used for Search Console integration.</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plug me-2"></i> Connect Store
                    </button>
                </form>
                
                <hr class="my-4">
                
                <h5>How to get your access token:</h5>
                <ol class="small">
                    <li>Go to your Shopify admin</li>
                    <li>Navigate to <strong>Settings → Apps and sales channels</strong></li>
                    <li>Click <strong>Develop apps</strong></li>
                    <li>Click <strong>Create an app</strong></li>
                    <li>Give your app a name (e.g., "AI Control")</li>
                    <li>Configure Admin API scopes:
                        <ul>
                            <li>read_products, write_products</li>
                            <li>read_orders, write_orders</li>
                            <li>read_customers, write_customers</li>
                            <li>read_translations, write_translations</li>
                            <li>write_online_store_navigation (for URL redirects)</li>
                        </ul>
                    </li>
                    <li>Install the app to your store</li>
                    <li>Copy the Admin API access token</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}