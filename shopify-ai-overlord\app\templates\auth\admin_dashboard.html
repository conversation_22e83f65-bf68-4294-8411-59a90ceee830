{% extends "base.html" %}

{% block title %}Admin Dashboard - Shopify AI Control{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-tachometer-alt text-primary"></i> Admin Dashboard
            </h1>
            <div>
                <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h5 class="card-title">Total Users</h5>
                <p class="h4">{{ access_keys|length }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-key fa-2x text-success mb-2"></i>
                <h5 class="card-title">Active Keys</h5>
                <p class="h4">{{ access_keys|selectattr('is_active', 'equalto', True)|list|length }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                <h5 class="card-title">Inactive Keys</h5>
                <p class="h4">{{ access_keys|selectattr('is_active', 'equalto', False)|list|length }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-cog fa-2x text-info mb-2"></i>
                <h5 class="card-title">Configured</h5>
                <p class="h4">{{ access_keys|selectattr('user_settings')|list|length }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-key"></i> Access Keys Management
                </h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addKeyModal">
                    <i class="fas fa-plus"></i> Add New Key
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Access Code</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key in access_keys %}
                            <tr>
                                <td>
                                    <code>{{ key.code[:8] }}...</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ key.code }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                                <td>{{ key.email or '-' }}</td>
                                <td>
                                    {% if key.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Expired</span>
                                    {% endif %}
                                </td>
                                <td>{{ key.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('auth.toggle_access_key', key_id=key.id) }}" 
                                       class="btn btn-sm {% if key.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %}">
                                        <i class="fas {% if key.is_active %}fa-pause{% else %}fa-play{% endif %}"></i>
                                        {% if key.is_active %}Disable{% else %}Enable{% endif %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Key Modal -->
<div class="modal fade" id="addKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Access Key</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('auth.create_access_key') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" name="email" placeholder="<EMAIL>" required>
                        <div class="form-text">Enter the email address of the user who will receive this access key.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Key</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show temporary success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed bottom-0 end-0 m-3';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">Access code copied!</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        setTimeout(() => toast.remove(), 3000);
    });
}

function editKey(keyId) {
    // Implement edit functionality
    console.log('Edit key:', keyId);
}

function deleteKey(keyId) {
    if (confirm('Are you sure you want to delete this access key?')) {
        fetch(`/auth/admin/keys/${keyId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}
</script>
{% endblock %}