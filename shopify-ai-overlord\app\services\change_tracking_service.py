from datetime import datetime
from app import db
from app.models.change_log import ChangeLog, OptimizationStatus, HomepageSEO
import json

class ChangeTrackingService:
    """Service to track all changes and optimization status"""
    
    @staticmethod
    def log_change(shop_id, resource_type, resource_id, action, before_data=None, after_data=None, metadata=None):
        """Log a change to the database"""
        try:
            change_log = ChangeLog(
                shop_id=shop_id,
                resource_type=resource_type,
                resource_id=str(resource_id),
                action=action,
                before_data=before_data or {},
                after_data=after_data or {},
                change_metadata=metadata or {}
            )
            db.session.add(change_log)
            db.session.commit()
            
            # Force refresh to ensure data is persisted
            db.session.refresh(change_log)
            print(f"[DEBUG] ChangeLog saved - ID: {change_log.id}, metadata: {change_log.change_metadata}")
            
            return change_log
        except Exception as e:
            db.session.rollback()
            print(f"Error logging change: {e}")
            return None
    
    @staticmethod
    def mark_as_optimized(shop_id, resource_type, resource_id, optimization_type='seo', 
                         current_data=None, optimized_by='claude-sonnet-4'):
        """Mark a resource as optimized"""
        try:
            # Check if status already exists
            status = OptimizationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type=resource_type,
                resource_id=str(resource_id),
                optimization_type=optimization_type
            ).first()
            
            if status:
                # Update existing
                status.is_optimized = True
                status.optimized_at = datetime.utcnow()
                status.optimized_by = optimized_by
                status.current_data = current_data or {}
            else:
                # Create new
                status = OptimizationStatus(
                    shop_id=shop_id,
                    resource_type=resource_type,
                    resource_id=str(resource_id),
                    optimization_type=optimization_type,
                    is_optimized=True,
                    optimized_at=datetime.utcnow(),
                    optimized_by=optimized_by,
                    current_data=current_data or {}
                )
                db.session.add(status)
            
            db.session.commit()
            return status
        except Exception as e:
            db.session.rollback()
            print(f"Error marking as optimized: {e}")
            return None
    
    @staticmethod
    def get_all_optimized_product_ids(shop_id, optimization_type='seo'):
        """Get all product IDs that have been optimized"""
        try:
            optimized = OptimizationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type='product',
                optimization_type=optimization_type,
                is_optimized=True
            ).all()
            optimized_ids = set(str(status.resource_id) for status in optimized)
            # Debug: print(f"Found {len(optimized_ids)} optimized products for shop {shop_id}")
            return optimized_ids
        except Exception as e:
            print(f"Error getting optimized product IDs: {e}")
            return set()
    
    @staticmethod
    def mark_as_translated(shop_id, resource_type, resource_id, language, translated_fields, translated_by='claude-sonnet-4', translated_data=None):
        """Mark a resource as translated for a specific language"""
        try:
            from app.models.change_log import TranslationStatus
            
            # Check if status already exists
            status = TranslationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type=resource_type,
                resource_id=str(resource_id),
                language=language
            ).first()
            
            if status:
                # Update existing
                status.is_translated = True
                status.translated_at = datetime.utcnow()
                status.translated_by = translated_by
                status.translated_fields = translated_fields
                if translated_data:
                    status.translated_data = translated_data
            else:
                # Create new
                status = TranslationStatus(
                    shop_id=shop_id,
                    resource_type=resource_type,
                    resource_id=str(resource_id),
                    language=language,
                    is_translated=True,
                    translated_at=datetime.utcnow(),
                    translated_by=translated_by,
                    translated_fields=translated_fields,
                    translated_data=translated_data or {}
                )
                db.session.add(status)
            
            db.session.commit()
            return status
        except Exception as e:
            db.session.rollback()
            print(f"Error marking as translated: {e}")
            return None
    
    @staticmethod
    def get_translation_status(shop_id, resource_type, resource_id, language):
        """Get translation status for a resource and language"""
        try:
            from app.models.change_log import TranslationStatus
            
            status = TranslationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type=resource_type,
                resource_id=str(resource_id),
                language=language
            ).first()
            return status
        except Exception as e:
            print(f"Error getting translation status: {e}")
            return None
    
    @staticmethod
    def get_collections_created_from_tags(shop_id):
        """Get all tags that have been used to create collections"""
        try:
            # Get all collection creation changes
            collection_changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='collection',
                action='create'
            ).all()
            
            # Extract tags from metadata
            tags_used = set()
            for change in collection_changes:
                if change.change_metadata and change.change_metadata.get('created_from_tag'):
                    tags_used.add(change.change_metadata.get('created_from_tag'))
            
            return tags_used
        except Exception as e:
            print(f"Error getting collection tags: {e}")
            return set()
    
    @staticmethod
    def get_all_translated_resource_ids(shop_id, language, resource_type=None):
        """Get all resource IDs that have been translated for a specific language"""
        try:
            from app.models.change_log import TranslationStatus
            
            query = TranslationStatus.query.filter_by(
                shop_id=shop_id,
                language=language,
                is_translated=True
            )
            
            if resource_type:
                query = query.filter_by(resource_type=resource_type)
            
            translated = query.all()
            
            # Group by resource type if no specific type requested
            if not resource_type:
                result = {
                    'product': set(),
                    'collection': set(),
                    'page': set()
                }
                for status in translated:
                    if status.resource_type in result:
                        result[status.resource_type].add(str(status.resource_id))
                return result
            else:
                return set(str(status.resource_id) for status in translated)
                
        except Exception as e:
            print(f"Error getting translated resource IDs: {e}")
            return set() if resource_type else {'product': set(), 'collection': set(), 'page': set()}
    
    @staticmethod
    def get_optimization_status(shop_id, resource_type, resource_id, optimization_type='seo'):
        """Get optimization status for a resource"""
        try:
            # Check if the table exists first
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            if 'optimization_status' not in inspector.get_table_names():
                return None
                
            status = OptimizationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type=resource_type,
                resource_id=str(resource_id),
                optimization_type=optimization_type
            ).first()
            return status
        except Exception as e:
            print(f"Error getting optimization status: {e}")
            return None
    
    @staticmethod
    def get_all_optimization_statuses(shop_id, resource_type=None, optimization_type='seo'):
        """Get all optimization statuses for a shop"""
        try:
            # Check if the table exists first
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            if 'optimization_status' not in inspector.get_table_names():
                print("OptimizationStatus table doesn't exist yet. Run migrations.")
                return []
            
            query = OptimizationStatus.query.filter_by(
                shop_id=shop_id,
                optimization_type=optimization_type
            )
            
            if resource_type:
                query = query.filter_by(resource_type=resource_type)
            
            return query.all()
        except Exception as e:
            print(f"Error getting optimization statuses: {e}")
            return []
    
    @staticmethod
    def get_change_history(shop_id, resource_type=None, resource_id=None, limit=50):
        """Get change history for a shop or specific resource"""
        try:
            # Check if the table exists first
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            if 'change_logs' not in inspector.get_table_names():
                print("ChangeLog table doesn't exist yet. Run migrations.")
                return []
            
            query = ChangeLog.query.filter_by(shop_id=shop_id)
            
            if resource_type:
                query = query.filter_by(resource_type=resource_type)
            
            if resource_id:
                query = query.filter_by(resource_id=str(resource_id))
            
            result = query.order_by(ChangeLog.created_at.desc()).limit(limit).all()
            # Changes retrieved successfully
            return result
        except Exception as e:
            print(f"Error getting change history: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    @staticmethod
    def revert_change(change_id, shop_id):
        """Revert a specific change"""
        try:
            change = ChangeLog.query.filter_by(id=change_id, shop_id=shop_id).first()
            if not change or change.status == 'reverted':
                return None
            
            # Mark the change as reverted
            change.status = 'reverted'
            change.reverted_at = datetime.utcnow()
            change.reverted_by = shop_id
            
            # Update optimization status if it was an optimization
            if change.action in ['optimize_seo', 'optimize_product']:
                status = OptimizationStatus.query.filter_by(
                    shop_id=shop_id,
                    resource_type=change.resource_type,
                    resource_id=change.resource_id
                ).first()
                if status:
                    status.is_optimized = False
            
            db.session.commit()
            return change
        except Exception as e:
            db.session.rollback()
            print(f"Error reverting change: {e}")
            return None
    
    @staticmethod
    def save_homepage_seo(shop_id, title=None, meta_description=None, keywords=None, 
                         analysis_data=None, competitor_urls=None):
        """Save homepage SEO settings"""
        try:
            homepage_seo = HomepageSEO.query.filter_by(shop_id=shop_id).first()
            
            if homepage_seo:
                # Update existing
                if title is not None:
                    homepage_seo.title = title
                if meta_description is not None:
                    homepage_seo.meta_description = meta_description
                if keywords is not None:
                    homepage_seo.keywords = keywords
                if analysis_data is not None:
                    homepage_seo.analysis_data = analysis_data
                if competitor_urls is not None:
                    homepage_seo.competitor_urls = competitor_urls
            else:
                # Create new
                homepage_seo = HomepageSEO(
                    shop_id=shop_id,
                    title=title,
                    meta_description=meta_description,
                    keywords=keywords,
                    analysis_data=analysis_data or {},
                    competitor_urls=competitor_urls or []
                )
                db.session.add(homepage_seo)
            
            db.session.commit()
            return homepage_seo
        except Exception as e:
            db.session.rollback()
            print(f"Error saving homepage SEO: {e}")
            return None
    
    @staticmethod
    def get_homepage_seo(shop_id):
        """Get homepage SEO settings"""
        try:
            return HomepageSEO.query.filter_by(shop_id=shop_id).first()
        except Exception as e:
            print(f"Error getting homepage SEO: {e}")
            return None
    
    @staticmethod
    def reset_all_changes(shop_id, confirmation_text, shopify_service=None):
        """Reset all changes if confirmation matches"""
        if confirmation_text != "RESET ALL":
            return False, "Confirmation text doesn't match. Please type 'RESET ALL'"
        
        try:
            # Cancel all pending Celery tasks
            from app.models.automation_job import AutomationJob
            pending_jobs = AutomationJob.query.filter_by(
                shop_id=shop_id,
                status='processing'
            ).all()
            
            for job in pending_jobs:
                if job.task_id:
                    try:
                        from celery_app import celery_app
                        celery_app.control.revoke(job.task_id, terminate=True)
                    except:
                        pass
                job.status = 'cancelled'
            
            # Get all created collections to delete
            collection_changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='collection',
                action='create',
                status='completed'
            ).all()
            
            deleted_collections = 0
            collection_errors = []
            
            # Delete collections from Shopify
            if shopify_service:
                for change in collection_changes:
                    try:
                        collection_id = change.resource_id
                        # Try to delete as smart collection first
                        try:
                            if shopify_service.delete_smart_collection(collection_id):
                                deleted_collections += 1
                            else:
                                # Try as custom collection
                                if shopify_service.delete_custom_collection(collection_id):
                                    deleted_collections += 1
                        except:
                            # Try as custom collection if smart fails
                            if shopify_service.delete_custom_collection(collection_id):
                                deleted_collections += 1
                    except Exception as e:
                        collection_errors.append(f"Collection {collection_id}: {str(e)}")
            
            # Get all product changes to revert
            product_changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='product',
                action='optimize_seo',
                status='completed'
            ).all()
            
            reverted_products = 0
            product_errors = []
            
            # Revert products in Shopify
            if shopify_service:
                for change in product_changes:
                    try:
                        if change.before_data:
                            # Revert product to original state
                            success = shopify_service.update_product(
                                change.resource_id,
                                change.before_data
                            )
                            if success:
                                reverted_products += 1
                            else:
                                product_errors.append(f"Failed to revert product {change.resource_id}")
                    except Exception as e:
                        product_errors.append(f"Product {change.resource_id}: {str(e)}")
            
            # Clear all database records
            # Delete all change logs
            change_logs_deleted = ChangeLog.query.filter_by(shop_id=shop_id).delete()
            print(f"Deleted {change_logs_deleted} change logs")
            
            # Reset all optimization statuses
            optimization_deleted = OptimizationStatus.query.filter_by(shop_id=shop_id).delete()
            print(f"Deleted {optimization_deleted} optimization statuses")
            
            # Clear translation statuses if table exists
            try:
                from app.models.change_log import TranslationStatus
                translation_deleted = TranslationStatus.query.filter_by(shop_id=shop_id).delete()
                print(f"Deleted {translation_deleted} translation statuses")
            except Exception as e:
                print(f"Translation status deletion skipped: {e}")
            
            # Clear homepage SEO
            homepage_deleted = HomepageSEO.query.filter_by(shop_id=shop_id).delete()
            print(f"Deleted {homepage_deleted} homepage SEO records")
            
            # Clear automation jobs
            jobs_deleted = AutomationJob.query.filter_by(shop_id=shop_id).delete()
            print(f"Deleted {jobs_deleted} automation jobs")
            
            # Clear session tags
            from flask import session
            keys_to_remove = [key for key in session.keys() if key.startswith('new_tags_')]
            for key in keys_to_remove:
                session.pop(key, None)
            
            db.session.commit()
            
            # Build result message
            results = []
            results.append(f"✓ Cancelled {len(pending_jobs)} pending tasks")
            results.append(f"✓ Deleted {deleted_collections} collections from Shopify")
            results.append(f"✓ Reverted {reverted_products} products in Shopify")
            results.append(f"✓ Cleared all optimization records")
            results.append(f"✓ Reset all tracking data")
            
            if collection_errors:
                results.append(f"⚠ Collection errors: {len(collection_errors)}")
            if product_errors:
                results.append(f"⚠ Product errors: {len(product_errors)}")
            
            return True, "\n".join(results)
            
        except Exception as e:
            db.session.rollback()
            print(f"Error resetting changes: {e}")
            import traceback
            traceback.print_exc()
            return False, f"Reset failed: {str(e)}"
    
    @staticmethod
    def get_new_tags_for_collections(shop_id, limit=None):
        """Get new tags that don't have collections yet"""
        try:
            # Get all tags that were added during optimization
            # Make sure to only get completed changes
            changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='product',
                action='optimize_seo'
            ).filter(
                ChangeLog.status.in_(['completed', None])  # Include None for backward compatibility
            ).all()
            
            print(f"[DEBUG] Found {len(changes)} product optimization changes for shop {shop_id}")
            
            # Extract unique tags from metadata
            all_tags = set()
            for change in changes:
                if change.change_metadata and 'new_tags' in change.change_metadata:
                    print(f"[DEBUG] Product {change.resource_id} has new_tags: {change.change_metadata['new_tags']}")
                    for tag in change.change_metadata['new_tags']:
                        if tag and tag.strip():
                            all_tags.add(tag.strip())
                else:
                    print(f"[DEBUG] Product {change.resource_id} has no new_tags in metadata")
            
            print(f"[DEBUG] Total unique tags found: {len(all_tags)}")
            
            # Get tags that already have collections
            existing_collection_tags = ChangeTrackingService.get_collections_created_from_tags(shop_id)
            print(f"[DEBUG] Tags that already have collections: {existing_collection_tags}")
            
            # Filter out tags that already have collections
            new_tags = []
            for tag in all_tags:
                if tag not in existing_collection_tags:
                    # Count products with this tag
                    count = 0
                    for change in changes:
                        if change.change_metadata and 'new_tags' in change.change_metadata:
                            if tag in change.change_metadata['new_tags']:
                                count += 1
                    
                    if count > 0:  # Only include tags that have products
                        new_tags.append({'tag': tag, 'count': count})
            
            # Sort by count descending
            new_tags.sort(key=lambda x: x['count'], reverse=True)
            
            # Apply limit if specified
            if limit:
                new_tags = new_tags[:limit]
            
            return new_tags
            
        except Exception as e:
            print(f"Error getting new tags for collections: {e}")
            return []