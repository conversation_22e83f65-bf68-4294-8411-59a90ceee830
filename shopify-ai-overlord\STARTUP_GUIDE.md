# Shopify AI Control - Startup Guide

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Up Environment Variables
Create a `.env` file with:
```
FLASK_SECRET_KEY=your-secret-key-here
REDIS_URL=redis://localhost:6379/0
```

### 3. Initialize Database
```bash
python init_db.py
```

### 4. Start All Services (Recommended)
```bash
python run.py --all
```

This will automatically:
- Start Redis (if not running)
- Start Celery worker and beat scheduler
- Start the Flask application
- All in one command!

## Alternative Startup Options

### Start Services Separately

#### Option 1: Background Services Only
```bash
python run.py --services
```
This starts Redis and Celery but not Flask.

#### Option 2: Flask Only
```bash
python run.py
```
This starts only the Flask app (assumes Redis/Celery are already running).

#### Option 3: Manual Start
```bash
# Terminal 1 - Start Redis
python start_redis.py

# Terminal 2 - Start Celery
python start_celery.py

# Terminal 3 - Start Flask
python run.py
```

## Access the Application

1. Open http://localhost:5000
2. Use access code: `admin` / `admin`
3. Configure your Shopify credentials in Settings
4. Enable automation from the Dashboard

## Automation Features

### Dashboard
The new automation dashboard shows:
- Real-time progress of SEO optimization
- Collection creation status
- Translation progress
- Daily limits and API usage

### Background Tasks
All heavy operations now run in the background:
- Product SEO optimization
- Collection creation from tags
- Multi-language translation
- Scheduled daily automation

### Settings
Configure automation settings from the dashboard:
- Daily optimization limits
- Target languages for translation
- SEO keywords
- Schedule times

## Troubleshooting

### Redis Connection Issues
- Windows: The script will try WSL or Docker automatically
- Linux/Mac: Install Redis with your package manager
- Check if Redis is running: `redis-cli ping`

### Celery Not Starting
- Make sure Redis is running first
- Check for port conflicts (default: 6379)
- Try restarting with `python run.py --all`

### Database Issues
- Run `python init_db.py` to reinitialize
- Check `instance/` folder permissions
- Delete `.db` file to start fresh

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Flask Web     │────▶│     Redis       │◀────│  Celery Worker  │
│   Application   │     │   Message Queue │     │  (Background)   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                                                │
         │                                                │
         └──────────────── SQLite DB ────────────────────┘
```

## Help

For more options:
```bash
python run.py --help
```

## Notes

- All automation runs in the background using Celery
- Progress is tracked in real-time
- The UI now shows status instead of performing actions
- Homepage SEO tool moved to "Manual Tools" section at bottom of dashboard