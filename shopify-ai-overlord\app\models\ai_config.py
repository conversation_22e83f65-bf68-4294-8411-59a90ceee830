from datetime import datetime
from app import db

class AIConfig(db.Model):
    __tablename__ = 'ai_configs'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    shop_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('shops.id'), nullable=False)
    module_type = db.Column(db.String(50), nullable=False)  # seo, ads, email, support
    config_data = db.Column(db.JSON, default={})
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('shop_id', 'module_type', name='_shop_module_uc'),)