from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON>ginManager
from config import Config

db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    
    # Import models
    from app.models import shop, ai_config, change_log, access_control, automation_job, google_oauth
    
    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(shop_id):
        from app.models.shop import Shop
        return Shop.query.get(int(shop_id))
    
    # Register blueprints
    from app.routes.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.routes.auth import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.routes.shopify import bp as shopify_bp
    app.register_blueprint(shopify_bp, url_prefix='/shopify')
    
    from app.routes.ai_modules import bp as ai_bp
    app.register_blueprint(ai_bp, url_prefix='/ai')
    
    from app.routes.google_oauth import bp as google_oauth_bp
    app.register_blueprint(google_oauth_bp)
    
    # Register custom template filters
    from datetime import datetime, timezone
    import json
    
    @app.template_filter('from_json')
    def from_json_filter(json_str):
        """Parse JSON string into Python object"""
        if not json_str:
            return {}
        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    @app.template_filter('timeago')
    def timeago_filter(dt):
        """Convert datetime to human-readable time ago format"""
        if not dt:
            return ''
        
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt)
            except:
                return dt
        
        # Handle timezone-aware/naive datetime compatibility
        if dt.tzinfo is not None:
            # dt is timezone-aware, use timezone-aware now
            now = datetime.now(timezone.utc)
        else:
            # dt is timezone-naive, use timezone-naive now
            now = datetime.utcnow()
        
        diff = now - dt
        
        seconds = diff.total_seconds()
        
        if seconds < 60:
            return 'just now'
        elif seconds < 3600:
            minutes = int(seconds / 60)
            return f'{minutes} minute{"s" if minutes != 1 else ""} ago'
        elif seconds < 86400:
            hours = int(seconds / 3600)
            return f'{hours} hour{"s" if hours != 1 else ""} ago'
        elif seconds < 604800:
            days = int(seconds / 86400)
            return f'{days} day{"s" if days != 1 else ""} ago'
        else:
            weeks = int(seconds / 604800)
            return f'{weeks} week{"s" if weeks != 1 else ""} ago'
    
    return app