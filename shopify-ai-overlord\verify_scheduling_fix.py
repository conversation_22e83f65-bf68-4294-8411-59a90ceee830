#!/usr/bin/env python3
"""
Verify Scheduling System Fix

This script verifies that the 20-minute interval scheduling system
is working correctly and identifies any remaining issues.
"""

import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.access_control import UserShop
from app.models.ai_config import AIConfig
from celerybeat_schedule import get_dynamic_schedule
import time

def verify_scheduling_system():
    """Comprehensive verification of the scheduling system"""
    print(f"\n{'='*80}")
    print("SCHEDULING SYSTEM VERIFICATION")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*80}\n")
    
    app = create_app()
    issues = []
    
    with app.app_context():
        # Test 1: Check UserShop model methods
        print("1. Testing UserShop model methods...")
        try:
            # Test assign_next_automation_slot
            hour, minute = UserShop.assign_next_automation_slot()
            print(f"   ✓ assign_next_automation_slot() works: {hour:02d}:{minute:02d}")
            
            # Test redistribute_all_schedules
            count = UserShop.redistribute_all_schedules()
            print(f"   ✓ redistribute_all_schedules() works: {count} shops")
            
        except Exception as e:
            issues.append(f"UserShop model methods: {e}")
            print(f"   ✗ UserShop model methods failed: {e}")
        
        # Test 2: Check schedule generation
        print("\n2. Testing dynamic schedule generation...")
        try:
            schedule = get_dynamic_schedule()
            automation_tasks = {k: v for k, v in schedule.items() if 'automation' in k}
            
            print(f"   ✓ Dynamic schedule generated: {len(schedule)} total tasks")
            print(f"   ✓ Automation tasks: {len(automation_tasks)}")
            
            if len(automation_tasks) == 0:
                issues.append("No automation tasks in schedule - check shop configurations")
            
        except Exception as e:
            issues.append(f"Schedule generation: {e}")
            print(f"   ✗ Schedule generation failed: {e}")
        
        # Test 3: Check shop configurations
        print("\n3. Checking shop configurations...")
        all_shops = UserShop.query.all()
        enabled_shops = UserShop.query.filter_by(automation_enabled=True).all()
        
        shops_with_features = 0
        for shop in enabled_shops:
            config = AIConfig.query.filter_by(
                shop_id=shop.shop_id,
                module_type='seo_automation'
            ).first()
            
            if config and config.config_data:
                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])
                
                if has_enabled_automation:
                    shops_with_features += 1
        
        print(f"   ✓ Total shops: {len(all_shops)}")
        print(f"   ✓ Automation enabled: {len(enabled_shops)}")
        print(f"   ✓ With enabled features: {shops_with_features}")
        
        if shops_with_features == 0:
            issues.append("No shops have enabled automation features")
        
        # Test 4: Check interval distribution
        print("\n4. Checking interval distribution...")
        scheduled_shops = UserShop.query.filter_by(automation_enabled=True).order_by(
            UserShop.automation_hour, 
            UserShop.automation_minute
        ).all()
        
        if scheduled_shops:
            intervals = []
            prev_minutes = None
            
            for shop in scheduled_shops:
                current_minutes = shop.automation_hour * 60 + shop.automation_minute
                if prev_minutes is not None:
                    interval = current_minutes - prev_minutes
                    intervals.append(interval)
                prev_minutes = current_minutes
            
            if intervals:
                min_interval = min(intervals)
                avg_interval = sum(intervals) / len(intervals)
                conflicts = [i for i in intervals if i < 20]
                
                print(f"   ✓ Minimum interval: {min_interval} minutes")
                print(f"   ✓ Average interval: {avg_interval:.1f} minutes")
                print(f"   ✓ Conflicts (< 20 min): {len(conflicts)}")
                
                if conflicts:
                    issues.append(f"{len(conflicts)} shops have intervals < 20 minutes")
            else:
                print("   ✓ Only one shop scheduled")
        else:
            issues.append("No shops are scheduled")
        
        # Test 5: Check system task timing
        print("\n5. Checking system task timing...")
        try:
            schedule = get_dynamic_schedule()
            
            # Check for system tasks
            cleanup_task = schedule.get('cleanup-old-jobs')
            search_console_task = schedule.get('fetch-search-console-metrics')
            
            if cleanup_task:
                cleanup_schedule = cleanup_task['schedule']
                print(f"   ✓ Cleanup task: {cleanup_schedule.hour[0]:02d}:{cleanup_schedule.minute[0]:02d}")
            else:
                issues.append("Cleanup task not found in schedule")
            
            if search_console_task:
                sc_schedule = search_console_task['schedule']
                print(f"   ✓ Search Console task: {sc_schedule.hour[0]:02d}:{sc_schedule.minute[0]:02d}")
            else:
                issues.append("Search Console task not found in schedule")
            
        except Exception as e:
            issues.append(f"System task check: {e}")
        
        # Test 6: Check for early shop schedules
        print("\n6. Checking for scheduling conflicts with system tasks...")
        early_shops = UserShop.query.filter(
            UserShop.automation_enabled == True,
            UserShop.automation_hour < 4
        ).all()
        
        if early_shops:
            issues.append(f"{len(early_shops)} shops scheduled before 4:00 AM (conflicts with system tasks)")
            for shop in early_shops:
                print(f"   ⚠️  Shop {shop.shop_id} at {shop.automation_hour:02d}:{shop.automation_minute:02d}")
        else:
            print("   ✓ No shops scheduled before 4:00 AM")
    
    # Summary
    print(f"\n{'='*80}")
    print("VERIFICATION SUMMARY")
    print(f"{'='*80}")
    
    if issues:
        print(f"❌ Found {len(issues)} issues:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 RECOMMENDED ACTIONS:")
        print("   1. Run: python redistribute_shop_schedules.py")
        print("   2. Enable automation features in shop settings")
        print("   3. Restart Celery Beat scheduler")
        print("   4. Run this verification again")
        
        return False
    else:
        print("✅ All checks passed! Scheduling system is working correctly.")
        print(f"\n📋 SYSTEM STATUS:")
        print("   • 20-minute intervals properly configured")
        print("   • No scheduling conflicts detected")
        print("   • System tasks properly separated")
        print("   • Dynamic schedule generation working")
        
        return True

def show_recommendations():
    """Show recommendations for optimal system operation"""
    print(f"\n{'='*80}")
    print("RECOMMENDATIONS FOR OPTIMAL OPERATION")
    print(f"{'='*80}")
    
    print("1. MONITORING:")
    print("   • Run 'python check_automation_schedules.py' daily")
    print("   • Monitor Celery Beat logs for schedule loading")
    print("   • Check task execution success rates")
    
    print("\n2. MAINTENANCE:")
    print("   • Restart Celery Beat weekly to refresh schedules")
    print("   • Run redistribution if adding many new shops")
    print("   • Monitor Redis memory usage")
    
    print("\n3. SCALING:")
    print("   • Current system supports 60+ shops (20 hours)")
    print("   • Consider multiple time zones for global scaling")
    print("   • Monitor task execution times vs intervals")
    
    print("\n4. TROUBLESHOOTING:")
    print("   • Check shop automation settings if tasks don't run")
    print("   • Verify Redis connectivity for task queuing")
    print("   • Ensure only one Celery Beat instance is running")

if __name__ == '__main__':
    success = verify_scheduling_system()
    show_recommendations()
    
    if success:
        print(f"\n🎉 SCHEDULING SYSTEM IS READY!")
        print("Your Celery task scheduling is now perfectly configured for 20-minute intervals.")
    else:
        print(f"\n⚠️  ISSUES DETECTED - PLEASE FIX BEFORE PROCEEDING")
    
    print(f"\n{'='*80}\n")
