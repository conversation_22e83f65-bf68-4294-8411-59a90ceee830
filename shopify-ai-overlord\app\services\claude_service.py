import os
import json
from typing import List, Dict, Any, Optional
import anthropic
from anthropic import Anthropic
import requests
from bs4 import BeautifulSoup

class ClaudeService:
    def __init__(self, api_key: str = None):
        if not api_key:
            api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            raise ValueError("Anthropic API key is required")
        self.client = Anthropic(api_key=api_key)
        self.model = "claude-sonnet-4-20250514"  # Claude Sonnet 4 - Latest model!
        
    def create_tools(self) -> List[Dict[str, Any]]:
        """Define available tools for Claude 4 to use"""
        return [
            {
                "name": "search_web",
                "description": "Search the web for current information about SEO, marketing trends, or competitive analysis",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "analyze_image",
                "description": "Analyze product images for SEO optimization and visual merchandising improvements",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "image_url": {
                            "type": "string",
                            "description": "URL of the product image to analyze"
                        },
                        "analysis_type": {
                            "type": "string",
                            "enum": ["seo", "quality", "merchandising", "accessibility"],
                            "description": "Type of analysis to perform"
                        }
                    },
                    "required": ["image_url", "analysis_type"]
                }
            },
            {
                "name": "analyze_competitor",
                "description": "Analyze a competitor's website for SEO and marketing insights",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "url": {
                            "type": "string",
                            "description": "The competitor's website URL"
                        }
                    },
                    "required": ["url"]
                }
            },
            {
                "name": "generate_seo_content",
                "description": "Generate SEO-optimized content for products or collections",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "content_type": {
                            "type": "string",
                            "enum": ["product_title", "product_description", "meta_description", "collection_description"],
                            "description": "Type of content to generate"
                        },
                        "keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Target keywords to include"
                        },
                        "context": {
                            "type": "string",
                            "description": "Additional context about the product/collection"
                        }
                    },
                    "required": ["content_type", "keywords", "context"]
                }
            },
            {
                "name": "translate_content",
                "description": "Translate content to different languages for international SEO",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": "Content to translate"
                        },
                        "target_language": {
                            "type": "string",
                            "description": "Target language code (e.g., 'es', 'fr', 'de')"
                        },
                        "maintain_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Keywords to maintain in original form"
                        }
                    },
                    "required": ["content", "target_language"]
                }
            }
        ]
    
    def execute_tool(self, tool_name: str, tool_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool and return the result"""
        if tool_name == "search_web":
            return self._search_web(tool_input["query"])
        elif tool_name == "analyze_competitor":
            return self._analyze_competitor(tool_input["url"])
        elif tool_name == "generate_seo_content":
            return self._generate_seo_content(
                tool_input["content_type"],
                tool_input["keywords"],
                tool_input["context"]
            )
        elif tool_name == "translate_content":
            return self._translate_content(
                tool_input["content"],
                tool_input["target_language"],
                tool_input.get("maintain_keywords", [])
            )
        else:
            return {"error": f"Unknown tool: {tool_name}"}
    
    def _search_web(self, query: str) -> Dict[str, Any]:
        """Simulate web search (in production, use a real search API)"""
        # In production, integrate with a search API like SerpAPI or Google Custom Search
        return {
            "results": [
                {
                    "title": f"SEO Best Practices for {query}",
                    "snippet": "Latest trends and strategies...",
                    "url": "https://example.com/seo-guide"
                }
            ]
        }
    
    def _analyze_competitor(self, url: str) -> Dict[str, Any]:
        """Analyze competitor website"""
        try:
            response = requests.get(url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract basic SEO elements
            title = soup.find('title')
            meta_description = soup.find('meta', attrs={'name': 'description'})
            h1_tags = soup.find_all('h1')
            
            return {
                "url": url,
                "title": title.text if title else "No title found",
                "meta_description": meta_description.get('content') if meta_description else "No meta description",
                "h1_count": len(h1_tags),
                "h1_content": [h1.text.strip() for h1 in h1_tags[:5]]
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _generate_seo_content(self, content_type: str, keywords: List[str], context: str) -> Dict[str, Any]:
        """Generate SEO-optimized content"""
        # This would use Claude to generate content
        # For now, return a template
        return {
            "content_type": content_type,
            "generated_content": f"SEO-optimized {content_type} including keywords: {', '.join(keywords)}",
            "keywords_included": keywords
        }
    
    def _translate_content(self, content: str, target_language: str, maintain_keywords: List[str]) -> Dict[str, Any]:
        """Translate content while maintaining keywords"""
        # This would use Claude for translation
        # For now, return a template
        return {
            "original": content,
            "translated": f"[Translated to {target_language}] {content}",
            "language": target_language,
            "maintained_keywords": maintain_keywords
        }
    
    def optimize_product_seo(self, product_title: str, product_description: str, keywords: List[str] = None) -> Dict[str, Any]:
        """Optimize product SEO using Claude's web search capabilities"""
        
        # Prepare the prompt for Claude
        prompt = f"""
        I need to optimize this Shopify product for SEO. Please search for top-ranking similar products and analyze their SEO strategies.
        
        Current Title: {product_title}
        Current Description: {product_description}
        Target Keywords: {', '.join(keywords) if keywords else 'Not specified'}
        
        IMPORTANT RULES:
        - ONLY use information from the provided product details and your web search results
        - DO NOT make up or invent any product details, features, or specifications
        - Base all optimizations on actual SEO patterns found in top-ranking products
        - If you don't have specific information about the product, work with what's provided
        - PRESERVE ALL EXISTING TABLES in the product description - any tables must be kept intact
        - DO NOT remove or modify any HTML tables present in the original description
        - Output HTML without any unecessary HTML tags, as this is going directly onto a CMS, start with <h2> NOT <h1> as the H1 is defined elsewhere.
        
        Please:
        1. Search for the top-ranking products similar to "{product_title}" on Google
        2. Analyze the top 10 results' titles, descriptions, and SEO strategies
        3. Create optimized content based on successful patterns found
        4. For the tags always if it's branded add Brand name + product type, and also Brand Name as a tag. For example, Kiton, and Kiton Sneakers, and Kiton Shoes
        5. More tags the better BUT do not create spammy tags - Around 15 Per product is about right  - You must ALWAYS create the base tag, for example Sneakers, Backpacks, etc, this is the product-type, you must also always create brand tags if the product has a brand.
        6. In both titles and descriptions avoid marketing language such as step into, in the everchanging world of, elevate, and other superfluous language that AI is prone to use, keep it direct and simple, using the product specifications to do the talking.

        Return your response as valid JSON in this exact format:
        {{
            "optimized_title": "SEO-optimized title based on top competitors (max 70 chars)",
            "meta_description": "SEO-optimized meta description based on search findings (max 160 chars)",
            "product_description": "Enhanced product description using ONLY provided details with natural keyword integration",
            "h1_tag": "Optimized H1 tag for the product page",
            "tags": ["product-type", "use-case-tag", "style-tag", "material-tag", "brand-category", "seasonal-tag", "brand_tag", "brand+product_category_tag", "color-tag"]
            "competitor_insights": "Brief summary of actual findings from top competitors",
            "keywords_used": ["keyword1", "keyword2", "keyword3"]
        }}
        
        TAGS GUIDANCE:
        - Generate 15 tags total
        - Focus on collection/category tags that products can be grouped by
        - Include tags for: product category, collection themes, use cases, styles, materials, occasions
        - Make tags that would work well as future collection names
        
        Make sure to return ONLY the JSON object, no additional text.
        """
        
        # Use Claude's web search tool - try without tools first to diagnose the issue
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
        except Exception as e:
            # If the regular call fails, return error details
            return {
                "success": False,
                "error": f"Claude API error: {str(e)}",
                "error_type": type(e).__name__,
                "model_used": self.model
            }
        
        # Extract the optimized content from Claude's response
        try:
            # Handle the response properly
            response_text = None
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                # Check if the first content block has text
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    response_text = content_block.text
                else:
                    # If it's not a text block, convert to string
                    response_text = str(content_block)
            else:
                # Fallback: try to convert entire response to string
                response_text = str(response)
            
            # Try to parse JSON from the response
            import re
            
            # Remove markdown code blocks if present
            if '```json' in response_text:
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
                if json_match:
                    response_text = json_match.group(1)
            
            # Try to find JSON object in the response
            json_match = re.search(r'\{[\s\S]*\}', response_text)
            if json_match:
                try:
                    parsed_data = json.loads(json_match.group())
                    return {
                        "success": True,
                        "parsed_data": parsed_data,
                        "model_used": self.model
                    }
                except json.JSONDecodeError as e:
                    # If JSON parsing fails, return the raw response
                    return {
                        "success": True,
                        "optimized_content": response_text,
                        "json_error": str(e),
                        "model_used": self.model
                    }
            else:
                # No JSON found in response
                return {
                    "success": True,
                    "optimized_content": response_text,
                    "model_used": self.model
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error extracting response: {str(e)}",
                "response_type": str(type(response)),
                "model_used": self.model
            }
    
    def optimize_collection_seo(self, tag: str, product_count: int, collection_type: str = 'smart', seo_keywords: List[str] = None) -> Dict[str, Any]:
        """Optimize collection SEO using Claude's web search capabilities"""
        
        # Prepare the prompt for Claude
        prompt = f"""
        I need to create an SEO-optimized Shopify collection for products tagged with: "{tag}"
        This collection will contain {product_count} products.
        Collection Type: {collection_type} collection
        Additional SEO Keywords: {', '.join(seo_keywords) if seo_keywords else 'None specified'}
        
        IMPORTANT RULES:
        - Search for successful online collections similar to "{tag}" 
        - Analyze their SEO strategies, titles, and descriptions
        - DO NOT make up product details or features
        - Create compelling, SEO-friendly content based on the tag theme
        - Generate a handle (URL slug) from the title, make it extremely stripped down, only use the key terms from it like kiton_shoes etc. do not add random words here from the title.
        
        Please:
        1. Search Google for top-ranking collections related to "{tag}"
        2. Analyze successful collection pages and their SEO strategies
        3. Create optimized content based on patterns found
        4. In both titles and descriptions avoid marketing language such as step into, in the everchanging world of, elevate, and other superfluous language that AI is prone to use, keep it direct and simple, using the product specifications to do the talking.

        
        Return your response as valid JSON in this exact format:
        {{
            "title": "SEO-optimized collection title (max 60 chars)",
            "handle": "url-friendly-handle-based-on-title",
            "meta_description": "SEO meta description for the collection page (max 160 chars)",
            "description": "Full HTML description for the collection page with natural keyword integration and compelling copy, starting with an <h2> tag and without unecessary HTML tags like <!doctype> as this is being posted to Shopify",
            "competitor_insights": "Brief summary of what top competitors are doing with similar collections",
            "seo_keywords_used": ["keyword1", "keyword2", "keyword3"],
            "suggested_sort_order": "best-selling"
        }}
        
        Make sure to return ONLY the JSON object, no additional text.
        """
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
        except Exception as e:
            return {
                "success": False,
                "error": f"Claude API error: {str(e)}",
                "error_type": type(e).__name__,
                "model_used": self.model
            }
        
        # Extract the optimized content from Claude's response
        try:
            response_text = None
            
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    response_text = content_block.text
                else:
                    response_text = str(content_block)
            else:
                response_text = str(response) if response else None
                
            if not response_text:
                return {
                    "success": False,
                    "error": "No text content in Claude's response",
                    "model_used": self.model
                }
            
            # Try to parse JSON from the response
            import re
            
            # Remove markdown code blocks if present
            if '```json' in response_text:
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
                if json_match:
                    response_text = json_match.group(1)
            
            # Try to find JSON object in the response
            json_match = re.search(r'\{[\s\S]*\}', response_text)
            if json_match:
                try:
                    parsed_data = json.loads(json_match.group())
                    return {
                        "success": True,
                        "parsed_data": parsed_data,
                        "model_used": self.model
                    }
                except json.JSONDecodeError as e:
                    # If JSON parsing fails, return the raw response
                    return {
                        "success": True,
                        "optimized_content": response_text,
                        "json_error": str(e),
                        "model_used": self.model
                    }
            else:
                # No JSON found in response
                return {
                    "success": True,
                    "optimized_content": response_text,
                    "model_used": self.model
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error extracting response: {str(e)}",
                "model_used": self.model
            }
    
    def translate_content(self, content: Dict[str, str], source_language: str, target_language: str, content_type: str, seo_keywords: List[str] = None) -> Dict[str, Any]:
        """Translate content with SEO optimization and cultural adaptation"""
        
        # Prepare the prompt for Claude
        prompt = f"""
        I need to translate and culturally adapt content from {source_language} to {target_language} for a Shopify store.
        
        Content Type: {content_type}
        Source Language: {source_language}
        Target Language: {target_language}
        SEO Keywords to maintain: {', '.join(seo_keywords) if seo_keywords else 'None specified'}
        
        Content to translate:
        {json.dumps(content, indent=2, ensure_ascii=False)}
        
        IMPORTANT RULES:
        - Provide culturally appropriate translations, not just literal ones
        - Maintain SEO effectiveness in the target language
        - For product titles, keep them under 70 characters
        - For meta descriptions, keep them under 160 characters
        - Preserve the meaning and selling points
        - Use native expressions that resonate with the target market
        - Research successful {content_type} in {target_language} markets
        
        Please:
        1. Search for successful {content_type} examples in {target_language} markets
        2. Analyze SEO patterns and cultural preferences
        3. Create translations that feel native, not translated
        
        Return your response as valid JSON in this exact format:
        {{
            "translations": {{
                "title": "Translated title with SEO optimization",
                "description": "Translated description maintaining selling points",
                "meta_description": "SEO-optimized meta description in target language",
                "body_html": "Full translated HTML content if applicable",
                "tags": ["translated-tag1", "translated-tag2", "translated-tag3"],
                "handle": "url-friendly-handle-in-target-language"
            }},
            "cultural_notes": "Brief notes on cultural adaptations made",
            "seo_analysis": "How SEO was maintained in translation",
            "localization_tips": "Specific market considerations"
        }}
        
        Make sure to return ONLY the JSON object, no additional text.
        """
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
        except Exception as e:
            return {
                "success": False,
                "error": f"Claude API error: {str(e)}",
                "error_type": type(e).__name__,
                "model_used": self.model
            }
        
        # Extract the response
        try:
            response_text = None
            
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    response_text = content_block.text
                else:
                    response_text = str(content_block)
            else:
                response_text = str(response) if response else None
                
            if not response_text:
                return {
                    "success": False,
                    "error": "No text content in Claude's response",
                    "model_used": self.model
                }
            
            # Try to parse JSON from the response
            import re
            
            # Remove markdown code blocks if present
            if '```json' in response_text:
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
                if json_match:
                    response_text = json_match.group(1)
            
            # Try to find JSON object in the response
            json_match = re.search(r'\{[\s\S]*\}', response_text)
            if json_match:
                try:
                    parsed_data = json.loads(json_match.group())
                    return {
                        "success": True,
                        "parsed_data": parsed_data,
                        "model_used": self.model
                    }
                except json.JSONDecodeError as e:
                    # If JSON parsing fails, return the raw response
                    return {
                        "success": True,
                        "translated_content": response_text,
                        "json_error": str(e),
                        "model_used": self.model
                    }
            else:
                # No JSON found in response
                return {
                    "success": True,
                    "translated_content": response_text,
                    "model_used": self.model
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error extracting response: {str(e)}",
                "model_used": self.model
            }
    
    def generate_hreflang_strategy(self, shop_domain: str, enabled_languages: List[Dict], url_structure: str = "subdirectory") -> Dict[str, Any]:
        """Generate hreflang implementation strategy and technical setup"""
        
        prompt = f"""
        I need to create an hreflang implementation strategy for a Shopify store.
        
        Shop Domain: {shop_domain}
        Enabled Languages: {json.dumps(enabled_languages, indent=2)}
        URL Structure Preference: {url_structure} (subdirectory, subdomain, or domain)
        
        Please provide a comprehensive hreflang implementation strategy including:
        1. Recommended URL structure based on the languages and markets
        2. Complete hreflang tag examples for different page types
        3. Sitemap configuration recommendations
        4. Implementation checklist
        5. Common pitfalls to avoid
        
        Return your response as valid JSON in this exact format:
        {{
            "url_structure": {{
                "recommendation": "subdirectory/subdomain/domain",
                "reasoning": "Why this structure is best for this setup",
                "examples": {{
                    "products": ["example.com/products/shirt", "example.com/fr/products/chemise"],
                    "collections": ["example.com/collections/summer", "example.com/fr/collections/ete"],
                    "pages": ["example.com/about", "example.com/fr/a-propos"]
                }}
            }},
            "hreflang_tags": {{
                "product_page": ["<link rel='alternate' hreflang='en' href='...' />", "..."],
                "collection_page": ["<link rel='alternate' hreflang='en' href='...' />", "..."],
                "home_page": ["<link rel='alternate' hreflang='x-default' href='...' />", "..."]
            }},
            "sitemap_config": {{
                "structure": "Recommended sitemap structure",
                "example": "XML sitemap example with hreflang"
            }},
            "implementation_checklist": [
                "Step 1: ...",
                "Step 2: ...",
                "Step 3: ..."
            ],
            "common_pitfalls": [
                "Pitfall 1: ...",
                "Pitfall 2: ..."
            ]
        }}
        
        Make sure to return ONLY the JSON object, no additional text.
        """
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
            
            response_text = None
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    response_text = content_block.text
                    
            return {
                "success": True,
                "strategy": response_text,
                "model_used": self.model
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating hreflang strategy: {str(e)}",
                "model_used": self.model
            }
    
    def analyze_homepage(self, homepage_url: str, target_keywords: List[str] = None, competitor_urls: List[str] = None) -> Dict[str, Any]:
        """Analyze homepage using JINA AI to fetch content and Claude for SEO analysis"""
        
        try:
            # Fetch homepage content using JINA AI
            jina_headers = {
                "Authorization": "Bearer jina_033257e7cdf14fd3b948578e2d34986bNtfCCkjHt7_j1Bkp5Kx521rDs2Eb",
                "X-Remove-Selector": "header, footer, nav, .popup, .modal"
            }
            
            jina_url = f"https://r.jina.ai/{homepage_url}"
            response = requests.get(jina_url, headers=jina_headers, timeout=30)
            
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Failed to fetch homepage content: {response.status_code}"
                }
            
            homepage_content = response.text
            
            # Fetch competitor content if provided
            competitor_analyses = []
            if competitor_urls:
                for comp_url in competitor_urls[:2]:  # Limit to 2 competitors
                    try:
                        comp_jina_url = f"https://r.jina.ai/{comp_url}"
                        comp_response = requests.get(comp_jina_url, headers=jina_headers, timeout=20)
                        if comp_response.status_code == 200:
                            competitor_analyses.append({
                                "url": comp_url,
                                "content": comp_response.text[:2000]  # First 2000 chars
                            })
                    except:
                        pass  # Skip failed competitor fetches
            
            # Prepare Claude prompt
            prompt = f"""
            Analyze this Shopify store homepage for SEO optimization. Use the JINA-fetched content to provide specific recommendations.
            
            Homepage URL: {homepage_url}
            Target Keywords: {', '.join(target_keywords) if target_keywords else 'Not specified'}
            
            Homepage Content:
            {homepage_content[:5000]}  # First 5000 chars
            
            {"Competitor Analyses:" if competitor_analyses else ""}
            {json.dumps(competitor_analyses, indent=2) if competitor_analyses else ""}
            
            Please analyze and provide recommendations in this exact JSON format:
            {{
                "current_title": "Extract the current page title",
                "current_meta_description": "Extract current meta description if found",
                "content_length": "Approximate word count",
                "optimized_title": "SEO-optimized title WITHOUT the brand/store name (Shopify automatically appends it) - max 60 chars - focus on value proposition and keywords",
                "optimized_meta_description": "Compelling meta description (max 160 chars) with keywords",
                "collection_recommendations": [
                    {{
                        "title": "Featured Products",
                        "description": "Showcase your best-selling items",
                        "priority": "High"
                    }},
                    {{
                        "title": "New Arrivals",
                        "description": "Latest additions to your catalog",
                        "priority": "Medium"
                    }}
                ],
                "content_suggestions": [
                    {{
                        "type": "Hero Banner",
                        "description": "Add a compelling hero section with clear value proposition",
                        "benefit": "Improves first impression and reduces bounce rate",
                        "priority": "High"
                    }},
                    {{
                        "type": "Trust Badges",
                        "description": "Include security badges, shipping info, return policy",
                        "benefit": "Builds customer confidence",
                        "priority": "Medium"
                    }}
                ],
                "seo_improvements": [
                    "Add schema markup for better rich snippets",
                    "Implement breadcrumb navigation",
                    "Optimize image alt texts",
                    "Add internal linking to key pages",
                    "Include customer testimonials section"
                ],
                "competitor_insights": "Key strengths observed in competitor homepages"
            }}
            
            Make sure to return ONLY the JSON object, no additional text.
            """
            
            # Get Claude's analysis
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )
            
            # Extract response
            response_text = None
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    response_text = content_block.text
                else:
                    response_text = str(content_block)
            
            # Try to parse JSON
            if response_text:
                try:
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', response_text)
                    if json_match:
                        analysis_data = json.loads(json_match.group())
                        return {
                            "success": True,
                            "data": analysis_data,
                            "model_used": self.model
                        }
                    else:
                        # Try parsing entire response
                        analysis_data = json.loads(response_text)
                        return {
                            "success": True,
                            "data": analysis_data,
                            "model_used": self.model
                        }
                except json.JSONDecodeError:
                    return {
                        "success": True,
                        "data": {
                            "raw_response": response_text,
                            "parsing_failed": True
                        },
                        "model_used": self.model
                    }
            
            return {
                "success": False,
                "error": "No response from Claude",
                "model_used": self.model
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error analyzing homepage: {str(e)}",
                "error_type": type(e).__name__
            }
    
    def generate_ad_copy(self, product_name: str, target_audience: str, platform: str, benefits: str = None) -> Dict[str, Any]:
        """Generate ad copy using Claude with competitor analysis"""
        
        prompt = f"""
        Create high-converting {platform} ad copy for:
        Product: {product_name}
        Target Audience: {target_audience}
        Key Benefits: {benefits or 'To be determined'}
        
        Please:
        1. Search for successful {platform} ads for similar products
        2. Analyze what makes top-performing ads effective
        3. Generate 5 compelling headlines (character limits for {platform})
        4. Write 3 engaging descriptions
        5. Include emotional triggers and clear CTAs
        6. Suggest A/B testing variations
        """
        
        response = self.client.messages.create(
            model=self.model,
            max_tokens=4096,
            temperature=0.8,
            tools=[{
                "type": "web_search_20250305",
                "name": "web_search",
                "max_uses": 2
            }],
            messages=[{"role": "user", "content": prompt}]
        )
        
        try:
            # Safely extract text from response
            ad_copy_text = None
            if hasattr(response, 'content') and response.content and len(response.content) > 0:
                content_block = response.content[0]
                if hasattr(content_block, 'text'):
                    ad_copy_text = content_block.text
                else:
                    ad_copy_text = str(content_block)
            else:
                ad_copy_text = str(response)
                
            return {
                "success": True,
                "ad_copy": ad_copy_text,
                "platform": platform
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating ad copy: {str(e)}",
                "platform": platform
            }