from functools import wraps
from flask import session, redirect, url_for, request, current_app
from app.models.access_control import AccessKey, UserSettings, UserShop


def login_required(f):
    """Decorator to require login via access key"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'access_key_id' not in session:
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('is_admin'):
            return redirect(url_for('auth.admin_login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function


def get_current_user_settings():
    """Get the current user's settings from session"""
    if 'access_key_id' not in session:
        return None
    
    access_key = AccessKey.query.get(session['access_key_id'])
    if not access_key or not access_key.is_active:
        return None
    
    return access_key.user_settings


def get_user_api_keys():
    """Get the current user's API keys"""
    settings = get_current_user_settings()
    if not settings:
        return {}
    
    return {
        'shopify_api_key': settings.shopify_api_key,
        'shopify_api_secret': settings.shopify_api_secret,
        'shopify_access_token': settings.shopify_access_token,
        'shopify_shop_domain': settings.shopify_shop_domain,
        'anthropic_api_key': settings.anthropic_api_key
    }


def get_current_shop():
    """Get the current user's selected shop"""
    if 'access_key_id' not in session:
        return None
    
    access_key_id = session['access_key_id']
    current_user_shop = UserShop.get_current_shop(access_key_id)
    
    if current_user_shop:
        # Update session with current shop info
        session['current_shop_id'] = current_user_shop.shop_id
        session['current_shop_name'] = current_user_shop.shop.shop_name or current_user_shop.shop.shop_domain
        return current_user_shop.shop
    
    return None


def store_required(f):
    """Decorator to require a store to be selected"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'access_key_id' not in session:
            return redirect(url_for('auth.login', next=request.url))
        
        current_shop = get_current_shop()
        if not current_shop:
            return redirect(url_for('auth.store_management'))
        
        return f(*args, **kwargs)
    return decorated_function