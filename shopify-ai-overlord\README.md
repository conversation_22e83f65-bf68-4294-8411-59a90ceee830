# Shopify AI Control System

A Python Flask application that allows Shopify store owners to control their storefronts using Claude 4 AI-powered modules for SEO, Ads, Email Marketing, and Customer Support.

## Features

- **Shopify OAuth Integration**: Secure connection to Shopify stores
- **5 AI Modules**:
  - SEO Optimization
  - Ads Management
  - Email Marketing
  - Customer Support/Chatbot
- **Dashboard**: Central control panel with module management
- **Sidebar Navigation**: Easy access to all AI modules

## Setup Instructions

### 1. Prerequisites

- Python 3.8+
- Redis (for background tasks)
- Shopify Partner account and app credentials

### 2. Installation

```bash
# Clone the repository
cd shopify-ai-control

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Edit `.env` and add your credentials:
```
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_APP_URL=https://your-app-url.com
OPENAI_API_KEY=your-openai-api-key
```

### 4. Shopify App Setup

1. Create a new app in your Shopify Partner Dashboard
2. Set the App URL to your application URL
3. Set the Allowed redirection URL(s) to: `https://your-app-url.com/shopify/callback`
4. Copy the API key and API secret to your `.env` file

### 5. Database Setup

```bash
# Initialize the database
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 6. Running the Application

```bash
# Development mode
python run.py

# Production mode with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

## Usage

1. Navigate to `http://localhost:5000`
2. Click "Connect Your Shopify Store"
3. Enter your store domain (e.g., `mystore` for `mystore.myshopify.com`)
4. Authorize the app in Shopify
5. You'll be redirected to the dashboard where you can enable/disable AI modules

## Project Structure

```
shopify-ai-control/
├── app/
│   ├── __init__.py
│   ├── models/
│   │   ├── shop.py
│   │   └── ai_config.py
│   ├── routes/
│   │   ├── main.py
│   │   ├── auth.py
│   │   ├── shopify.py
│   │   └── ai_modules.py
│   ├── services/
│   │   └── shopify_service.py
│   ├── templates/
│   │   ├── base.html
│   │   ├── index.html
│   │   ├── dashboard.html
│   │   └── ai_modules/
│   └── static/
├── config.py
├── run.py
├── requirements.txt
└── README.md
```

## Security Notes

- Never commit your `.env` file
- Use HTTPS in production
- Regularly rotate your API credentials
- Implement rate limiting for API calls

## Future Enhancements

- Implement actual AI functionality for each module
- Add webhook support for real-time updates
- Implement background job processing with Celery
- Add more detailed analytics and reporting
- Support for multiple stores per account