#!/usr/bin/env python3
"""
Clear Task Queue and Reset System

This script clears all pending Celery tasks and resets the automation system
to stop the infinite loop of duplicate tasks.
"""

import os
import sys
import redis

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.automation_job import AutomationJob
import time

def clear_redis_queues():
    """Clear all Celery queues in Redis"""
    try:
        # Connect to Redis
        redis_url = os.getenv('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
        r = redis.from_url(redis_url, ssl_cert_reqs=None)
        
        print("Connecting to Redis...")
        
        # Get all keys
        all_keys = r.keys('*')
        print(f"Found {len(all_keys)} total keys in Redis")
        
        # Find Celery-related keys
        celery_keys = [key for key in all_keys if b'celery' in key.lower()]
        queue_keys = [key for key in all_keys if key.startswith(b'_kombu')]
        task_keys = [key for key in all_keys if b'task' in key.lower()]
        
        print(f"Found {len(celery_keys)} Celery keys")
        print(f"Found {len(queue_keys)} queue keys")
        print(f"Found {len(task_keys)} task keys")
        
        # Clear all Celery-related keys
        keys_to_delete = set(celery_keys + queue_keys + task_keys)
        
        if keys_to_delete:
            print(f"Deleting {len(keys_to_delete)} keys...")
            r.delete(*keys_to_delete)
            print("✓ Redis queues cleared")
        else:
            print("No Celery keys found to delete")
            
        # Also clear specific queue names
        queue_names = ['celery', 'default', 'shopify_ai_control']
        for queue_name in queue_names:
            queue_length = r.llen(queue_name)
            if queue_length > 0:
                print(f"Clearing queue '{queue_name}' with {queue_length} items...")
                r.delete(queue_name)
        
        return True
        
    except Exception as e:
        print(f"Error clearing Redis queues: {e}")
        return False

def reset_automation_jobs():
    """Reset automation job statuses"""
    app = create_app()
    
    with app.app_context():
        try:
            # Find all processing jobs
            processing_jobs = AutomationJob.query.filter_by(status='processing').all()
            print(f"Found {len(processing_jobs)} processing jobs")
            
            # Mark them as failed
            for job in processing_jobs:
                job.status = 'failed'
                job.error_message = 'System reset - task queue cleared'
            
            db.session.commit()
            print(f"✓ Reset {len(processing_jobs)} processing jobs to failed status")
            
            # Show recent automation jobs
            recent_jobs = AutomationJob.query.filter_by(
                task_type='daily_automation'
            ).order_by(AutomationJob.created_at.desc()).limit(10).all()
            
            print(f"\nRecent daily automation jobs:")
            for job in recent_jobs:
                print(f"  {job.created_at}: Shop {job.shop_id} - {job.status}")
            
            return True
            
        except Exception as e:
            print(f"Error resetting automation jobs: {e}")
            return False

def show_system_status():
    """Show current system status"""
    app = create_app()
    
    with app.app_context():
        try:
            # Count jobs by status
            total_jobs = AutomationJob.query.count()
            processing_jobs = AutomationJob.query.filter_by(status='processing').count()
            completed_jobs = AutomationJob.query.filter_by(status='completed').count()
            failed_jobs = AutomationJob.query.filter_by(status='failed').count()
            
            print(f"\nAutomation Job Status:")
            print(f"  Total: {total_jobs}")
            print(f"  Processing: {processing_jobs}")
            print(f"  Completed: {completed_jobs}")
            print(f"  Failed: {failed_jobs}")
            
            # Check for today's automation runs
            from datetime import datetime
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            
            today_automations = AutomationJob.query.filter(
                AutomationJob.task_type == 'daily_automation',
                AutomationJob.created_at >= today_start
            ).all()
            
            print(f"\nToday's daily automation runs: {len(today_automations)}")
            for job in today_automations:
                print(f"  Shop {job.shop_id}: {job.status} at {job.created_at}")
            
        except Exception as e:
            print(f"Error showing system status: {e}")

def main():
    print(f"\n{'='*60}")
    print("CLEARING TASK QUEUE AND RESETTING SYSTEM")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*60}\n")
    
    print("This will:")
    print("1. Clear all pending Celery tasks from Redis")
    print("2. Reset processing automation jobs to failed status")
    print("3. Show current system status")
    
    response = input("\nProceed with system reset? (y/N): ").strip().lower()
    
    if response != 'y':
        print("System reset cancelled.")
        return
    
    print("\n1. Clearing Redis task queues...")
    redis_success = clear_redis_queues()
    
    print("\n2. Resetting automation jobs...")
    jobs_success = reset_automation_jobs()
    
    print("\n3. System status after reset:")
    show_system_status()
    
    if redis_success and jobs_success:
        print(f"\n{'='*60}")
        print("✅ SYSTEM RESET COMPLETE")
        print("The task queue has been cleared and automation jobs reset.")
        print("You can now restart your Celery workers and beat scheduler.")
        print(f"{'='*60}\n")
        
        print("Next steps:")
        print("1. Stop all Celery workers and beat scheduler")
        print("2. Restart Celery workers: celery -A celery_app worker --loglevel=info")
        print("3. Start beat scheduler: python celery_beat_config.py")
        print("4. Monitor with: python check_automation_schedules.py")
    else:
        print(f"\n{'='*60}")
        print("❌ SYSTEM RESET INCOMPLETE")
        print("Some operations failed. Check the errors above.")
        print(f"{'='*60}\n")

if __name__ == '__main__':
    main()
