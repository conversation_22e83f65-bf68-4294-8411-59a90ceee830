# Celery Task Scheduling System - 20-Minute Intervals

## Overview

This document describes the fixed Celery task scheduling system that ensures each shop ID runs all quota'd tasks at 20-minute intervals. The system has been completely redesigned to eliminate conflicts and provide perfect scheduling.

## Key Features

- **20-Minute Intervals**: Each shop is assigned a unique 20-minute time slot
- **Automatic Assignment**: New shops automatically get the next available slot
- **Conflict Prevention**: System tasks run at 1:00 AM and 3:00 AM, shop automations start at 4:00 AM
- **Dynamic Scheduling**: Celery Beat dynamically loads schedules based on enabled automations
- **Quota Integration**: Each shop runs all enabled automations (SEO, Collections, Translation) in sequence

## Schedule Structure

### System Tasks
- **01:00 UTC**: Cleanup old automation jobs
- **03:00 UTC**: Fetch Search Console metrics

### Shop Automations
- **Starting 04:00 UTC**: Shop automations with 20-minute intervals
- **04:00 UTC**: Shop 1
- **04:20 UTC**: Shop 2  
- **04:40 UTC**: Shop 3
- **05:00 UTC**: Shop 4
- And so on...

## Files Modified

### Core Scheduling Files
1. **`celerybeat_schedule.py`**
   - Enhanced dynamic schedule generation
   - Only schedules shops with enabled automations
   - Improved logging and error handling

2. **`app/models/access_control.py`**
   - Added `assign_next_automation_slot()` method
   - Added `redistribute_all_schedules()` method
   - Automatic 20-minute interval assignment

3. **`app/routes/shopify.py`**
   - Updated shop registration to use new slot assignment
   - Automatic scheduling when shops are added

4. **`app/tasks.py`**
   - Removed conflicting `check_automation_queues` task
   - Eliminated duplicate scheduling mechanisms

### Utility Scripts
1. **`redistribute_shop_schedules.py`**
   - Redistributes existing shops to proper intervals
   - Fixes any scheduling conflicts

2. **`check_automation_schedules.py`**
   - Displays current schedule status
   - Identifies conflicts and issues
   - Shows next run times

3. **`trigger_daily_automation.py`**
   - Updated to use 20-minute intervals for manual triggers

## How It Works

### 1. Shop Registration
When a new shop is registered:
```python
# Automatically assigns next available 20-minute slot
automation_hour, automation_minute = UserShop.assign_next_automation_slot()
```

### 2. Dynamic Schedule Generation
Celery Beat calls `get_dynamic_schedule()` which:
- Queries all shops with `automation_enabled=True`
- Checks each shop's automation configuration
- Only schedules shops with enabled features (SEO, Collections, Translation)
- Creates individual cron jobs for each shop

### 3. Task Execution
Each shop's automation runs:
1. **SEO Optimization**: Based on quota limits
2. **Collection Creation**: Based on available tags and quota
3. **Translation Tasks**: For enabled languages
4. **Alt Text Optimization**: If enabled (30 minutes after main automation)

## Usage Instructions

### Check Current Schedules
```bash
python check_automation_schedules.py
```

### Fix Existing Schedules
```bash
python redistribute_shop_schedules.py
```

### Manual Trigger (Testing)
```bash
python trigger_daily_automation.py
```

### Start Celery Beat Scheduler
```bash
python celery_beat_config.py
```

## Configuration

### Enable Automation for a Shop
1. Go to the SEO Dashboard
2. Click "Settings"
3. Enable desired automations:
   - SEO Optimization
   - Collection Creation
   - Translation

### Modify Schedule Timing
```python
# In app/models/access_control.py
start_time = 4 * 60  # 4:00 AM in minutes
interval = 20        # 20-minute intervals
```

## Troubleshooting

### Common Issues

1. **Duplicate Tasks**
   - Ensure only one Celery Beat instance is running
   - Check for conflicting schedule entries

2. **Missing Schedules**
   - Verify shops have `automation_enabled=True`
   - Check that automation features are enabled in AIConfig

3. **Scheduling Conflicts**
   - Run `redistribute_shop_schedules.py` to fix intervals
   - Check for manual schedule overrides

### Verification Commands

```bash
# Check schedule status
python check_automation_schedules.py

# Verify Celery Beat is loading schedules
tail -f celery_beat.log

# Check Redis for scheduled tasks
redis-cli keys "*celery*"
```

## Benefits of New System

1. **Predictable Timing**: Each shop runs at exactly the same time daily
2. **No Conflicts**: 20-minute intervals prevent resource conflicts
3. **Scalable**: Can handle 60+ shops (20 hours of scheduling)
4. **Automatic**: New shops get scheduled automatically
5. **Quota Aware**: Integrates with existing quota system
6. **Maintainable**: Clear separation of concerns

## Migration from Old System

If migrating from the old system:

1. **Stop Celery Beat**: `pkill -f celery.*beat`
2. **Redistribute Schedules**: `python redistribute_shop_schedules.py`
3. **Verify Configuration**: `python check_automation_schedules.py`
4. **Restart Celery Beat**: `python celery_beat_config.py`

## Monitoring

### Key Metrics to Monitor
- Schedule distribution (20-minute intervals)
- Task execution success rates
- Queue lengths and processing times
- Shop automation completion rates

### Log Files
- `celery_beat.log`: Schedule loading and task queuing
- `celery_worker.log`: Task execution details
- Application logs: Automation results and errors

## Future Enhancements

1. **Dynamic Interval Adjustment**: Adjust intervals based on task duration
2. **Priority Scheduling**: High-priority shops get earlier slots
3. **Load Balancing**: Distribute across multiple time zones
4. **Health Monitoring**: Automatic detection of failed schedules
