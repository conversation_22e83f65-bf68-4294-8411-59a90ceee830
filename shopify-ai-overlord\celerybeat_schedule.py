"""
Celery Beat Schedule Configuration
Defines periodic tasks for the application with dynamic per-store scheduling
"""
from celery.schedules import crontab
from datetime import timed<PERSON><PERSON>


def get_dynamic_schedule():
    """Generate dynamic schedule based on UserShop automation settings with 20-minute intervals"""
    schedule = {
        # Clean up old automation jobs every day at 1 AM (before store automations)
        'cleanup-old-jobs': {
            'task': 'cleanup_old_jobs',
            'schedule': crontab(hour=1, minute=0),
            'options': {
                'expires': 3600,
            }
        },
        # Fetch Search Console metrics at 3 AM GMT (after data updates)
        'fetch-search-console-metrics': {
            'task': 'fetch_search_console_metrics_all',
            'schedule': crontab(hour=3, minute=0),
            'options': {
                'expires': 3600,
            }
        }
    }

    try:
        from app.models.access_control import UserShop
        from app.models.ai_config import AIConfig

        # Get all user shops with automation enabled, ordered by shop_id for consistency
        user_shops = UserShop.query.filter_by(automation_enabled=True).order_by(UserShop.shop_id).all()

        print(f"Loading dynamic schedules for {len(user_shops)} shops with automation enabled")

        # Create a schedule entry for each store with enabled automations
        for user_shop in user_shops:
            # Check if this shop has any SEO automations enabled
            config = AIConfig.query.filter_by(
                shop_id=user_shop.shop_id,
                module_type='seo_automation'
            ).first()

            # Only schedule if shop has automation config with enabled features
            if config and config.config_data:
                config_data = config.config_data
                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])

                if has_enabled_automation:
                    schedule_key = f'daily-automation-shop-{user_shop.shop_id}'
                    schedule[schedule_key] = {
                        'task': 'daily_automation',
                        'schedule': crontab(
                            hour=user_shop.automation_hour,
                            minute=user_shop.automation_minute
                        ),
                        'args': (user_shop.shop_id,),
                        'options': {
                            'expires': 3600,  # Expire after 1 hour
                        }
                    }

                    print(f"Scheduled shop {user_shop.shop_id} at {user_shop.automation_hour:02d}:{user_shop.automation_minute:02d} UTC")

                    # Add alt text optimization if enabled for this shop
                    if getattr(user_shop, 'alt_text_enabled', False):
                        alt_text_key = f'daily-alt-text-shop-{user_shop.shop_id}'
                        # Run 30 minutes after main automation
                        alt_text_hour = user_shop.automation_hour
                        alt_text_minute = (user_shop.automation_minute + 30) % 60
                        if user_shop.automation_minute + 30 >= 60:
                            alt_text_hour = (alt_text_hour + 1) % 24

                        schedule[alt_text_key] = {
                            'task': 'daily_alt_text_optimization',
                            'schedule': crontab(
                                hour=alt_text_hour,
                                minute=alt_text_minute
                            ),
                            'args': (user_shop.shop_id, 25),  # Process up to 25 images daily
                            'options': {
                                'expires': 3600,
                            }
                        }

                        print(f"Scheduled alt-text for shop {user_shop.shop_id} at {alt_text_hour:02d}:{alt_text_minute:02d} UTC")
                else:
                    print(f"Shop {user_shop.shop_id} has no enabled automations, skipping schedule")
            else:
                print(f"Shop {user_shop.shop_id} has no automation config, skipping schedule")

    except Exception as e:
        # Fallback if database not available (initial setup)
        print(f"Warning: Could not load dynamic schedules: {e}")
        import traceback
        traceback.print_exc()

    print(f"Generated {len(schedule)} total scheduled tasks")
    return schedule


# Static schedule for backwards compatibility
CELERYBEAT_SCHEDULE = {
    # Clean up old automation jobs every day at 1 AM
    'cleanup-old-jobs': {
        'task': 'cleanup_old_jobs',
        'schedule': crontab(hour=1, minute=0),
        'options': {
            'expires': 3600,
        }
    },
    
    # REMOVED: check-automation-queues task was causing duplicate runs
    # Individual shop schedules are handled by dynamic scheduling above
}


def get_beat_schedule():
    """Get the Celery beat schedule configuration with dynamic per-store scheduling"""
    try:
        # Try to get dynamic schedule first
        return get_dynamic_schedule()
    except Exception as e:
        print(f"Falling back to static schedule: {e}")
        # Fallback to static schedule if dynamic fails
        return CELERYBEAT_SCHEDULE