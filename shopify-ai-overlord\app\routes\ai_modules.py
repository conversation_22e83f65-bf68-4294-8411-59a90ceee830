from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from app.auth import login_required, get_user_api_keys, get_current_user_settings, get_current_shop
from app import db
from app.models.ai_config import AIConfig
from app.models.automation_job import AutomationJob
from app.services.shopify_service import ShopifyService
# from app.services.claude_service import ClaudeService
import asyncio
import json
import os
from datetime import datetime

bp = Blueprint('ai_modules', __name__)


def get_shopify_service():
    """Helper function to get ShopifyService with user API keys"""
    api_keys = get_user_api_keys()
    if not api_keys.get('shopify_shop_domain') or not api_keys.get('shopify_access_token'):
        return None
    return ShopifyService(api_keys['shopify_shop_domain'], api_keys['shopify_access_token'])


def get_current_user_id():
    """Get the current user's shop ID - unified resolver for consistent ID usage"""
    from app.auth import get_current_user_settings
    from app.models.shop import Shop
    
    settings = get_current_user_settings()
    if not settings:
        return None
    
    # ALWAYS resolve to actual Shop ID for consistency
    shop = None
    
    # Try to find shop by shopify_shop_domain
    if settings.shopify_shop_domain:
        shop = Shop.query.filter_by(shopify_shop_domain=settings.shopify_shop_domain).first()
        if not shop:
            shop = Shop.query.filter_by(shop_domain=settings.shopify_shop_domain).first()
    
    # Create shop if it doesn't exist
    if not shop and settings.shopify_shop_domain and settings.shopify_access_token:
        shop = Shop(
            shop_domain=settings.shopify_shop_domain,
            shopify_shop_domain=settings.shopify_shop_domain,
            access_token=settings.shopify_access_token,
            shopify_access_token=settings.shopify_access_token,
            anthropic_api_key=settings.anthropic_api_key,
            shop_name=settings.shopify_shop_domain.split('.')[0],
            email=settings.access_key.email
        )
        db.session.add(shop)
        db.session.commit()
    
    # Update shop with latest API keys if needed
    if shop and (shop.shopify_access_token != settings.shopify_access_token or 
                 shop.anthropic_api_key != settings.anthropic_api_key):
        shop.shopify_access_token = settings.shopify_access_token
        shop.anthropic_api_key = settings.anthropic_api_key
        db.session.commit()
    
    return shop.id if shop else None

@bp.route('/seo')
@login_required
def seo():
    """SEO optimization module - redirect to SEO dashboard"""
    return redirect(url_for('ai_modules.seo_dashboard'))

@bp.route('/seo/dashboard')
@login_required
def seo_dashboard():
    """SEO Dashboard - One-stop shop for Shopify SEO automation"""
    from app.services.change_tracking_service import ChangeTrackingService
    from app.models.automation_job import AutomationJob
    from app.models.shop import Shop
    from app.models.access_control import UserSettings
    from app.services.quota_service import calculate_quota_with_deficit
    from app.models import GoogleOAuth
    import json
    from datetime import datetime, timedelta
    
    # Get current user's shop ID (now always returns actual shop ID)
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    shop = Shop.query.get(shop_id)
    
    # Force fresh database query for Google OAuth
    db.session.close()  # Close the session to ensure fresh data
    
    # Check if Google OAuth is connected with a fresh query
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
    
    # Get current user settings for API keys
    user_settings = get_current_user_settings()
    
    # Check if OpenAI is available for technical SEO features
    openai_available = bool(os.getenv('OPENAI_API_KEY')) or bool(user_settings.openai_api_key if user_settings else None)
    
    # Debug log
    print(f"[SEO Dashboard] Shop ID: {shop_id}, Google OAuth found: {bool(google_oauth)}, OpenAI available: {openai_available}")
    
    # Get statistics
    shopify_service = get_shopify_service()
    stats = {
        'products_optimized': 0,
        'total_products': 0,
        'products_percentage': 0,
        'collections_created': 0,
        'translations_completed': 0,
        'languages_count': 0,
        'tasks_today': 0,
        'tasks_remaining': 0,
        'daily_products': 0,
        'api_calls': 0
    }
    
    # Get product optimization stats
    if shopify_service:
        try:
            # Get count of products available on Online Store only
            stats['total_products'] = shopify_service.get_products_count(published_status='published')
            optimized_ids = ChangeTrackingService.get_all_optimized_product_ids(shop_id, 'seo')
            stats['products_optimized'] = len(optimized_ids)
            stats['products_percentage'] = int((stats['products_optimized'] / stats['total_products'] * 100)) if stats['total_products'] > 0 else 0
        except:
            pass
    
    # Get collections created
    collection_tags = ChangeTrackingService.get_collections_created_from_tags(shop_id)
    stats['collections_created'] = len(collection_tags)
    
    # Get translation stats
    try:
        from app.models.change_log import TranslationStatus
        translations = TranslationStatus.query.filter_by(
            shop_id=shop_id,
            is_translated=True
        ).all()
        stats['translations_completed'] = len(translations)
        # Get unique languages
        languages = set(t.language for t in translations)
        stats['languages_count'] = len(languages)
    except:
        pass
    
    # Get today's automation activity
    today = datetime.utcnow().date()
    today_jobs = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.created_at >= today
    ).all()
    stats['tasks_today'] = len(today_jobs)
    stats['tasks_remaining'] = len([j for j in today_jobs if j.status == 'processing'])
    
    # Get recent jobs with better information - get more for client-side pagination
    recent_jobs = AutomationJob.query.filter_by(
        shop_id=shop_id
    ).order_by(AutomationJob.created_at.desc()).limit(50).all()
    
    # Format jobs for display
    formatted_jobs = []
    for job in recent_jobs:
        # Parse result data for better display
        result_summary = ''
        if job.result_data:
            try:
                data = json.loads(job.result_data)
                if job.task_type == 'bulk_product_seo':
                    result_summary = f"{data.get('optimized', 0)} products optimized, {data.get('failed', 0)} failed"
                elif job.task_type == 'bulk_create_collections':
                    result_summary = f"{data.get('created', 0)} collections created, {data.get('skipped', 0)} skipped"
                elif job.task_type == 'bulk_translate':
                    result_summary = f"{data.get('translated', 0)} items translated to {len(data.get('languages', []))} languages"
                elif job.task_type == 'daily_alt_text_optimization':
                    queued = data.get('products_queued', 0)
                    estimated = data.get('estimated_images_to_update', 0)
                    failed = data.get('products_failed', 0)
                    result_summary = f"{queued} products queued, ~{estimated} images to optimize"
                    if failed > 0:
                        result_summary += f", {failed} failed"
                else:
                    result_summary = 'Completed successfully'
            except:
                result_summary = 'Details not available'
        elif job.status == 'failed':
            result_summary = job.error_message or 'Unknown error'
        
        # Calculate duration
        duration = 'N/A'
        if job.updated_at and job.created_at:
            duration_seconds = (job.updated_at - job.created_at).total_seconds()
            if duration_seconds < 60:
                duration = f"{int(duration_seconds)}s"
            elif duration_seconds < 3600:
                duration = f"{int(duration_seconds/60)}m"
            else:
                duration = f"{int(duration_seconds/3600)}h {int((duration_seconds%3600)/60)}m"
        
        formatted_jobs.append({
            'id': job.id,
            'task_type': job.task_type,
            'status': job.status,
            'created_at': job.created_at,
            'duration': duration,
            'result_summary': result_summary,
            'task_id': job.task_id,
            'icon': {
                'product_seo': 'box',
                'bulk_product_seo': 'boxes',
                'create_collection': 'layer-group',
                'bulk_create_collections': 'layer-group',
                'translate': 'language',
                'bulk_translate': 'globe'
            }.get(job.task_type, 'tasks')
        })
    
    # Get automation settings
    config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_automation'
    ).first()
    
    # Default settings
    settings = {
        'seo_enabled': False,
        'collection_enabled': False,
        'translation_enabled': False,
        'alt_text_enabled': False,
        'search_console_enabled': False,
        'daily_optimization_limit': 10,
        'seo_schedule': '02:00',
        'target_languages': [],
        'optimization_keywords': [],
        'api_limit': 5000,
        'rate_limit': 2,
        'daily_limit': 50
    }
    
    # Override with saved settings if they exist
    if config and config.config_data:
        import json
        config_data = config.config_data
        
        # Ensure config_data is a dictionary (handle cases where it might be a string)
        if isinstance(config_data, str):
            try:
                config_data = json.loads(config_data)
            except (json.JSONDecodeError, TypeError):
                config_data = {}
        elif not isinstance(config_data, dict):
            config_data = {}
            
        settings.update(config_data)
        # Settings loaded from config
    
    # Get available languages
    available_languages = []
    primary_locale = None
    if shopify_service:
        try:
            shop_locales = shopify_service.get_shop_locales()
            
            # Find primary locale and filter it out from available languages
            for locale in shop_locales:
                if locale.get('primary', False):
                    primary_locale = locale.get('locale')
                    break
            
            # If no primary flag, assume 'en' or the first locale is primary
            if not primary_locale and shop_locales:
                # Check for 'en' first
                for locale in shop_locales:
                    if locale.get('locale') == 'en':
                        primary_locale = 'en'
                        break
                # If no 'en', use the first locale
                if not primary_locale:
                    primary_locale = shop_locales[0].get('locale')
            
            # Only include published locales that are not the primary
            available_languages = [l for l in shop_locales 
                                 if l.get('published', False) and 
                                 l.get('locale') != primary_locale]
        except Exception as e:
            print(f"Error getting languages: {e}")
            pass
    
    # Get quota information
    quota_info = None
    if shopify_service:
        try:
            total_products = stats['total_products']
            quota_info = calculate_quota_with_deficit(shop_id, total_products)
        except Exception as e:
            print(f"Error calculating quota: {e}")
    
    # Check if automation is active
    automation_active = any([
        settings.get('seo_enabled'),
        settings.get('collection_enabled'),
        settings.get('translation_enabled')
    ])
    
    return render_template('ai_modules/seo/dashboard.html',
                         stats=stats,
                         recent_jobs=formatted_jobs,
                         settings=settings,
                         user_settings=user_settings,
                         available_languages=available_languages,
                         automation_active=automation_active,
                         quota_info=quota_info,
                         shop_domain=shop.shop_domain if shop else None,
                         google_oauth=google_oauth,
                         openai_available=openai_available)


@bp.route('/api/trigger-daily-automation', methods=['POST'])
@login_required
def trigger_daily_automation():
    """Manually trigger the daily automation task"""
    from app.tasks import daily_automation_task
    
    shop_id = get_current_user_id()
    if not shop_id:
        return jsonify({'success': False, 'error': 'Shop not configured'}), 400
    
    try:
        # Queue the daily automation task
        result = daily_automation_task.apply_async(
            args=[shop_id],
            countdown=5  # Start in 5 seconds
        )
        
        return jsonify({
            'success': True,
            'task_id': result.id,
            'message': 'Daily automation task queued successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/check-and-create-collections', methods=['POST'])
@login_required
def check_and_create_collections():
    """Manually check for new tags and create collections"""
    from app.tasks import check_and_create_collections_task
    from app.services.quota_service import check_quota_available
    
    shop_id = get_current_user_id()
    if not shop_id:
        return jsonify({'success': False, 'error': 'Shop not configured'}), 400
    
    try:
        # Check collection quota
        quota_available, remaining, message = check_quota_available(shop_id, 'collections', 1)
        if not quota_available:
            return jsonify({
                'success': False, 
                'error': f'Collection quota exceeded. {remaining} remaining today.'
            }), 400
        
        # Get automation config
        from app.models.ai_config import AIConfig
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type='seo_automation'
        ).first()
        
        seo_keywords = []
        if config and config.config_data:
            seo_keywords = config.config_data.get('optimization_keywords', [])
        
        # Queue the task
        result = check_and_create_collections_task.apply_async(
            args=[shop_id, remaining, seo_keywords],
            countdown=5  # Start in 5 seconds
        )
        
        return jsonify({
            'success': True,
            'task_id': result.id,
            'message': 'Collection check task queued',
            'quota_remaining': remaining
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/changes')
@login_required
def changes():
    """View and manage change history"""
    print("Changes page accessed")
    return render_template('ai_modules/changes.html')

@bp.route('/seo/products')
@login_required
def seo_products():
    """Product SEO status page - shows optimization status from automated system"""
    from app.services.change_tracking_service import ChangeTrackingService
    from app.models.automation_job import AutomationJob
    
    # Get current shop ID (unified resolver)
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    shopify_service = get_shopify_service()
    if not shopify_service:
        flash('Shopify API credentials not configured. Please set them in settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    try:
        # Get only first 250 products initially for faster page load - ONLY Online Store products
        products = shopify_service.get_products(limit=250, published_status='published')
        # Get total count for the UI - ONLY Online Store products
        total_count = shopify_service.get_products_count(published_status='published')
    except Exception as e:
        # Handle API errors gracefully
        if '401' in str(e) or 'Unauthorized' in str(e):
            flash('Invalid Shopify API credentials. Please check your access token in settings.', 'error')
            return redirect(url_for('auth.settings'))
        else:
            flash(f'Error connecting to Shopify: {str(e)}', 'error')
            products = []
            total_count = 0
    
    # Get optimization statuses for these products
    optimization_statuses = {}
    optimization_details = {}
    
    for product in products:
        product_id = str(product['id'])
        status = ChangeTrackingService.get_optimization_status(
            shop_id=shop_id,
            resource_type='product',
            resource_id=product_id,
            optimization_type='seo'
        )
        
        if status and status.is_optimized:
            optimization_statuses[product_id] = True
            
            # Get optimization details from change log
            changes = ChangeTrackingService.get_change_history(
                shop_id=shop_id,
                resource_type='product',
                resource_id=product_id,
                limit=1
            )
            
            if changes:
                change = changes[0]
                optimization_details[product_id] = {
                    'optimized_at': status.optimized_at.strftime('%Y-%m-%d %H:%M') if status.optimized_at else 'N/A',
                    'optimized_by': status.optimized_by or 'Claude 3.5 Sonnet',
                    'new_tags': change.change_metadata.get('new_tags', []) if change.change_metadata else [],
                    'improvements': 'Title, Description, Tags optimized'
                }
        else:
            optimization_statuses[product_id] = False
    
    # Get processing count from active jobs
    processing_jobs = AutomationJob.query.filter_by(
        shop_id=shop_id,
        task_type='product_seo',
        status='processing'
    ).count()
    
    # Calculate statistics using the unified shop ID
    all_statuses = ChangeTrackingService.get_all_optimization_statuses(
        shop_id=shop_id,
        resource_type='product',
        optimization_type='seo'
    )
    optimized_count = sum(1 for status in all_statuses if status.is_optimized)
    pending_count = total_count - optimized_count - processing_jobs
    
    print(f"Shop {shop_id}: {total_count} total, {optimized_count} optimized, {processing_jobs} processing, {pending_count} pending")
    
    config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_products'
    ).first()
    
    return render_template('ai_modules/seo/products_status.html', 
                         config=config, 
                         products=products,
                         total_count=total_count,
                         optimization_statuses=optimization_statuses,
                         optimization_details=optimization_details,
                         optimized_count=optimized_count,
                         processing_count=processing_jobs,
                         pending_count=pending_count)

@bp.route('/seo/collections')
@login_required
def seo_collections():
    """Collection status page - shows collections created from AI-generated tags"""
    from app.services.change_tracking_service import ChangeTrackingService
    from app.models.automation_job import AutomationJob
    from datetime import datetime, timedelta, timezone
    
    # Get current shop ID (unified resolver)
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    shopify_service = get_shopify_service()
    if not shopify_service:
        flash('Shopify API credentials not configured. Please set them in settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    # Get shop domain for links
    api_keys = get_user_api_keys()
    shop_domain = api_keys.get('shopify_shop_domain', '')
    
    try:
        # Get collections count
        total_collections_count = shopify_service.get_collections_count()
    except Exception as e:
        total_collections_count = 0
    
    # Get all AI-generated tags from optimizations (last 30 days)
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    all_changes = ChangeTrackingService.get_change_history(
        shop_id=shop_id,
        resource_type='product',
        limit=1000  # Get more history
    )
    
    # Extract all AI-generated tags
    all_ai_tags = set()
    for change in all_changes:
        if (change.action == 'optimize_seo' and 
            change.created_at >= thirty_days_ago and
            change.change_metadata and
            change.change_metadata.get('new_tags')):
            new_tags = change.change_metadata.get('new_tags', [])
            if isinstance(new_tags, list):
                all_ai_tags.update(new_tags)
    
    # Get tags that already have collections created
    tags_with_collections = ChangeTrackingService.get_collections_created_from_tags(shop_id)
    
    # Get collection creation history
    collection_changes = ChangeTrackingService.get_change_history(
        shop_id=shop_id,
        resource_type='collection',
        limit=100
    )
    
    # Get products ONCE to analyze tags
    try:
        products = shopify_service.get_products(limit=250, published_status='published')
    except:
        products = []
    
    # Build tag counts from products
    tag_product_counts = {}
    for product in products:
        if product.get('tags'):
            tags = [tag.strip() for tag in product['tags'].split(',')]
            for tag in tags:
                if tag:
                    tag_product_counts[tag] = tag_product_counts.get(tag, 0) + 1
    
    # Fetch actual collections from Shopify to get current status
    actual_collections = {}
    try:
        all_shopify_collections = shopify_service.get_collections(limit=250)
        for coll in all_shopify_collections:
            actual_collections[str(coll.get('id'))] = coll
    except Exception as e:
        print(f"Error fetching collections from Shopify: {e}")
    
    # Build created collections data
    created_collections = []
    for change in collection_changes:
        if change.action == 'create' and change.status == 'completed':
            collection_id = str(change.resource_id)
            
            # Get actual collection data from Shopify if available
            actual_collection = actual_collections.get(collection_id, {})
            
            collection_data = {
                'id': change.resource_id,
                'title': actual_collection.get('title') or change.after_data.get('title', 'Unknown'),
                'handle': actual_collection.get('handle') or change.after_data.get('handle', ''),
                'description': actual_collection.get('body_html') or change.after_data.get('body_html', ''),
                'created_from_tag': change.change_metadata.get('created_from_tag', 'Unknown') if change.change_metadata else 'Unknown',
                'collection_type': change.change_metadata.get('collection_type', 'smart') if change.change_metadata else 'smart',
                'created_at': change.created_at.strftime('%Y-%m-%d'),
                'published_at': actual_collection.get('published_at'),  # Get actual published status
                'published': actual_collection.get('published', True),  # Collections are published by default
                'products_count': tag_product_counts.get(change.change_metadata.get('created_from_tag', '') if change.change_metadata else '', 0)
            }
            
            created_collections.append(collection_data)
    
    # Calculate pending tags (AI-generated but no collection yet)
    pending_tags = []
    for tag in all_ai_tags:
        if tag not in tags_with_collections:
            # Get count from our pre-built dictionary
            count = tag_product_counts.get(tag, 0)
            if count > 0:
                pending_tags.append((tag, count))
    
    # Sort pending tags by product count
    pending_tags.sort(key=lambda x: x[1], reverse=True)
    
    # Get processing jobs count
    processing_jobs = AutomationJob.query.filter_by(
        shop_id=shop_id,
        task_type='create_collection',
        status='processing'
    ).count()
    
    config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_collections'
    ).first()
    
    return render_template('ai_modules/seo/collections_status.html', 
                         config=config,
                         shop_domain=shop_domain,
                         created_collections=created_collections,
                         pending_tags=pending_tags,
                         available_tags_count=len(all_ai_tags),
                         collections_created=len(tags_with_collections),
                         tags_pending=len(pending_tags),
                         total_collections=total_collections_count,
                         processing_count=processing_jobs)

@bp.route('/seo/languages')
@login_required
def seo_languages():
    """Translation status page - shows translation progress from automated system"""
    from app.services.change_tracking_service import ChangeTrackingService
    from app.models.change_log import TranslationStatus
    from app.models.automation_job import AutomationJob
    
    # Get current shop ID (unified resolver)
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    shopify_service = get_shopify_service()
    if not shopify_service:
        flash('Shopify API credentials not configured. Please set them in settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    # Get shop domain from user settings
    api_keys = get_user_api_keys()
    shop_domain = api_keys.get('shopify_shop_domain', '')
    
    try:
        # Get resource counts
        total_products = shopify_service.get_products_count()
        total_collections = shopify_service.get_collections_count()
        total_pages = shopify_service.get_pages_count()
    except Exception as e:
        total_products = 0
        total_collections = 0
        total_pages = 0
    
    total_resources = total_products + total_collections + total_pages
    resource_counts = {
        'products': total_products,
        'collections': total_collections,
        'pages': total_pages
    }
    
    # Get enabled languages from shop
    shop_locales = []
    primary_locale = None
    try:
        all_locales = shopify_service.get_shop_locales()
        
        # Find primary locale
        for locale in all_locales:
            if locale.get('primary', False):
                primary_locale = locale.get('locale')
                break
                
        # Filter to only show published (enabled) languages that are not primary
        shop_locales = [locale for locale in all_locales 
                       if locale.get('published', False) and 
                       locale.get('locale') != primary_locale]
    except Exception as e:
        print(f"Error getting shop locales: {e}")
    
    # Get translation statistics for each language
    translation_stats = {}
    total_translated = 0
    
    for locale in shop_locales:
        lang_code = locale.get('locale')
        
        # Get all translations for this language
        translations = TranslationStatus.query.filter_by(
            shop_id=shop_id,
            language=lang_code,
            is_translated=True
        ).all()
        
        # Count by resource type
        products_translated = sum(1 for t in translations if t.resource_type == 'product')
        collections_translated = sum(1 for t in translations if t.resource_type == 'collection')
        pages_translated = sum(1 for t in translations if t.resource_type == 'page')
        
        total_for_lang = len(translations)
        total_translated += total_for_lang
        
        # Get recent translations with details
        recent_translations = []
        for trans in translations[:5]:  # Get last 5
            title = 'Unknown'
            
            # Try to get the title from Shopify based on resource type
            try:
                if trans.resource_type == 'product':
                    product = shopify_service.get_product(trans.resource_id)
                    if product:
                        title = product.get('title', 'Unknown')
                elif trans.resource_type == 'collection':
                    # Try to get collection by iterating through all collections
                    collections = shopify_service.get_collections()
                    for collection in collections:
                        if str(collection.get('id')) == str(trans.resource_id):
                            title = collection.get('title', 'Unknown')
                            break
                elif trans.resource_type == 'page':
                    pages = shopify_service.get_online_store_pages()
                    for page in pages:
                        if str(page.get('id')) == str(trans.resource_id):
                            title = page.get('title', 'Unknown')
                            break
            except Exception as e:
                print(f"Error getting title for {trans.resource_type} {trans.resource_id}: {e}")
                # Fall back to change log
                changes = ChangeTrackingService.get_change_history(
                    shop_id=shop_id,
                    resource_type=trans.resource_type,
                    resource_id=trans.resource_id,
                    limit=1
                )
                if changes and changes[0].after_data:
                    title = changes[0].after_data.get('title', 'Unknown')
            
            recent_translations.append({
                'title': title,
                'type': trans.resource_type,
                'type_icon': {
                    'product': 'box',
                    'collection': 'layer-group',
                    'page': 'file-alt'
                }.get(trans.resource_type, 'file'),
                'translated_at': trans.translated_at.strftime('%Y-%m-%d') if trans.translated_at else 'N/A'
            })
        
        translation_stats[lang_code] = {
            'translated': total_for_lang,
            'percentage': int((total_for_lang / total_resources * 100)) if total_resources > 0 else 0,
            'products': products_translated,
            'collections': collections_translated,
            'pages': pages_translated,
            'recent_translations': recent_translations
        }
    
    # Get processing jobs count
    processing_jobs = AutomationJob.query.filter_by(
        shop_id=shop_id,
        task_type='translate',
        status='processing'
    ).count()
    
    # Locale display names and flags
    locale_names = {
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt-BR': 'Portuguese (Brazil)',
        'pt-PT': 'Portuguese (Portugal)',
        'nl': 'Dutch',
        'ja': 'Japanese',
        'ko': 'Korean',
        'zh-CN': 'Chinese (Simplified)',
        'zh-TW': 'Chinese (Traditional)',
        'ar': 'Arabic',
        'he': 'Hebrew',
        'ru': 'Russian',
        'pl': 'Polish',
        'tr': 'Turkish',
        'sv': 'Swedish',
        'da': 'Danish',
        'fi': 'Finnish',
        'nb': 'Norwegian',
        'cs': 'Czech',
        'hu': 'Hungarian',
        'ro': 'Romanian',
        'el': 'Greek',
        'th': 'Thai',
        'vi': 'Vietnamese',
        'id': 'Indonesian',
        'ms': 'Malay',
        'hi': 'Hindi'
    }
    
    locale_flags = {
        'en': '🇬🇧',
        'es': '🇪🇸',
        'fr': '🇫🇷',
        'de': '🇩🇪',
        'it': '🇮🇹',
        'pt': '🇵🇹',
        'nl': '🇳🇱',
        'ja': '🇯🇵',
        'ko': '🇰🇷',
        'zh': '🇨🇳',
        'ar': '🇸🇦',
        'he': '🇮🇱',
        'ru': '🇷🇺',
        'pl': '🇵🇱',
        'tr': '🇹🇷',
        'sv': '🇸🇪',
        'da': '🇩🇰',
        'fi': '🇫🇮',
        'nb': '🇳🇴',
        'cs': '🇨🇿',
        'hu': '🇭🇺',
        'ro': '🇷🇴',
        'el': '🇬🇷',
        'th': '🇹🇭',
        'vi': '🇻🇳',
        'id': '🇮🇩',
        'ms': '🇲🇾',
        'hi': '🇮🇳'
    }
    
    config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_languages'
    ).first()
    
    return render_template('ai_modules/seo/languages_status.html', 
                         config=config,
                         shop_domain=shop_domain,
                         shop_locales=shop_locales,
                         locale_names=locale_names,
                         locale_flags=locale_flags,
                         total_resources=total_resources,
                         resource_counts=resource_counts,
                         translation_stats=translation_stats,
                         total_translated=total_translated,
                         languages_active=len(shop_locales),
                         processing_count=processing_jobs)

@bp.route('/seo/homepage')
@login_required
def seo_homepage():
    """Homepage SEO optimization"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='seo_homepage'
    ).first()
    
    # Get shop domain from user settings
    api_keys = get_user_api_keys()
    shop_domain = api_keys.get('shopify_shop_domain', '')
    
    return render_template('ai_modules/seo/homepage.html', config=config, shop_domain=shop_domain)

@bp.route('/seo/technical')
@login_required
def seo_technical():
    """Technical SEO optimization including alt text optimization"""
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    # Get technical SEO config
    config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_technical'
    ).first()
    
    # Get current user settings for API key availability
    user_settings = get_current_user_settings()
    
    # Check if OpenAI is available for alt text optimization
    openai_available = bool(os.getenv('OPENAI_API_KEY')) or bool(user_settings.openai_api_key if user_settings else None)
    
    # Get comprehensive alt text optimization statistics (regardless of OpenAI availability)
    shopify_service = get_shopify_service()
    alt_text_stats = {
        'images_without_alt_text': 0,
        'total_images': 0,
        'sources_checked': 0,
        'completion_percentage': 0,
        'breakdown_by_type': {
            'products': {'total': 0, 'missing_alt': 0},
            'collections': {'total': 0, 'missing_alt': 0},
            'pages': {'total': 0, 'missing_alt': 0},
            'blog_posts': {'total': 0, 'missing_alt': 0}
        }
    }
    
    if shopify_service:
        try:
            # Get comprehensive image data from all sources
            all_images = shopify_service.get_all_images_for_alt_text_optimization(limit=100)
            
            # Count total images and breakdown by type
            total_images_checked = 0
            images_without_alt = 0
            
            for source in all_images:
                source_type = source.get('type', 'unknown')
                total_for_source = source.get('total_images', 0)
                missing_for_source = source.get('images_without_alt', 0)
                
                total_images_checked += total_for_source
                images_without_alt += missing_for_source
                
                # Update breakdown
                if source_type in alt_text_stats['breakdown_by_type']:
                    alt_text_stats['breakdown_by_type'][source_type]['total'] += total_for_source
                    alt_text_stats['breakdown_by_type'][source_type]['missing_alt'] += missing_for_source
            
            alt_text_stats.update({
                'images_without_alt_text': images_without_alt,
                'total_images': total_images_checked,
                'sources_checked': len(all_images),
                'completion_percentage': round((1 - images_without_alt / max(total_images_checked, 1)) * 100, 1) if total_images_checked > 0 else 100
            })
        except Exception as e:
            print(f"Error getting comprehensive alt text stats: {e}")
    
    # Get recent alt text optimization jobs (both individual and daily tasks)
    recent_jobs = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['alt_text_optimization', 'daily_alt_text_optimization'])
    ).order_by(AutomationJob.created_at.desc()).limit(20).all()
    
    # Calculate additional statistics
    total_images_optimized_today = 0
    total_api_calls_today = 0
    recent_changes = []
    
    for job in recent_jobs:
        if job.created_at.date() == datetime.now().date() and job.result_data:
            try:
                result = json.loads(job.result_data)
                if job.task_type == 'alt_text_optimization':
                    total_images_optimized_today += result.get('successful_updates', 0)
                    total_api_calls_today += result.get('vision_api_calls', 0)
                    
                    # Extract recent changes for display
                    updates = result.get('updates', [])
                    for update in updates:
                        recent_changes.append({
                            'type': 'alt_text_update',
                            'timestamp': job.created_at,
                            'product_title': result.get('product_title', 'Unknown Product'),
                            'before': update.get('before_alt_text', ''),
                            'after': update.get('after_alt_text', ''),
                            'image_url': update.get('image_url', ''),
                            'shopify_url': update.get('shopify_edit_url', ''),
                            'job_id': job.id
                        })
            except:
                pass
    
    # Add to stats
    alt_text_stats.update({
        'images_optimized_today': total_images_optimized_today,
        'api_calls_today': total_api_calls_today,
        'recent_changes': recent_changes[:10]  # Show last 10 changes
    })
    
    # Get shop domain from user settings for Shopify links
    api_keys = get_user_api_keys()
    shop_domain = api_keys.get('shopify_shop_domain', '')
    
    return render_template('ai_modules/seo/technical.html', 
                         config=config, 
                         alt_text_stats=alt_text_stats,
                         recent_jobs=recent_jobs,
                         openai_available=openai_available,
                         shop_domain=shop_domain)

@bp.route('/seo/technical/start-alt-text-optimization', methods=['POST'])
@login_required
def start_alt_text_optimization():
    """Start manual alt text optimization"""
    print("=== START ALT TEXT OPTIMIZATION CALLED ===")
    
    from app.tasks import daily_alt_text_optimization_task
    
    shop = get_current_shop()
    if not shop:
        print("ERROR: Shop not found")
        return jsonify({'success': False, 'error': 'Shop not found'})
    
    shop_id = shop.id
    print(f"Shop ID: {shop_id}")
    
    # Check if OpenAI is configured for this user
    user_settings = get_current_user_settings()
    if not user_settings or not user_settings.openai_api_key:
        print("ERROR: OpenAI API key not configured")
        return jsonify({
            'success': False, 
            'error': 'OpenAI API key not configured. Please add your OpenAI API key in Settings.'
        })
    
    print("OpenAI API key found, starting task...")
    
    try:
        # Start the alt text optimization task
        result = daily_alt_text_optimization_task.apply_async(
            args=[shop_id, 25]  # Process up to 25 images
        )
        
        print(f"Task started successfully with ID: {result.id}")
        
        return jsonify({
            'success': True,
            'task_id': result.id,
            'message': 'Alt text optimization started. Processing up to 25 images.'
        })
        
    except Exception as e:
        print(f"ERROR starting task: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to start alt text optimization: {str(e)}'
        })

@bp.route('/seo/search-console')
@login_required
def seo_search_console():
    """Search Console - Track optimized URLs and their performance"""
    from app.services.change_tracking_service import ChangeTrackingService
    from app.models.change_log import ChangeLog
    from app.models import GoogleOAuth, SearchConsoleMetrics
    
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('auth.settings'))
    
    # Force fresh database query
    db.session.expire_all()
    
    # Check if Google OAuth is connected
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
    
    # Get ShopifyService for building URLs
    shopify_service = get_shopify_service()
    shop_domain = None
    if shopify_service:
        # Get shop domain from the API keys (more reliable)
        api_keys = get_user_api_keys()
        shop_domain = api_keys.get('shopify_shop_domain', '')
    
    # Get all optimized products and their handles
    optimized_urls = []
    
    # Get optimized products
    product_changes = ChangeLog.query.filter_by(
        shop_id=shop_id,
        resource_type='product',
        action='optimize_seo'
    ).order_by(ChangeLog.created_at.desc()).all()
    
    for change in product_changes:
        if change.after_data:
            # Extract product handle from after_data
            handle = None
            title = change.after_data.get('title', 'Unknown')
            
            # Try to get handle from the product data
            if change.resource_id:
                # We need to fetch the product to get its handle
                if shopify_service:
                    try:
                        product = shopify_service.get_product(change.resource_id)
                        if product:
                            handle = product.get('handle')
                    except:
                        pass
            
            if handle:
                optimized_urls.append({
                    'type': 'Product',
                    'title': title,
                    'handle': handle,
                    'url': f"/products/{handle}",
                    'full_url': f"https://{shop_domain}/products/{handle}" if shop_domain else None,
                    'date_optimized': change.created_at,
                    'languages': []  # We'll populate this later
                })
    
    # Get created collections and their handles
    collection_changes = ChangeLog.query.filter_by(
        shop_id=shop_id,
        resource_type='collection',
        action='create'
    ).order_by(ChangeLog.created_at.desc()).all()
    
    for change in collection_changes:
        if change.after_data:
            handle = change.after_data.get('handle')
            title = change.after_data.get('title', 'Unknown')
            
            if handle:
                optimized_urls.append({
                    'type': 'Collection',
                    'title': title,
                    'handle': handle,
                    'url': f"/collections/{handle}",
                    'full_url': f"https://{shop_domain}/collections/{handle}" if shop_domain else None,
                    'date_optimized': change.created_at,
                    'languages': []
                })
    
    # Get translation status and create separate entries for translated pages
    from app.models.change_log import TranslationStatus
    translated_urls = []
    
    # Build a map of resource IDs for quick lookup
    resource_map = {}
    for change in product_changes:
        if change.after_data:
            resource_map[('product', change.resource_id)] = {
                'title': change.after_data.get('title', 'Unknown'),
                'handle': None  # Will be fetched if needed
            }
    
    for change in collection_changes:
        if change.after_data:
            resource_map[('collection', change.resource_id)] = {
                'title': change.after_data.get('title', 'Unknown'),
                'handle': change.after_data.get('handle')
            }
    
    # Get all translations for this shop
    all_translations = TranslationStatus.query.filter_by(
        shop_id=shop_id,
        is_translated=True
    ).all()
    
    # Group translations by resource
    for trans in all_translations:
        resource_key = (trans.resource_type, trans.resource_id)
        if resource_key in resource_map:
            base_data = resource_map[resource_key]
            
            # Get handle if we don't have it yet (for products)
            handle = base_data['handle']
            if not handle and trans.resource_type == 'product' and shopify_service:
                try:
                    product = shopify_service.get_product(trans.resource_id)
                    if product:
                        handle = product.get('handle')
                        base_data['handle'] = handle  # Cache it
                except:
                    pass
            
            if handle:
                # Get translated handle if available
                translated_handle = handle  # Default to English handle
                if hasattr(trans, 'translated_data') and trans.translated_data and 'handle' in trans.translated_data:
                    translated_handle = trans.translated_data['handle']
                
                # Get translated title if available
                translated_title = base_data['title']
                if hasattr(trans, 'translated_data') and trans.translated_data and 'title' in trans.translated_data:
                    translated_title = trans.translated_data['title']
                
                # Add translated URL entry
                lang_code = trans.language.lower()
                translated_urls.append({
                    'type': trans.resource_type.title(),
                    'title': f"{translated_title} ({trans.language.upper()})",
                    'handle': translated_handle,
                    'url': f"/{lang_code}/{trans.resource_type}s/{translated_handle}",
                    'full_url': f"https://{shop_domain}/{lang_code}/{trans.resource_type}s/{translated_handle}" if shop_domain else None,
                    'date_optimized': trans.translated_at,
                    'languages': [trans.language],
                    'is_translation': True,
                    'base_language': 'en'
                })
    
    # Add language info to base URLs
    for url_data in optimized_urls:
        # Find matching resource ID
        resource_id = None
        if url_data['type'] == 'Product':
            for change in product_changes:
                if change.after_data and change.after_data.get('title') == url_data['title']:
                    resource_id = change.resource_id
                    break
        else:  # Collection
            for change in collection_changes:
                if change.after_data and change.after_data.get('handle') == url_data['handle']:
                    resource_id = change.resource_id
                    break
        
        if resource_id:
            translations = TranslationStatus.query.filter_by(
                shop_id=shop_id,
                resource_type=url_data['type'].lower(),
                resource_id=str(resource_id),
                is_translated=True
            ).all()
            url_data['languages'] = ['en'] + [trans.language for trans in translations]
            url_data['is_translation'] = False
            url_data['base_language'] = 'en'
    
    # Combine all URLs
    all_urls = optimized_urls + translated_urls
    
    # Get latest metrics for each URL
    for url_data in all_urls:
        url_path = url_data['url']
        latest_metrics = SearchConsoleMetrics.query.filter_by(
            shop_id=shop_id,
            url=url_path
        ).order_by(SearchConsoleMetrics.metrics_date.desc()).first()
        
        if latest_metrics:
            url_data['metrics'] = {
                'clicks': latest_metrics.clicks,
                'impressions': latest_metrics.impressions,
                'ctr': latest_metrics.ctr,
                'position': latest_metrics.position,
                'clicks_change': latest_metrics.clicks_change,
                'impressions_change': latest_metrics.impressions_change,
                'has_data': True
            }
        else:
            url_data['metrics'] = {
                'clicks': 0,
                'impressions': 0,
                'ctr': 0,
                'position': 0,
                'clicks_change': 0,
                'impressions_change': 0,
                'has_data': False
            }
    
    # Sort by date (newest first)
    all_urls.sort(key=lambda x: x['date_optimized'], reverse=True)
    
    return render_template('ai_modules/seo/search_console.html', 
                         urls=all_urls, 
                         shop_domain=shop_domain,
                         total_urls=len(all_urls),
                         google_oauth=google_oauth)


@bp.route('/seo/search-console/data')
@login_required
def seo_search_console_data():
    """Get Search Console performance data via API"""
    from app.services.search_console_service import SearchConsoleService
    from datetime import datetime, timedelta
    
    shop_id = get_current_user_id()
    if not shop_id:
        return jsonify({'error': 'Shop not found'}), 404
    
    # Check if Google OAuth is connected
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
    if not google_oauth:
        return jsonify({'error': 'Google account not connected'}), 401
    
    try:
        # Initialize Search Console service
        service = SearchConsoleService(shop_id)
        
        # Get the Search Console property URL
        site_url = service.get_default_property()
        if not site_url:
            # Fall back to shop domain
            api_keys = get_user_api_keys()
            shop_domain = api_keys.get('shopify_shop_domain', '')
            if not shop_domain:
                return jsonify({'error': 'No Search Console property configured. Please set your Store Live URL in settings.'}), 400
            site_url = f"https://{shop_domain}/"
        
        # Get date range (last 28 days by default)
        days = request.args.get('days', 28, type=int)
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)
        
        # Get dimension from query params
        dimension = request.args.get('dimension', 'query')
        
        # Query search analytics
        analytics = service.query_search_analytics(
            site_url=site_url,
            start_date=start_date,
            end_date=end_date,
            dimensions=[dimension],
            row_limit=100
        )
        
        return jsonify({
            'success': True,
            'data': analytics,
            'site_url': site_url,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            }
        })
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': f'Search Console API error: {str(e)}'}), 500

@bp.route('/ads')
@login_required
def ads():
    """Ads management module - redirect to google"""
    return redirect(url_for('ai_modules.ads_google'))

# Ads Sub-modules
@bp.route('/ads/google')
@login_required
def ads_google():
    """Google Ads management"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='ads_google'
    ).first()
    return render_template('ai_modules/ads/google.html', config=config)

@bp.route('/ads/facebook')
@login_required
def ads_facebook():
    """Facebook/Meta Ads management"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='ads_facebook'
    ).first()
    return render_template('ai_modules/ads/facebook.html', config=config)

@bp.route('/ads/tiktok')
@login_required
def ads_tiktok():
    """TikTok Ads management"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='ads_tiktok'
    ).first()
    return render_template('ai_modules/ads/tiktok.html', config=config)

@bp.route('/ads/analytics')
@login_required
def ads_analytics():
    """Ad Analytics dashboard"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='ads_analytics'
    ).first()
    return render_template('ai_modules/ads/analytics.html', config=config)

@bp.route('/email-marketing')
@login_required
def email_marketing():
    """Email marketing module - redirect to campaigns"""
    return redirect(url_for('ai_modules.email_campaigns'))

# Email Marketing Sub-modules
@bp.route('/email/campaigns')
@login_required
def email_campaigns():
    """Email campaigns"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='email_campaigns'
    ).first()
    return render_template('ai_modules/email/campaigns.html', config=config)

@bp.route('/email/automation')
@login_required
def email_automation():
    """Email automation flows"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='email_automation'
    ).first()
    return render_template('ai_modules/email/automation.html', config=config)

@bp.route('/email/segments')
@login_required
def email_segments():
    """Customer segmentation"""
    shopify_service = get_shopify_service()
    if not shopify_service:
        flash('Shopify API credentials not configured. Please set them in settings.', 'error')
        return redirect(url_for('auth.settings'))
    customers = shopify_service.get_customers(limit=50)
    
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='email_segments'
    ).first()
    return render_template('ai_modules/email/segments.html', 
                         config=config,
                         customers=customers)

@bp.route('/email/templates')
@login_required
def email_templates():
    """Email templates"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='email_templates'
    ).first()
    return render_template('ai_modules/email/templates.html', config=config)

@bp.route('/customer-support')
@login_required
def customer_support():
    """Customer support/chatbot module - redirect to chatbot"""
    return redirect(url_for('ai_modules.support_chatbot'))

# Customer Support Sub-modules
@bp.route('/support/chatbot')
@login_required
def support_chatbot():
    """AI Chatbot configuration"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='support_chatbot'
    ).first()
    return render_template('ai_modules/support/chatbot.html', config=config)

@bp.route('/support/tickets')
@login_required
def support_tickets():
    """Ticket management"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='support_tickets'
    ).first()
    return render_template('ai_modules/support/tickets.html', config=config)

@bp.route('/support/faqs')
@login_required
def support_faqs():
    """FAQs and Knowledge Base"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='support_faqs'
    ).first()
    return render_template('ai_modules/support/faqs.html', config=config)

@bp.route('/support/insights')
@login_required
def support_insights():
    """Customer insights"""
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type='support_insights'
    ).first()
    return render_template('ai_modules/support/insights.html', config=config)

@bp.route('/module/<module_type>/toggle', methods=['POST'])
@login_required
def toggle_module(module_type):
    """Enable/disable AI module"""
    if module_type not in ['seo', 'ads', 'email', 'support']:
        return jsonify({'error': 'Invalid module type'}), 400
    
    # Get current user settings
    user_settings = get_current_user_settings()
    if not user_settings:
        return jsonify({'error': 'User settings not found'}), 404
    
    # Update module settings
    if module_type == 'seo':
        user_settings.seo_enabled = not getattr(user_settings, 'seo_enabled', False)
    elif module_type == 'ads':
        user_settings.ads_enabled = not getattr(user_settings, 'ads_enabled', False)
    elif module_type == 'email':
        user_settings.email_marketing_enabled = not getattr(user_settings, 'email_marketing_enabled', False)
    elif module_type == 'support':
        user_settings.customer_support_enabled = not getattr(user_settings, 'customer_support_enabled', False)
    
    db.session.commit()
    
    return jsonify({'success': True})

@bp.route('/module/<module_type>/config', methods=['POST'])
@login_required
def update_config(module_type):
    """Update AI module configuration"""
    if module_type not in ['seo', 'ads', 'email', 'support']:
        return jsonify({'error': 'Invalid module type'}), 400
    
    config_data = request.get_json()
    
    config = AIConfig.query.filter_by(
        shop_id=get_current_user_id(),
        module_type=module_type
    ).first()
    
    if not config:
        config = AIConfig(
            shop_id=get_current_user_id(),
            module_type=module_type
        )
        db.session.add(config)
    
    config.config_data = config_data
    db.session.commit()
    
    return jsonify({'success': True})

# API endpoints for Claude AI interactions
@bp.route('/api/ai/optimize-product-seo', methods=['POST'])
@login_required
def optimize_product_seo():
    """Use Claude to optimize product SEO with real web search"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        product_id = data.get('product_id')
        product_title = data.get('title', '')
        product_description = data.get('description', '')
        keywords = data.get('keywords', '').split(',') if data.get('keywords') else []
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Get optimized content using Claude's web search
        result = claude_service.optimize_product_seo(
            product_title=product_title,
            product_description=product_description,
            keywords=[k.strip() for k in keywords if k.strip()]
        )
        
        # Try to parse JSON from Claude's response
        if result.get('success'):
            try:
                # Claude's response might be in the optimized_content field
                response_text = result.get('optimized_content', '')
                
                if response_text and isinstance(response_text, str):
                    # Try to extract JSON from the response
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', response_text)
                    if json_match:
                        optimized_data = json.loads(json_match.group())
                        result['parsed_data'] = optimized_data
                    else:
                        # If no JSON found, try to parse the entire response
                        try:
                            optimized_data = json.loads(response_text)
                            result['parsed_data'] = optimized_data
                        except:
                            # Can't parse as JSON, return raw
                            result['parsed_data'] = None
                else:
                    result['parsed_data'] = None
                    result['error'] = f"No response content received from Claude (type: {type(response_text)})"
                    
            except json.JSONDecodeError as e:
                # If parsing fails, return the raw response
                result['parsed_data'] = None
                result['parse_error'] = str(e)
            except Exception as e:
                result['parsed_data'] = None
                result['error'] = str(e)
            
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/ai/optimize-collection', methods=['POST'])
@login_required
def optimize_collection():
    """Use Claude to optimize collection SEO"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        tag = data.get('tag', '')
        product_count = data.get('product_count', 0)
        collection_type = data.get('collection_type', 'smart')
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Get optimized content using Claude
        result = claude_service.optimize_collection_seo(
            tag=tag,
            product_count=int(product_count),
            collection_type=collection_type,
            seo_keywords=[k.strip() for k in seo_keywords if k.strip()]
        )
        
        # Try to parse JSON from Claude's response
        if result.get('success'):
            try:
                response_text = result.get('optimized_content', '')
                
                if response_text and isinstance(response_text, str):
                    # Try to extract JSON from the response
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', response_text)
                    if json_match:
                        optimized_data = json.loads(json_match.group())
                        result['parsed_data'] = optimized_data
                    else:
                        # If no JSON found, try to parse the entire response
                        try:
                            optimized_data = json.loads(response_text)
                            result['parsed_data'] = optimized_data
                        except:
                            result['parsed_data'] = None
                else:
                    result['parsed_data'] = None
                    result['error'] = f"No response content received from Claude"
                    
            except json.JSONDecodeError as e:
                result['parsed_data'] = None
                result['parse_error'] = str(e)
            except Exception as e:
                result['parsed_data'] = None
                result['error'] = str(e)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/create-collection', methods=['POST'])
@login_required
def create_collection():
    """Create Shopify collection with optimized content"""
    try:
        from app.services.shopify_service import ShopifyService
        
        data = request.get_json()
        tag = data.get('tag')
        collection_type = data.get('collection_type', 'smart')
        title = data.get('title')
        handle = data.get('handle')
        description = data.get('description', '')
        meta_description = data.get('meta_description', '')
        
        if not all([tag, title, handle]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: tag, title, handle'
            }), 400
        
        # Get Shopify service
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                'success': False,
                'error': 'Shopify API credentials not configured. Please set them in settings.'
            }), 400
        
        # Create collection based on type
        collection_result = None
        if collection_type == 'smart':
            # Create smart collection with rule for the tag
            collection_result = shopify_service.create_smart_collection({
                'title': title,
                'handle': handle,
                'body_html': description,
                'rules': [{
                    'column': 'tag',
                    'relation': 'equals',
                    'condition': tag
                }],
                'disjunctive': False,  # All conditions must match
                'published': True
            })
        else:
            # Create custom collection
            collection_result = shopify_service.create_custom_collection({
                'title': title,
                'handle': handle,
                'body_html': description,
                'published': True
            })
        
        if collection_result:
            # Log the change in the change tracking service
            from app.services.change_tracking_service import ChangeTrackingService
            
            # Get the collection ID from the result
            collection_id = None
            if isinstance(collection_result, dict):
                collection_id = str(collection_result.get('id', ''))
            
            # Create after_data with the collection details
            after_data = {
                'id': collection_id,
                'title': title,
                'handle': handle,
                'body_html': description,
                'collection_type': collection_type,
                'tag': tag,
                'meta_description': meta_description
            }
            
            # If we have the full collection object, include more details
            if isinstance(collection_result, dict):
                after_data.update({
                    'published_at': collection_result.get('published_at'),
                    'updated_at': collection_result.get('updated_at'),
                    'sort_order': collection_result.get('sort_order', ''),
                })
            
            # Log the collection creation
            ChangeTrackingService.log_change(
                shop_id=get_current_user_id(),
                resource_type='collection',
                resource_id=collection_id or handle,  # Use ID if available, otherwise handle
                action='create',
                before_data={},  # Empty for new collections
                after_data=after_data,
                metadata={
                    'collection_type': collection_type,
                    'created_from_tag': tag,
                    'optimized_by': 'claude-sonnet-4'
                }
            )
            
            # TODO: Add metafield for meta_description if Shopify supports it
            return jsonify({
                'success': True,
                'message': f'{collection_type.title()} collection created successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to create collection'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/ai/clear-generated-tags', methods=['POST'])
@login_required
def clear_generated_tags():
    """Clear all generated tags from session"""
    try:
        # Remove all new_tags_ keys from session
        keys_to_remove = [key for key in session.keys() if key.startswith('new_tags_')]
        for key in keys_to_remove:
            session.pop(key, None)
        
        return jsonify({
            'success': True,
            'message': 'Generated tags cleared from session'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/translate-content', methods=['POST'])
@login_required
def translate_content():
    """Translate content using Claude with cultural adaptation"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        content = data.get('content', {})
        source_language = data.get('source_language', 'EN')
        target_language = data.get('target_language')
        content_type = data.get('content_type', 'product')
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        
        if not target_language:
            return jsonify({
                'success': False,
                'error': 'Target language is required'
            }), 400
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Get translated content
        result = claude_service.translate_content(
            content=content,
            source_language=source_language,
            target_language=target_language,
            content_type=content_type,
            seo_keywords=[k.strip() for k in seo_keywords if k.strip()]
        )
        
        # Try to parse JSON from Claude's response
        if result.get('success'):
            try:
                response_text = result.get('translated_content', '')
                
                if response_text and isinstance(response_text, str):
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', response_text)
                    if json_match:
                        translated_data = json.loads(json_match.group())
                        result['parsed_data'] = translated_data
                    else:
                        try:
                            translated_data = json.loads(response_text)
                            result['parsed_data'] = translated_data
                        except:
                            result['parsed_data'] = None
                else:
                    result['parsed_data'] = None
                    
            except Exception as e:
                result['parsed_data'] = None
                result['parse_error'] = str(e)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/translation-details')
@login_required
def get_translation_details():
    """Get detailed translation status for resources"""
    from app.models.change_log import TranslationStatus
    
    resource_type = request.args.get('type', 'all')
    shop_id = get_current_user_id()
    
    if not shop_id:
        return jsonify({'success': False, 'error': 'Shop not found'})
    
    try:
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({'success': False, 'error': 'Shopify service not available'})
        
        items = []
        
        # Get resources based on type
        if resource_type == 'all' or resource_type == 'products':
            products = shopify_service.get_products(limit=50, published_status='published')
            for product in products:
                # Get translation status for this product
                translations = {}
                translation_records = TranslationStatus.query.filter_by(
                    shop_id=shop_id,
                    resource_type='product',
                    resource_id=str(product['id'])
                ).all()
                
                for record in translation_records:
                    if record.is_translated:
                        translations[record.language] = True
                
                items.append({
                    'id': product['id'],
                    'title': product.get('title', 'Untitled'),
                    'type': 'product',
                    'translations': translations
                })
        
        if resource_type == 'all' or resource_type == 'collections':
            collections = shopify_service.get_collections(limit=50)
            for collection in collections:
                translations = {}
                translation_records = TranslationStatus.query.filter_by(
                    shop_id=shop_id,
                    resource_type='collection',
                    resource_id=str(collection['id'])
                ).all()
                
                for record in translation_records:
                    if record.is_translated:
                        translations[record.language] = True
                
                items.append({
                    'id': collection['id'],
                    'title': collection.get('title', 'Untitled'),
                    'type': 'collection',
                    'translations': translations
                })
        
        if resource_type == 'all' or resource_type == 'pages':
            pages = shopify_service.get_online_store_pages(limit=50)
            for page in pages:
                translations = {}
                translation_records = TranslationStatus.query.filter_by(
                    shop_id=shop_id,
                    resource_type='page',
                    resource_id=str(page['id'])
                ).all()
                
                for record in translation_records:
                    if record.is_translated:
                        translations[record.language] = True
                
                items.append({
                    'id': page['id'],
                    'title': page.get('title', 'Untitled'),
                    'type': 'page',
                    'translations': translations
                })
        
        return jsonify({
            'success': True,
            'items': items
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@bp.route('/api/ai/generate-hreflang', methods=['POST'])
@login_required
def generate_hreflang():
    """Generate hreflang implementation strategy"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        api_keys = get_user_api_keys()
        shop_domain = api_keys.get('shopify_shop_domain', '')
        enabled_languages = data.get('enabled_languages', [])
        url_structure = data.get('url_structure', 'subdirectory')
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Get hreflang strategy
        result = claude_service.generate_hreflang_strategy(
            shop_domain=shop_domain,
            enabled_languages=enabled_languages,
            url_structure=url_structure
        )
        
        # Try to parse JSON from response
        if result.get('success'):
            try:
                strategy_text = result.get('strategy', '')
                if strategy_text:
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', strategy_text)
                    if json_match:
                        strategy_data = json.loads(json_match.group())
                        result['parsed_data'] = strategy_data
                    else:
                        result['parsed_data'] = json.loads(strategy_text)
            except:
                result['parsed_data'] = None
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/enable-language', methods=['POST'])
@login_required
def enable_language():
    """Direct user to enable language in Shopify Admin"""
    try:
        data = request.get_json()
        locale = data.get('locale')
        
        if not locale:
            return jsonify({
                'success': False,
                'error': 'Locale is required'
            }), 400
        
        # Get API keys to build redirect URL
        api_keys = get_user_api_keys()
        shop_domain = api_keys.get('shopify_shop_domain', '')
        
        # Languages must be enabled in Shopify Admin
        return jsonify({
            'success': False,
            'error': 'Please enable languages in Shopify Admin → Settings → Languages → Markets',
            'redirect_url': f'https://admin.shopify.com/store/{shop_domain.split(".")[0]}/settings/languages' if shop_domain else ''
        })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# check-scopes endpoint removed - custom apps have all permissions

@bp.route('/api/ai/save-translation', methods=['POST'])
@login_required
def save_translation():
    """Save translation to Shopify"""
    try:
        from app.services.shopify_service import ShopifyService
        
        data = request.get_json()
        resource_type = data.get('resource_type')
        resource_id = data.get('resource_id')
        locale = data.get('locale')
        translations_data = data.get('translations', {})
        
        if not all([resource_type, resource_id, locale, translations_data]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields'
            }), 400
        
        # Get Shopify service
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                'success': False,
                'error': 'Shopify API credentials not configured. Please set them in settings.'
            }), 400
        
        # Format translations for GraphQL API
        translations = []
        
        # Map frontend field names to Shopify translation keys
        field_mapping = {
            'title': 'title',
            'description': 'body_html',
            'meta_title': 'meta_title',
            'meta_description': 'meta_description',
            'handle': 'handle'
        }
        
        for field, key in field_mapping.items():
            if field in translations_data and translations_data[field]:
                translations.append({
                    'locale': locale,
                    'key': key,
                    'value': translations_data[field]
                })
        
        # Save translations
        success = shopify_service.create_translation(resource_type, resource_id, translations)
        
        if success:
            # Create URL redirects for language-specific paths if handle was translated
            if 'handle' in translations_data and translations_data['handle']:
                original_handle = data.get('original_handle', '')
                translated_handle = translations_data['handle']
                
                if original_handle and translated_handle != original_handle:
                    # Create redirect from /locale/translated-handle to the actual product/collection
                    shopify_service.create_url_redirect(
                        f"/{locale}/{translated_handle}",
                        f"/{resource_type}s/{original_handle}?locale={locale}"
                    )
            
            return jsonify({
                'success': True,
                'message': 'Translation saved successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to save translation'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/products/count')
@login_required
def get_products_count():
    """Get total product count"""
    try:
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                'success': False,
                'error': 'Shopify API credentials not configured.'
            }), 400
        # Only count products available on Online Store
        count = shopify_service.get_products_count(published_status='published')
        return jsonify({
            'success': True,
            'count': count
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/products/batch')
@login_required
def get_products_batch():
    """Get products in batches with pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 250, type=int)
        
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        # Only get products available on Online Store
        products, has_next = shopify_service.get_products_batch(page=page, limit=limit, published_status='published')
        
        # Get optimization statuses for these products
        from app.services.change_tracking_service import ChangeTrackingService
        optimization_statuses = {}
        
        for product in products:
            status = ChangeTrackingService.get_optimization_status(
                shop_id=get_current_user_id(),
                resource_type='product',
                resource_id=str(product['id']),
                optimization_type='seo'
            )
            optimization_statuses[str(product['id'])] = status.is_optimized if status else False
        
        return jsonify({
            'success': True,
            'products': products,
            'optimization_statuses': optimization_statuses,
            'page': page,
            'limit': limit,
            'has_next': has_next
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/collections/count')
@login_required
def get_collections_count():
    """Get total collection count"""
    try:
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        count = shopify_service.get_collections_count()
        return jsonify({
            'success': True,
            'count': count
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/collections/batch')
@login_required
def get_collections_batch():
    """Get collections in batches with pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 250, type=int)
        
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        collections, has_next = shopify_service.get_collections_batch(page=page, limit=limit)
        
        return jsonify({
            'success': True,
            'collections': collections,
            'page': page,
            'limit': limit,
            'has_next': has_next
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/pages/count')
@login_required
def get_pages_count():
    """Get total pages count"""
    try:
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        count = shopify_service.get_pages_count()
        return jsonify({
            'success': True,
            'count': count
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/pages/batch')
@login_required
def get_pages_batch():
    """Get pages in batches with pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 50, type=int)
        
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        pages, has_next = shopify_service.get_pages_batch(page=page, limit=limit)
        
        return jsonify({
            'success': True,
            'pages': pages,
            'page': page,
            'limit': limit,
            'has_next': has_next
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/change-history')
@login_required
def get_change_history():
    """Get change history for the shop"""
    print("=== CHANGE HISTORY ENDPOINT HIT ===")
    try:
        # First, let's just return a simple response to test
        api_keys = get_user_api_keys()
        print(f"Current user: {get_current_user_id()}, shop: {api_keys.get('shopify_shop_domain', 'N/A')}")
        
        # Try importing after we know the endpoint works
        try:
            from app.services.change_tracking_service import ChangeTrackingService
            print("Successfully imported ChangeTrackingService")
        except Exception as import_error:
            print(f"Failed to import ChangeTrackingService: {import_error}")
            return jsonify({
                'success': False,
                'error': f'Import error: {str(import_error)}'
            }), 500
        
        resource_type = request.args.get('resource_type')
        resource_id = request.args.get('resource_id')
        limit = request.args.get('limit', 50, type=int)
        
        print(f"Getting change history for shop {get_current_user_id()}, resource_type: {resource_type}, resource_id: {resource_id}, limit: {limit}")
        
        changes = ChangeTrackingService.get_change_history(
            shop_id=get_current_user_id(),
            resource_type=resource_type,
            resource_id=resource_id,
            limit=limit
        )
        
        print(f"Found {len(changes)} changes")
        
        return jsonify({
            'success': True,
            'changes': [change.to_dict() for change in changes]
        })
    except Exception as e:
        print(f"Error in get_change_history: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/revert-change/<int:change_id>', methods=['POST'])
@login_required
def revert_change(change_id):
    """Revert a specific change"""
    try:
        from app.services.change_tracking_service import ChangeTrackingService
        from app.services.shopify_service import ShopifyService
        
        # Get the change
        change = ChangeTrackingService.revert_change(change_id, get_current_user_id())
        
        if not change:
            return jsonify({
                'success': False,
                'error': 'Change not found or already reverted'
            }), 404
        
        # Apply the revert in Shopify
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        
        if change.resource_type == 'product' and change.before_data:
            # Revert product changes
            success = shopify_service.update_product(
                change.resource_id,
                change.before_data
            )
        elif change.resource_type == 'collection':
            # Handle collection revert
            if change.action == 'create':
                # If the action was 'create', we need to delete the collection
                collection_id = change.resource_id
                collection_type = change.after_data.get('collection_type', 'smart')
                
                if collection_type == 'smart':
                    success = shopify_service.delete_smart_collection(collection_id)
                else:
                    success = shopify_service.delete_custom_collection(collection_id)
                    
                if not success:
                    # Try the other type in case it was misidentified
                    if collection_type == 'smart':
                        success = shopify_service.delete_custom_collection(collection_id)
                    else:
                        success = shopify_service.delete_smart_collection(collection_id)
            else:
                # For other actions (like update), we would restore previous state
                # This is not implemented yet
                success = False
        else:
            success = False
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Successfully reverted {change.resource_type} {change.resource_id}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to revert changes in Shopify'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/reset-all-changes', methods=['POST'])
@login_required
def reset_all_changes():
    """Reset all changes with confirmation"""
    try:
        from app.services.change_tracking_service import ChangeTrackingService
        from app.services.shopify_service import ShopifyService
        
        data = request.get_json()
        confirmation = data.get('confirmation', '')
        
        # Create Shopify service to revert changes
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        
        # Get current shop ID (now always returns actual shop ID)
        shop_id = get_current_user_id()
        if not shop_id:
            return jsonify({
                'success': False,
                'error': 'Shop configuration not found'
            }), 400
        
        print(f"Reset using shop ID: {shop_id}")
        
        success, message = ChangeTrackingService.reset_all_changes(
            shop_id=shop_id,
            confirmation_text=confirmation,
            shopify_service=shopify_service
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/homepage-seo')
@login_required
def get_homepage_seo():
    """Get saved homepage SEO settings"""
    try:
        from app.services.change_tracking_service import ChangeTrackingService
        
        homepage_seo = ChangeTrackingService.get_homepage_seo(get_current_user_id())
        
        if homepage_seo:
            return jsonify({
                'success': True,
                'data': homepage_seo.to_dict()
            })
        else:
            return jsonify({
                'success': True,
                'data': None
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/generate-ad-copy', methods=['POST'])
@login_required
def generate_ad_copy():
    """Use Claude to generate ad copy with competitor research"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Generate ad copy using Claude's web search
        result = claude_service.generate_ad_copy(
            product_name=data.get('product_name', ''),
            target_audience=data.get('target_audience', ''),
            platform=data.get('platform', 'Google'),
            benefits=data.get('benefits', '')
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/analyze-homepage', methods=['POST'])
@login_required
def analyze_homepage():
    """Analyze homepage using JINA AI and Claude"""
    try:
        from app.services.claude_service import ClaudeService
        
        data = request.get_json()
        homepage_url = data.get('homepage_url')
        target_keywords = data.get('target_keywords', '')
        competitor_urls = data.get('competitor_urls', [])
        
        if not homepage_url:
            return jsonify({
                'success': False,
                'error': 'Homepage URL is required'
            }), 400
        
        # Get user API keys
        api_keys = get_user_api_keys()
        if not api_keys.get('anthropic_api_key'):
            return jsonify({
                'success': False,
                'error': 'Anthropic API key not configured. Please set your API key in settings.'
            }), 400
        
        # Initialize Claude service
        claude_service = ClaudeService(api_key=api_keys['anthropic_api_key'])
        
        # Analyze homepage
        result = claude_service.analyze_homepage(
            homepage_url=homepage_url,
            target_keywords=target_keywords.split(',') if target_keywords else [],
            competitor_urls=competitor_urls
        )
        
        # If successful, save the analysis data
        if result.get('success') and result.get('data'):
            from app.services.change_tracking_service import ChangeTrackingService
            
            # Save homepage SEO analysis
            ChangeTrackingService.save_homepage_seo(
                shop_id=get_current_user_id(),
                keywords=target_keywords,
                analysis_data=result.get('data'),
                competitor_urls=competitor_urls
            )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/update-homepage-seo', methods=['POST'])
@login_required
def update_homepage_seo():
    """Update homepage SEO settings in Shopify"""
    try:
        from app.services.shopify_service import ShopifyService
        
        data = request.get_json()
        seo_title = data.get('seo_title')
        seo_description = data.get('seo_description')
        
        if not seo_title or not seo_description:
            return jsonify({
                'success': False,
                'error': 'Both SEO title and description are required'
            }), 400
        
        # Get Shopify service
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        
        # Update homepage SEO using metafields
        success = shopify_service.update_homepage_seo(seo_title, seo_description)
        
        # Always save to our database regardless of Shopify API result
        from app.services.change_tracking_service import ChangeTrackingService
        
        ChangeTrackingService.save_homepage_seo(
            shop_id=get_current_user_id(),
            title=seo_title,
            meta_description=seo_description
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Homepage SEO settings updated successfully'
            })
        else:
            # Provide guidance on how to update homepage SEO
            api_keys = get_user_api_keys()
            shop_domain = api_keys.get('shopify_shop_domain', '')
            shop_handle = shop_domain.split('.')[0] if shop_domain else ''
            admin_url = f'https://admin.shopify.com/store/{shop_handle}/online_store/preferences'
            
            return jsonify({
                'success': False,
                'error': 'Homepage SEO settings cannot be updated via API.',
                'guidance': 'Homepage SEO must be updated manually in your Shopify admin.',
                'admin_url': admin_url,
                'instructions': [
                    'Go to Online Store > Preferences in your Shopify admin',
                    'Update the Homepage title and Homepage meta description fields',
                    'Save your changes'
                ],
                'optimized_title': seo_title,
                'optimized_description': seo_description
            })
            
    except Exception as e:
        print(f"Error updating homepage SEO: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/ai/update-product', methods=['POST'])
@login_required
def update_product():
    """Update Shopify product with optimized content"""
    try:
        from app.services.shopify_service import ShopifyService
        import json
        
        data = request.get_json()
        product_id = data.get('product_id')
        
        print(f"Update product request - Product ID: {product_id}")
        print(f"Request data: {data}")
        
        if not product_id:
            return jsonify({
                'success': False,
                'error': 'Product ID is required'
            }), 400
        
        # Get Shopify service
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                "success": False,
                "error": "Shopify API credentials not configured."
            }), 400
        
        # First, get the current product to preserve existing tags
        try:
            print(f"Fetching product ID: {product_id}")
            current_product = shopify_service.get_product(product_id)
            
            if not current_product:
                print(f"Product {product_id} not found")
                return jsonify({
                    'success': False,
                    'error': f'Product {product_id} not found'
                }), 404
                
            print(f"Found product: {current_product.get('title')}")
        except Exception as e:
            print(f"Error fetching product: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': f'Failed to fetch current product: {str(e)}'
            }), 500
        
        # Get existing tags
        existing_tags = current_product.get('tags', '').split(', ') if current_product.get('tags') else []
        print(f"Existing tags: {existing_tags}")
        
        # Prepare update data
        update_data = {}
        
        # Initialize tag variables
        new_tags = []
        combined_tags = existing_tags.copy()
        
        # Add fields that were provided
        if 'title' in data:
            update_data['title'] = data['title']
        
        if 'body_html' in data:
            update_data['body_html'] = data['body_html']
        
        if 'tags' in data:
            # Parse new tags
            new_tags = data['tags'].split(', ') if isinstance(data['tags'], str) else data['tags']
            print(f"New tags to add: {new_tags}")
            
            # Combine existing tags with new tags (preserve existing, add new)
            # Remove duplicates while preserving order
            for tag in new_tags:
                if tag and tag not in combined_tags:
                    combined_tags.append(tag)
            
            print(f"Combined tags: {combined_tags}")
            update_data['tags'] = ', '.join(combined_tags)
            
            # Store which tags were newly generated for collection creation
            session[f'new_tags_{product_id}'] = new_tags
            print(f"Stored new tags in session: new_tags_{product_id}")
        
        # Initialize variables for response
        tags_info = {
            'new_tags_count': len(new_tags),
            'total_tags_count': len(combined_tags)
        }
        
        print(f"Update data being sent to Shopify: {update_data}")
        
        # Update product via Shopify service
        try:
            success = shopify_service.update_product(product_id, update_data)
            print(f"Update product result: {success}")
        except Exception as update_error:
            print(f"Error updating product: {str(update_error)}")
            return jsonify({
                'success': False,
                'error': f'Shopify update failed: {str(update_error)}'
            }), 500
        
        if success:
            # Import change tracking service
            from app.services.change_tracking_service import ChangeTrackingService
            
            # Log the change
            before_data = {
                'title': current_product.get('title'),
                'body_html': current_product.get('body_html'),
                'tags': current_product.get('tags', '')
            }
            
            after_data = {
                'title': update_data.get('title', current_product.get('title')),
                'body_html': update_data.get('body_html', current_product.get('body_html')),
                'tags': update_data.get('tags', current_product.get('tags', ''))
            }
            
            metadata = {
                'new_tags': new_tags,
                'model_used': 'claude-sonnet-4-20250514'
            }
            
            # If metafields were provided, add them to metadata
            if 'metafields' in data:
                metadata['metafields'] = data['metafields']
            
            # Log the change
            ChangeTrackingService.log_change(
                shop_id=get_current_user_id(),
                resource_type='product',
                resource_id=product_id,
                action='optimize_seo',
                before_data=before_data,
                after_data=after_data,
                metadata=metadata
            )
            
            # Mark as optimized
            ChangeTrackingService.mark_as_optimized(
                shop_id=get_current_user_id(),
                resource_type='product',
                resource_id=product_id,
                optimization_type='seo',
                current_data=after_data
            )
            
            # Handle metafields if provided
            if 'metafields' in data:
                metafields = data['metafields']
                
                # Update SEO metafields
                try:
                    if 'meta_description' in metafields:
                        shopify_service.update_product_metafield(
                            product_id,
                            namespace='global',
                            key='description_tag',
                            value=metafields['meta_description'],
                            value_type='string'
                        )
                    
                    if 'h1_tag' in metafields:
                        shopify_service.update_product_metafield(
                            product_id,
                            namespace='custom',
                            key='h1_tag',
                            value=metafields['h1_tag'],
                            value_type='string'
                        )
                except Exception as meta_error:
                    print(f"Warning: Failed to update metafields: {str(meta_error)}")
                    # Don't fail the whole request if metafields fail
            
            return jsonify({
                'success': True,
                'message': 'Product updated successfully',
                **tags_info
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update product'
            }), 500
            
    except Exception as e:
        print(f"Unexpected error in update_product: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# Automation Control Endpoints
@bp.route('/automation/toggle/<automation_type>', methods=['POST'])
@login_required
def toggle_automation(automation_type):
    """Toggle automation on/off"""
    try:
        from app.models.ai_config import AIConfig
        
        # Get request data
        data = request.get_json()
        enabled = data.get('enabled', False)
        
        # Map automation types to config fields
        field_mapping = {
            'seo': 'seo_enabled',
            'collection': 'collection_enabled',
            'translation': 'translation_enabled',
            'alt_text': 'alt_text_enabled',
            'search_console': 'search_console_enabled'
        }
        
        if automation_type not in field_mapping:
            return jsonify({
                'success': False,
                'error': f'Invalid automation type: {automation_type}'
            }), 400
        
        field_name = field_mapping[automation_type]
        
        # Get current shop ID (unified resolver)
        shop_id = get_current_user_id()
        if not shop_id:
            return jsonify({
                'success': False,
                'error': 'Shop configuration not found'
            }), 400
        
        # Get or create config for seo_automation module
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type='seo_automation'
        ).first()
        
        if not config:
            config = AIConfig(
                shop_id=shop_id,
                module_type='seo_automation',
                config_data={}
            )
            db.session.add(config)
        
        # Update the specific field
        config_data = config.config_data or {}
        config_data[field_name] = enabled
        config.config_data = config_data
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'enabled': enabled,
            'message': f'{automation_type.title()} automation {"enabled" if enabled else "disabled"}'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/automation/run/<automation_type>', methods=['POST'])
@login_required
def run_automation(automation_type):
    """Manually trigger automation"""
    try:
        # Handle the simple automation types from dashboard
        if automation_type == 'products':
            from app.tasks import optimize_all_products_task
            
            # Get limit from request body
            data = request.get_json() or {}
            limit = data.get('limit')
            
            result = optimize_all_products_task.delay(get_current_user_id(), None, limit)
            return jsonify({
                'success': True,
                'task_id': result.id,
                'message': f'Product optimization started{f" (limit: {limit})" if limit else ""}'
            })
        elif automation_type == 'collections':
            return jsonify({
                'success': False,
                'error': 'Please use the Collections page to create collections',
                'redirect': url_for('ai_modules.seo_collections')
            })
        elif automation_type == 'translate':
            return jsonify({
                'success': False,
                'error': 'Please use the Languages page to run translations',
                'redirect': url_for('ai_modules.seo_languages')
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Invalid automation type: {automation_type}'
            }), 400
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/automation/settings', methods=['POST'])
@login_required
def save_automation_settings():
    """Save automation settings"""
    try:
        data = request.get_json()
        automation_type = data.get('automation_type')
        settings = data.get('settings', {})
        
        if not automation_type:
            return jsonify({
                'success': False,
                'error': 'Automation type is required'
            }), 400
        
        # Get current shop ID (unified resolver)
        shop_id = get_current_user_id()
        if not shop_id:
            return jsonify({
                'success': False,
                'error': 'Shop configuration not found'
            }), 400
        
        # Get or create config
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type=automation_type
        ).first()
        
        if not config:
            config = AIConfig(
                shop_id=shop_id,
                module_type=automation_type,
                config_data={}
            )
            db.session.add(config)
        
        # Update settings
        config_data = config.config_data or {}
        
        # Extract API keys from settings to save to user settings
        openai_api_key = settings.pop('openai_api_key', None)
        anthropic_api_key = settings.pop('anthropic_api_key', None)
        
        # Update remaining automation config
        config_data.update(settings)
        
        # Force SQLAlchemy to recognize the JSON field has changed
        from sqlalchemy.orm.attributes import flag_modified
        config.config_data = config_data
        flag_modified(config, 'config_data')
        
        # Save API keys to user settings if provided
        if openai_api_key is not None or anthropic_api_key is not None:
            user_settings = get_current_user_settings()
            if user_settings:
                if openai_api_key is not None:
                    user_settings.openai_api_key = openai_api_key.strip()
                if anthropic_api_key is not None:
                    user_settings.anthropic_api_key = anthropic_api_key.strip()
        
        # Save to database
        db.session.commit()
        print(f"Automation settings saved for shop {shop_id}, module: {automation_type}")
        
        return jsonify({
            'success': True,
            'message': 'Automation settings saved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/automation/history')
@login_required
def get_automation_history():
    """View automation history"""
    try:
        from app.services.change_tracking_service import ChangeTrackingService
        
        # Get query parameters
        automation_type = request.args.get('automation_type')
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build metadata filter for automation changes
        metadata_filter = {'automated': True}
        if automation_type:
            metadata_filter['automation_type'] = automation_type
        
        # Get changes with automation metadata
        changes = ChangeTrackingService.get_change_history(
            shop_id=get_current_user_id(),
            limit=limit,
            metadata_filter=metadata_filter
        )
        
        # Format response
        history = []
        for change in changes:
            history_item = {
                'id': change.id,
                'timestamp': change.created_at.isoformat(),
                'resource_type': change.resource_type,
                'resource_id': change.resource_id,
                'action': change.action,
                'automation_type': change.metadata.get('automation_type', 'unknown'),
                'status': 'completed' if not change.is_reverted else 'reverted',
                'details': {
                    'before': change.before_data,
                    'after': change.after_data
                }
            }
            history.append(history_item)
        
        return jsonify({
            'success': True,
            'history': history,
            'total': len(history),
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# Celery Task API Endpoints
@bp.route('/api/tasks/optimize-product', methods=['POST'])
@login_required
def submit_optimize_product_task():
    """Submit product optimization task to Celery"""
    try:
        from app.tasks import optimize_product_seo_task
        
        data = request.get_json()
        product_id = data.get('product_id')
        product_data = data.get('product_data', {})
        keywords = data.get('keywords', '').split(',') if data.get('keywords') else []
        
        if not product_id:
            return jsonify({
                'success': False,
                'error': 'Product ID is required'
            }), 400
        
        # Submit task to Celery
        task = optimize_product_seo_task.apply_async(
            args=[get_current_user_id(), product_id, product_data, 
                  [k.strip() for k in keywords if k.strip()]]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Optimization task submitted'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/tasks/optimize-all-products', methods=['POST'])
@login_required
def submit_optimize_all_products_task():
    """Submit bulk product optimization task to Celery"""
    try:
        from app.tasks import optimize_all_products_task
        
        data = request.get_json()
        keywords = data.get('keywords', '').split(',') if data.get('keywords') else []
        limit = data.get('limit')  # Optional limit
        
        # Submit task to Celery
        task = optimize_all_products_task.apply_async(
            args=[get_current_user_id(), [k.strip() for k in keywords if k.strip()], limit]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Bulk optimization task submitted'
        })
        
    except Exception as e:
        import traceback
        print(f"Error in submit_optimize_all_products_task: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/tasks/status/<task_id>')
@login_required
def get_task_status(task_id):
    """Get Celery task status"""
    try:
        from celery.result import AsyncResult
        
        result = AsyncResult(task_id)
        
        response = {
            'task_id': task_id,
            'state': result.state,
            'current': 0,
            'total': 0,
            'status': 'Unknown'
        }
        
        if result.state == 'PENDING':
            response['status'] = 'Task is waiting to be processed...'
        elif result.state == 'PROGRESS':
            response['current'] = result.info.get('current', 0)
            response['total'] = result.info.get('total', 1)
            response['status'] = result.info.get('status', 'Processing...')
        elif result.state == 'SUCCESS':
            response['result'] = result.result
            response['status'] = 'Task completed successfully'
        elif result.state == 'FAILURE':
            response['info'] = str(result.info)
            response['status'] = 'Task failed'
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            'task_id': task_id,
            'state': 'FAILURE',
            'info': str(e)
        }), 500


@bp.route('/api/tasks/create-collection', methods=['POST'])
@login_required
def submit_create_collection_task():
    """Submit collection creation task to Celery"""
    try:
        from app.tasks import create_collection_from_tag_task
        
        data = request.get_json()
        tag = data.get('tag')
        product_count = data.get('product_count', 0)
        collection_type = data.get('collection_type', 'smart')
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        
        if not tag:
            return jsonify({
                'success': False,
                'error': 'Tag is required'
            }), 400
        
        # Submit task to Celery
        task = create_collection_from_tag_task.apply_async(
            args=[get_current_user_id(), tag, product_count, collection_type,
                  [k.strip() for k in seo_keywords if k.strip()]]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Collection creation task submitted'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/tasks/create-all-collections', methods=['POST'])
@login_required
def submit_create_all_collections_task():
    """Submit bulk collection creation task to Celery"""
    try:
        from app.tasks import create_all_collections_task
        
        data = request.get_json()
        tags_data = data.get('tags_data', [])
        collection_type = data.get('collection_type', 'smart')
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        
        if not tags_data:
            return jsonify({
                'success': False,
                'error': 'No tags provided'
            }), 400
        
        # Submit task to Celery
        task = create_all_collections_task.apply_async(
            args=[get_current_user_id(), tags_data, collection_type,
                  [k.strip() for k in seo_keywords if k.strip()]]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Bulk collection creation task submitted'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/tasks/translate-content', methods=['POST'])
@login_required
def submit_translate_content_task():
    """Submit translation task to Celery"""
    try:
        from app.tasks import translate_content_task
        
        data = request.get_json()
        resource_type = data.get('resource_type')
        resource_id = data.get('resource_id')
        content = data.get('content', {})
        target_language = data.get('target_language')
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        
        if not all([resource_type, resource_id, target_language]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields'
            }), 400
        
        # Submit task to Celery
        task = translate_content_task.apply_async(
            args=[get_current_user_id(), resource_type, resource_id, content,
                  target_language, [k.strip() for k in seo_keywords if k.strip()]]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Translation task submitted'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/get-available-tags')
@login_required
def get_available_tags():
    """Get available tags for collection creation"""
    try:
        from app.services.change_tracking_service import ChangeTrackingService
        from app.models.shop import Shop
        from app.models.access_control import UserSettings
        from datetime import datetime, timedelta, timezone
        
        # Get current shop ID (now always returns actual shop ID)
        shop_id = get_current_user_id()
        if not shop_id:
            return jsonify({
                'success': False,
                'error': 'Shop configuration not found'
            }), 400
        
        # Get shopify service
        shopify_service = get_shopify_service()
        if not shopify_service:
            return jsonify({
                'success': False,
                'error': 'Shopify API credentials not configured'
            }), 400
        
        # Get products to analyze tags
        products = shopify_service.get_products(limit=250)
        
        # Collect AI-generated tags from recent optimizations
        new_generated_tags = set()
        yesterday = datetime.now(timezone.utc) - timedelta(days=7)  # Look at last 7 days
        recent_changes = ChangeTrackingService.get_change_history(
            shop_id=shop_id,
            resource_type='product',
            limit=500
        )
        
        # Extract AI-generated tags
        for change in recent_changes:
            if (change.action == 'optimize_seo' and 
                change.created_at >= yesterday and
                change.change_metadata and
                change.change_metadata.get('new_tags')):
                new_tags = change.change_metadata.get('new_tags', [])
                if isinstance(new_tags, list):
                    new_generated_tags.update(new_tags)
        
        # Count tags from products
        tag_counts = {}
        for product in products:
            if product.get('tags'):
                tags = [tag.strip() for tag in product['tags'].split(',')]
                for tag in tags:
                    if tag and tag in new_generated_tags:
                        tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Filter out tags that already have collections
        existing_collection_tags = ChangeTrackingService.get_collections_created_from_tags(shop_id)
        
        # Get existing collections to check handles
        collections = shopify_service.get_collections(limit=250)
        existing_handles = [c.get('handle', '') for c in collections]
        existing_titles = [c.get('title', '').lower() for c in collections]
        
        # Build available tags list
        available_tags = []
        for tag, count in sorted(tag_counts.items(), key=lambda x: x[1], reverse=True):
            if tag in existing_collection_tags:
                continue
                
            # Check if collection already exists
            tag_handle = tag.lower().replace(' ', '-').replace('_', '-')
            tag_title = tag.lower()
            
            if tag_handle in existing_handles or tag_title in existing_titles:
                continue
                
            available_tags.append({
                'tag': tag,
                'count': count
            })
        
        return jsonify({
            'success': True,
            'tags': available_tags,
            'total': len(available_tags)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/tasks/translate-all', methods=['POST'])
@login_required
def submit_translate_all_task():
    """Submit bulk translation task to Celery"""
    try:
        from app.tasks import translate_all_content_task
        
        data = request.get_json()
        target_languages = data.get('target_languages', [])
        resource_types = data.get('resource_types', ['product', 'collection', 'page'])
        seo_keywords = data.get('seo_keywords', '').split(',') if data.get('seo_keywords') else []
        limit = data.get('limit')  # Add limit support
        
        if not target_languages:
            return jsonify({
                'success': False,
                'error': 'No target languages provided'
            }), 400
        
        # Submit task to Celery
        task = translate_all_content_task.apply_async(
            args=[get_current_user_id(), target_languages, resource_types,
                  [k.strip() for k in seo_keywords if k.strip()], limit]
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Bulk translation task submitted'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/search-console/update-metrics', methods=['POST'])
@login_required
def update_search_console_metrics():
    """Manually trigger Search Console metrics update"""
    try:
        from app.tasks import fetch_search_console_metrics_task
        from app.models import GoogleOAuth
        
        # Check if Google OAuth is connected
        google_oauth = GoogleOAuth.query.filter_by(shop_id=get_current_user_id()).first()
        if not google_oauth:
            return jsonify({
                'success': False,
                'error': 'Please connect your Google account first'
            }), 400
        
        # Submit task to Celery
        task = fetch_search_console_metrics_task.apply_async(
            args=[get_current_user_id(), False]  # False = not initial fetch
        )
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Metrics update started. This may take a few minutes.'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500