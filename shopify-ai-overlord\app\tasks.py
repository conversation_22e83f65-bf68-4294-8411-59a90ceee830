from celery import current_task
from celery_app import celery_app as celery
from celery.exceptions import SoftTimeLimitExceeded
from app.services.shopify_service import ShopifyService
from app.services.claude_service import ClaudeService
from app.services.change_tracking_service import ChangeTrackingService
from app.models.shop import Shop
from app.models.automation_job import AutomationJob
from app import db
import json
import os
import time
import traceback


def get_config_data(config):
    """Helper function to safely get config_data as dict, handling string JSON"""
    if not config or not config.config_data:
        return {}
    
    config_data = config.config_data
    if isinstance(config_data, str):
        try:
            return json.loads(config_data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    return config_data if isinstance(config_data, dict) else {}

@celery.task(bind=True, name='optimize_product_seo')
def optimize_product_seo_task(self, shop_id, product_id, product_data, keywords=None):
    """Background task to optimize product SEO"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: optimize_product_seo")
        print(f"Task ID: {self.request.id}")
        print(f"Product ID: {product_id}")
        print(f"Shop ID: {shop_id}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check if task is delayed
        if hasattr(self.request, 'eta') and self.request.eta:
            print(f"SCHEDULED TO START AT: {self.request.eta}")
            eta_seconds = (self.request.eta - time.time()) if isinstance(self.request.eta, (int, float)) else 0
            if eta_seconds > 0:
                print(f"WAITING TIME: {eta_seconds/60:.1f} minutes")
        else:
            print(f"STARTING IMMEDIATELY")
        print(f"{'='*60}\n")
        
        # Update task state
        self.update_state(state='PROGRESS', meta={'status': 'Starting optimization...'})
        
        # Get shop and API keys - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Create job record
        job = AutomationJob(
            shop_id=shop.id,  # Use the actual shop ID
            task_type='product_seo',
            resource_id=str(product_id),
            status='processing',
            task_id=self.request.id
        )
        db.session.add(job)
        db.session.commit()
        time.sleep(0.3)  # Prevent database locking
        
        # Initialize services
        # Ensure we have valid shop domain
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured' or shop_domain == 'none':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        claude_service = ClaudeService(api_key=shop.anthropic_api_key)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'status': 'Analyzing product with AI...'})
        
        # Optimize with Claude
        result = claude_service.optimize_product_seo(
            product_title=product_data.get('title', ''),
            product_description=product_data.get('description', ''),
            keywords=keywords or []
        )
        
        if result.get('success'):
            self.update_state(state='PROGRESS', meta={'status': 'Applying optimizations...'})
            
            if result.get('parsed_data'):
                opt = result['parsed_data']
            else:
                # Log the raw response for debugging
                print(f"No parsed_data in result for product {product_id}")
                print(f"Result: {result}")
                raise Exception("No parsed data in optimization result")
            
            # CRITICAL: Fetch current product to preserve existing tags
            current_product = shopify_service.get_product(product_id)
            if not current_product:
                raise Exception(f"Product {product_id} not found")
            
            # Get existing tags
            existing_tags = current_product.get('tags', '').split(', ') if current_product.get('tags') else []
            ai_generated_tags = opt.get('tags', [])
            
            # Find truly NEW tags (not in existing)
            truly_new_tags = []
            combined_tags = existing_tags.copy()
            for tag in ai_generated_tags:
                if tag and tag not in existing_tags:  # Only add if not already present
                    truly_new_tags.append(tag)
                    combined_tags.append(tag)
            
            print(f"Product {product_id}: existing tags: {len(existing_tags)}, AI generated: {len(ai_generated_tags)}, truly new: {len(truly_new_tags)}, combined: {len(combined_tags)}")
            
            # Update product in Shopify
            update_data = {
                'title': opt.get('optimized_title'),
                'body_html': opt.get('product_description'),
                'tags': ', '.join(combined_tags)  # Use COMBINED tags, not just new ones!
            }
            
            success = shopify_service.update_product(product_id, update_data)
            
            if success:
                # Log the change
                change_log = ChangeTrackingService.log_change(
                    shop_id=shop.id,
                    resource_type='product',
                    resource_id=str(product_id),
                    action='optimize_seo',
                    before_data={
                        'title': current_product.get('title'),
                        'body_html': current_product.get('body_html'), 
                        'tags': current_product.get('tags', '')
                    },
                    after_data=update_data,
                    metadata={
                        'model_used': 'claude-sonnet-4',
                        'automated': True,
                        'new_tags': truly_new_tags  # CRITICAL: Store ONLY truly new tags for collection maker!
                    }
                )
                
                # Debug: Verify the change was saved correctly
                if change_log:
                    print(f"[DEBUG] Change logged with ID {change_log.id}, metadata: {change_log.change_metadata}")
                else:
                    print(f"[DEBUG] Failed to log change for product {product_id}")
                
                # Mark as optimized
                ChangeTrackingService.mark_as_optimized(
                    shop_id=shop.id,
                    resource_type='product',
                    resource_id=str(product_id),
                    optimization_type='seo',
                    current_data=update_data
                )
                
                # Update job status
                job.status = 'completed'
                job.result_data = json.dumps(opt)
                db.session.commit()
                time.sleep(0.3)  # Prevent database locking
                
                # REMOVED: Automatic translation after products
                # Translations should only happen after collections are created
                # This ensures proper workflow: Products -> Collections -> Translations
                
                return {
                    'success': True,
                    'product_id': product_id,
                    'optimization': opt
                }
            else:
                raise Exception("Failed to update product in Shopify")
        else:
            raise Exception(result.get('error', 'Optimization failed'))
            
    except SoftTimeLimitExceeded:
        job.status = 'failed'
        job.error_message = 'Task timed out'
        db.session.commit()
        time.sleep(0.3)  # Prevent database locking
        raise
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
            time.sleep(0.3)  # Prevent database locking
        raise


@celery.task(bind=True, name='optimize_all_products')
def optimize_all_products_task(self, shop_id, keywords=None, limit=None):
    """Background task to optimize all products"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: optimize_all_products")
        print(f"Task ID: {self.request.id}")
        print(f"Shop ID: {shop_id}")
        print(f"Limit: {limit}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"STARTING BULK OPTIMIZATION...")
        print(f"{'='*60}\n")
        
        from app.services.quota_service import check_quota_available
        
        self.update_state(state='PROGRESS', meta={
            'status': 'Starting bulk optimization...',
            'current': 0,
            'total': 0
        })
        
        # Get shop - handle both shop_id and user_settings_id
        print(f"Looking for shop with ID: {shop_id}")
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    print(f"Found shop by domain: {user_settings.shopify_shop_domain}")
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
                        print(f"Updated API keys for shop")
        
        if not shop:
            raise Exception(f"Shop not found for ID: {shop_id}")
        
        # Create master job record
        job = AutomationJob(
            shop_id=shop.id,
            task_type='bulk_product_seo',
            status='processing',
            task_id=self.request.id
        )
        db.session.add(job)
        db.session.commit()
        
        # Initialize Shopify service
        print(f"Shop details - domain: {shop.shopify_shop_domain}, shop_domain: {shop.shop_domain}, has token: {bool(shop.shopify_access_token)}")
        
        # Ensure we have valid shop domain
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Get ALL products to optimize - only get products available on Online Store
        # Don't apply limit here - we need to see all products to filter properly
        all_products = shopify_service.get_products(limit=0, published_status='published')
        total_products = len(all_products)
        
        # Get all already optimized product IDs in one query (much faster)
        optimized_ids = ChangeTrackingService.get_all_optimized_product_ids(shop.id, 'seo')
        
        # Filter out already optimized products
        products_to_optimize = []
        already_optimized_count = 0
        
        for product in all_products:
            product_id = str(product['id'])
            if product_id not in optimized_ids:
                products_to_optimize.append(product)
            else:
                already_optimized_count += 1
                
        # Apply limit if provided (this is the quota limit)
        if limit and limit > 0:
            products_to_optimize = products_to_optimize[:limit]
                
        # Check quota before processing
        quota_available, remaining, message = check_quota_available(shop.id, 'products', len(products_to_optimize))
        if not quota_available:
            # Limit to remaining quota
            products_to_optimize = products_to_optimize[:remaining] if remaining > 0 else []
            print(f"Quota limit reached: {message}")
                
        # Debug info
        print(f"Product optimization task: {total_products} total, {already_optimized_count} already done, {len(products_to_optimize)} to process")
        processed = 0
        succeeded = 0
        failed = 0
        
        for product in products_to_optimize:
            try:
                self.update_state(state='PROGRESS', meta={
                    'status': f'Optimizing {product["title"]}...',
                    'current': processed,
                    'total': len(products_to_optimize)
                })
                
                # Call individual optimization task with shop.id
                # Don't use .get() - just fire and forget the subtasks
                # Add countdown to spread jobs over time (30 seconds per job)
                countdown_seconds = processed * 5  # 5 seconds * job number
                
                print(f"[PRODUCT {processed + 1}/{len(products_to_optimize)}] Queuing: {product['title']}")
                print(f"  - Product ID: {product['id']}")
                print(f"  - Will start in: {countdown_seconds} seconds")
                
                optimize_product_seo_task.apply_async(
                    args=[shop.id, product['id'], {
                        'title': product.get('title'),
                        'description': product.get('body_html'),
                        'tags': product.get('tags', '')
                    }, keywords],
                    countdown=countdown_seconds
                )
                
                # For now, count as succeeded since we can't track subtasks
                succeeded += 1
                    
                processed += 1
                
                # No need for sleep since we're using countdown
                
            except Exception as e:
                failed += 1
                processed += 1
                print(f"Error optimizing product {product['id']}: {str(e)}")
        
        # Update job with results
        job.status = 'completed'
        job.result_data = json.dumps({
            'total_products': total_products,
            'optimized': succeeded,
            'failed': failed,
            'already_optimized': already_optimized_count,
            'skipped': already_optimized_count,
            'processed': len(products_to_optimize)
        })
        db.session.commit()
        
        return {
            'success': True,
            'total': total_products,
            'optimized': succeeded,
            'failed': failed
        }
        
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='create_collection_from_tag')
def create_collection_from_tag_task(self, shop_id, tag, product_count, collection_type='smart', seo_keywords=None):
    """Background task to create collection from tag"""
    try:
        from app.services.quota_service import check_quota_available
        
        # Check quota before processing
        quota_available, remaining, message = check_quota_available(shop_id, 'collections', 1)
        if not quota_available:
            print(f"Collection quota exceeded: {message}")
            return {
                'success': False,
                'error': f'Daily collection quota exceeded. {remaining} remaining.',
                'skipped': True
            }
        
        self.update_state(state='PROGRESS', meta={'status': 'Optimizing collection with AI...'})
        
        # Get shop - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Create job record
        job = AutomationJob(
            shop_id=shop.id,  # Use the actual shop ID
            task_type='create_collection',
            status='processing',
            task_id=self.request.id,
            job_metadata=json.dumps({'tag': tag})
        )
        db.session.add(job)
        db.session.commit()
        
        # Initialize services
        # Ensure we have valid shop domain
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured' or shop_domain == 'none':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        claude_service = ClaudeService(api_key=shop.anthropic_api_key)
        
        # Double-check if collection already exists for this tag (prevent race conditions)
        existing_tags = ChangeTrackingService.get_collections_created_from_tags(shop.id)
        if tag in existing_tags:
            print(f"Collection for tag '{tag}' already exists - skipping")
            job.status = 'completed'
            job.result_data = json.dumps({'skipped': True, 'reason': 'Collection already exists'})
            db.session.commit()
            return {
                'success': True,
                'skipped': True,
                'message': f'Collection for tag "{tag}" already exists'
            }
        
        # Get optimization from Claude
        result = claude_service.optimize_collection_seo(
            tag=tag,
            product_count=product_count,
            collection_type=collection_type,
            seo_keywords=seo_keywords or []
        )
        
        if result.get('success') and result.get('parsed_data'):
            self.update_state(state='PROGRESS', meta={'status': 'Creating collection in Shopify...'})
            
            opt = result['parsed_data']
            
            # Create collection in Shopify
            collection_data = {
                'title': opt.get('title'),
                'handle': opt.get('handle'),
                'body_html': opt.get('description'),
                'published': True
            }
            
            if collection_type == 'smart':
                collection_data['rules'] = [{
                    'column': 'tag',
                    'relation': 'equals',
                    'condition': tag
                }]
                collection_data['disjunctive'] = False
                collection_result = shopify_service.create_smart_collection(collection_data)
            else:
                collection_result = shopify_service.create_custom_collection(collection_data)
            
            if collection_result:
                # Log the change
                ChangeTrackingService.log_change(
                    shop_id=shop.id,  # Use the actual shop ID
                    resource_type='collection',
                    resource_id=str(collection_result.get('id', '')),
                    action='create',
                    before_data={},
                    after_data=collection_data,
                    metadata={
                        'collection_type': collection_type,
                        'created_from_tag': tag,
                        'optimized_by': 'claude-sonnet-4',
                        'automated': True
                    }
                )
                
                # Update job status
                job.status = 'completed'
                job.resource_id = str(collection_result.get('id', ''))
                job.result_data = json.dumps(opt)
                db.session.commit()
                
                # REMOVED: Automatic translation after collections
                # Translations are now handled by trigger_all_translations_task
                # which runs after ALL collections are created
                
                return {
                    'success': True,
                    'collection_id': collection_result.get('id'),
                    'optimization': opt
                }
            else:
                raise Exception("Failed to create collection in Shopify")
        else:
            raise Exception(result.get('error', 'Optimization failed'))
            
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='create_all_collections')
def create_all_collections_task(self, shop_id, tags_data, collection_type='smart', seo_keywords=None):
    """Background task to create multiple collections"""
    try:
        from app.services.quota_service import check_quota_available
        
        self.update_state(state='PROGRESS', meta={
            'status': 'Starting collection creation...',
            'current': 0,
            'total': len(tags_data)
        })
        
        # Get shop - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Create master job
        job = AutomationJob(
            shop_id=shop.id,  # Use the actual shop ID
            task_type='bulk_create_collections',
            status='processing',
            task_id=self.request.id
        )
        db.session.add(job)
        db.session.commit()
        
        # Get tags that already have collections to avoid duplicates
        existing_collection_tags = ChangeTrackingService.get_collections_created_from_tags(shop.id)
        print(f"Tags that already have collections: {existing_collection_tags}")
        
        # Check quota before processing
        tags_to_process = [t for t in tags_data if t['tag'] not in existing_collection_tags]
        quota_available, remaining, message = check_quota_available(shop.id, 'collections', len(tags_to_process))
        if not quota_available:
            # Limit to remaining quota
            tags_to_process = tags_to_process[:remaining] if remaining > 0 else []
            print(f"Collection quota limit reached: {message}")
        
        processed = 0
        succeeded = 0
        failed = 0
        skipped = 0
        
        for tag_info in tags_data:
            try:
                tag = tag_info['tag']
                count = tag_info['count']
                
                # Skip if collection already exists for this tag
                if tag in existing_collection_tags:
                    print(f"Skipping tag '{tag}' - collection already exists")
                    skipped += 1
                    processed += 1
                    continue
                
                # Skip if not in tags_to_process (quota exceeded)
                if tag_info not in tags_to_process:
                    print(f"Skipping tag '{tag}' - quota exceeded")
                    skipped += 1
                    processed += 1
                    continue
                
                self.update_state(state='PROGRESS', meta={
                    'status': f'Creating collection for "{tag}"...',
                    'current': processed,
                    'total': len(tags_data)
                })
                
                # Call individual task - don't use .get() to avoid blocking
                # Add countdown to spread jobs over time (30 seconds per job)
                countdown_seconds = processed * 5  # 5 seconds * job number
                create_collection_from_tag_task.apply_async(
                    args=[shop.id, tag, count, collection_type, seo_keywords],
                    countdown=countdown_seconds
                )
                
                # For now, count as succeeded since we can't track subtasks synchronously
                succeeded += 1
                    
                processed += 1
                # No need for sleep since we're using countdown
                
            except Exception as e:
                # Don't count Celery warnings as failures
                if "Never call result.get()" not in str(e):
                    failed += 1
                    print(f"Error creating collection for tag {tag}: {str(e)}")
                processed += 1
        
        # Update job
        job.status = 'completed'
        job.result_data = json.dumps({
            'total': len(tags_data),
            'created': succeeded,
            'failed': failed,
            'skipped': skipped
        })
        db.session.commit()
        
        return {
            'success': True,
            'total': len(tags_data),
            'created': succeeded,
            'failed': failed,
            'skipped': skipped
        }
        
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='translate_content')
def translate_content_task(self, shop_id, resource_type, resource_id, content, target_language, seo_keywords=None):
    """Background task to translate content"""
    try:
        self.update_state(state='PROGRESS', meta={'status': 'Translating content...'})
        
        # Get shop - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Create job
        job = AutomationJob(
            shop_id=shop.id,  # Use the actual shop ID
            task_type='translate',
            resource_id=str(resource_id),
            status='processing',
            task_id=self.request.id,
            job_metadata=json.dumps({
                'resource_type': resource_type,
                'target_language': target_language
            })
        )
        db.session.add(job)
        db.session.commit()
        
        # Initialize services
        # Ensure we have valid shop domain
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured' or shop_domain == 'none':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        claude_service = ClaudeService(api_key=shop.anthropic_api_key)
        
        # Translate with Claude
        result = claude_service.translate_content(
            content=content,
            source_language='EN',
            target_language=target_language,
            content_type=resource_type,
            seo_keywords=seo_keywords or []
        )
        
        if result.get('success') and result.get('parsed_data'):
            self.update_state(state='PROGRESS', meta={'status': 'Saving translation...'})
            
            translations_data = result['parsed_data']['translations']
            
            # Format translations for Shopify
            translations = []
            for field, value in translations_data.items():
                if field in ['title', 'body_html', 'meta_title', 'meta_description', 'handle']:
                    translations.append({
                        'locale': target_language.lower(),
                        'key': field,
                        'value': value
                    })
            
            # Save translation
            success = shopify_service.create_translation(resource_type, resource_id, translations)
            
            if success:
                # Track the translation
                translated_fields = list(translations_data.keys())
                ChangeTrackingService.mark_as_translated(
                    shop_id=shop.id,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    language=target_language,
                    translated_fields=translated_fields,
                    translated_by='claude-sonnet-4',
                    translated_data=translations_data  # Store the actual translated content
                )
                
                # Log the change
                ChangeTrackingService.log_change(
                    shop_id=shop.id,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    action='translate',
                    before_data={'language': 'original'},
                    after_data={'language': target_language, 'fields': translated_fields},
                    metadata={
                        'target_language': target_language,
                        'translated_by': 'claude-sonnet-4',
                        'automated': True
                    }
                )
                
                job.status = 'completed'
                job.result_data = json.dumps(translations_data)
                db.session.commit()
                
                return {
                    'success': True,
                    'resource_id': resource_id,
                    'translations': translations_data
                }
            else:
                raise Exception("Failed to save translation")
        else:
            raise Exception(result.get('error', 'Translation failed'))
            
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='translate_all_content')
def translate_all_content_task(self, shop_id, target_languages, resource_types=None, seo_keywords=None, limit=None):
    """Background task to translate all content"""
    try:
        self.update_state(state='PROGRESS', meta={
            'status': 'Starting bulk translation...',
            'current': 0,
            'total': 0
        })
        
        # Get shop - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Create master job
        job = AutomationJob(
            shop_id=shop.id,  # Use the actual shop ID
            task_type='bulk_translate',
            status='processing',
            task_id=self.request.id,
            job_metadata=json.dumps({
                'languages': target_languages,
                'resource_types': resource_types or ['product', 'collection', 'page']
            })
        )
        db.session.add(job)
        db.session.commit()
        
        # Initialize Shopify service
        # Ensure we have valid shop domain
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured' or shop_domain == 'none':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Collect all resources to translate
        resources = []
        
        # Get already translated resources for all languages
        translated_by_language = {}
        for language in target_languages:
            translated_by_language[language] = ChangeTrackingService.get_all_translated_resource_ids(
                shop.id, language
            )
            print(f"Already translated for {language}: {sum(len(ids) for ids in translated_by_language[language].values())} resources")
        
        # Apply limit to each resource type if specified
        resource_limit = limit // len(resource_types) if limit and resource_types else limit
        
        if not resource_types or 'product' in resource_types:
            products = shopify_service.get_products(limit=resource_limit or 0, published_status='published')
            for product in products:
                resources.append({
                    'type': 'product',
                    'id': product['id'],
                    'content': {
                        'title': product.get('title'),
                        'description': product.get('body_html')
                    }
                })
        
        if not resource_types or 'collection' in resource_types:
            collections = shopify_service.get_collections(limit=resource_limit or 0)
            for collection in collections:
                resources.append({
                    'type': 'collection',
                    'id': collection['id'],
                    'content': {
                        'title': collection.get('title'),
                        'description': collection.get('body_html')
                    }
                })
        
        if not resource_types or 'page' in resource_types:
            pages = shopify_service.get_online_store_pages(limit=resource_limit or 0)
            for page in pages:
                resources.append({
                    'type': 'page',
                    'id': page['id'],
                    'content': {
                        'title': page.get('title'),
                        'description': page.get('body_html')
                    }
                })
        
        # Calculate actual tasks (excluding already translated)
        actual_tasks = 0
        for language in target_languages:
            for resource in resources:
                resource_id = str(resource['id'])
                if resource_id not in translated_by_language[language].get(resource['type'], set()):
                    actual_tasks += 1
        
        print(f"Total resources: {len(resources)}, Languages: {len(target_languages)}, Actual tasks to process: {actual_tasks}")
        
        processed = 0
        succeeded = 0
        failed = 0
        skipped = 0
        
        for language in target_languages:
            for resource in resources:
                try:
                    # Skip already translated resources
                    resource_id = str(resource['id'])
                    if resource_id in translated_by_language[language].get(resource['type'], set()):
                        skipped += 1
                        processed += 1
                        print(f"Skipping {resource['type']} {resource_id} - already translated to {language}")
                        continue
                    
                    self.update_state(state='PROGRESS', meta={
                        'status': f'Translating {resource["type"]} to {language}...',
                        'current': processed,
                        'total': actual_tasks
                    })
                    
                    # Call individual translation task - don't use .get() to avoid blocking
                    translate_content_task.apply_async(
                        args=[shop.id, resource['type'], resource['id'], 
                              resource['content'], language, seo_keywords]
                    )
                    
                    # For now, count as succeeded since we can't track subtasks synchronously
                    succeeded += 1
                        
                    processed += 1
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    failed += 1
                    processed += 1
                    print(f"Error translating {resource['type']} {resource['id']}: {str(e)}")
        
        # Update job
        job.status = 'completed'
        job.result_data = json.dumps({
            'total': actual_tasks,
            'translated': succeeded,
            'failed': failed,
            'skipped': skipped
        })
        db.session.commit()
        
        return {
            'success': True,
            'total': actual_tasks,
            'translated': succeeded,
            'failed': failed,
            'skipped': skipped
        }
        
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='daily_automation')
def daily_automation_task(self, shop_id):
    """Daily automation task that runs based on shop settings and quota system"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: daily_automation")
        print(f"Task ID: {self.request.id}")
        print(f"Shop ID: {shop_id}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"STARTING DAILY AUTOMATION PROCESSING...")
        print(f"{'='*60}\n")
        
        from app.services.quota_service import calculate_quota_with_deficit
        from app.models.ai_config import AIConfig
        
        shop = Shop.query.get(shop_id)
        if not shop:
            return {'success': False, 'message': 'Shop not found'}
        
        # Check for existing running automation to prevent duplicates
        existing_job = AutomationJob.query.filter_by(
            shop_id=shop_id,
            task_type='daily_automation',
            status='processing'
        ).first()

        if existing_job:
            print(f"Daily automation already running for shop {shop_id} (Job ID: {existing_job.task_id})")
            return {'success': False, 'message': 'Daily automation already running'}

        # Check if daily automation already completed today
        from datetime import datetime, timedelta
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)

        completed_today = AutomationJob.query.filter(
            AutomationJob.shop_id == shop_id,
            AutomationJob.task_type == 'daily_automation',
            AutomationJob.status == 'completed',
            AutomationJob.created_at >= today_start
        ).first()

        if completed_today:
            print(f"Daily automation already completed today for shop {shop_id} at {completed_today.created_at}")
            return {'success': True, 'message': 'Daily automation already completed today', 'skipped': True}
        
        # Get automation configuration
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type='seo_automation'
        ).first()
        
        if not config or not config.config_data:
            return {'success': False, 'message': 'Automation not configured'}
        
        settings = get_config_data(config)
        if not settings:
            return {'success': False, 'message': 'Invalid automation configuration'}
        
        print(f"Settings loaded: {settings}")
        print(f"  - seo_enabled: {settings.get('seo_enabled', False)}")
        print(f"  - collection_enabled: {settings.get('collection_enabled', False)}")
        print(f"  - translation_enabled: {settings.get('translation_enabled', False)}")
        
        # Get shop product count
        # Initialize Shopify service to get product count
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured':
            return {'success': False, 'message': 'Shop domain not configured'}
            
        from app.services.shopify_service import ShopifyService
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Get count of products available on Online Store only
        try:
            total_products = shopify_service.get_products_count(published_status='published')
        except Exception as e:
            print(f"Error getting product count: {e}")
            total_products = 0
        
        # Calculate today's quotas
        quota_info = calculate_quota_with_deficit(shop_id, total_products)

        # Create automation job to track this run
        automation_job = AutomationJob(
            shop_id=shop_id,
            task_type='daily_automation',
            status='processing',
            task_id=self.request.id
        )
        db.session.add(automation_job)
        db.session.commit()
        
        # Run product optimization if enabled and quota available
        if settings.get('seo_enabled', False) and quota_info['remaining']['products'] > 0:
            print(f"\n[PRODUCTS] SEO Optimization enabled")
            print(f"  - Quota available: {quota_info['remaining']['products']} products")
            print(f"  - Queuing bulk optimization task...")
            
            result = optimize_all_products_task.apply_async(
                args=[shop_id, None, quota_info['remaining']['products']]
            )
            print(f"  - Task ID: {result.id}")
            print(f"  - Status: QUEUED")
        
        # Schedule collection creation check if enabled
        # This needs to run AFTER products are optimized, so we delay it
        if settings.get('collection_enabled', False) and quota_info['remaining']['collections'] > 0:
            # Calculate delay based on number of products being processed
            # Each product takes ~30 seconds, plus some buffer time
            products_being_processed = quota_info['remaining']['products']
            delay_seconds = (products_being_processed * 30) + 60  # Add 60 seconds buffer
            
            print(f"\n[COLLECTIONS] Smart Collections enabled")
            print(f"  - Will check for new tags after {delay_seconds/60:.1f} minutes")
            print(f"  - Collection quota available: {quota_info['remaining']['collections']}")
            
            check_and_create_collections_task.apply_async(
                args=[shop_id, quota_info['remaining']['collections'], settings.get('optimization_keywords', [])],
                countdown=delay_seconds
            )
        
        # Note: Translations happen automatically after product/collection creation
        # if translation_enabled is True in settings
        
        # Schedule Alt Text Optimization if enabled and OpenAI is available
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if settings.get('alt_text_enabled', False) and openai_api_key:
            # Calculate delay to run after products are optimized
            products_being_processed = quota_info['remaining']['products']
            alt_text_delay = (products_being_processed * 30) + 120  # Add 2 minutes buffer after products
            
            print(f"\n[ALT TEXT] Image Alt Text Optimization enabled")
            print(f"  - Will start after {alt_text_delay/60:.1f} minutes (after product optimization)")
            print(f"  - Daily limit: 25 images")
            
            daily_alt_text_optimization_task.apply_async(
                args=[shop_id, 25],  # 25 images per day
                countdown=alt_text_delay
            )
        elif settings.get('alt_text_enabled', False):
            print(f"\n[ALT TEXT] Alt Text Optimization enabled but OpenAI API key not configured")
        
        # Schedule Search Console automation if enabled
        # This runs immediately to submit current sitemaps
        if settings.get('search_console_enabled', False):
            print(f"\n[SEARCH CONSOLE] Automation enabled")
            print(f"  - Submitting sitemaps immediately")
            
            search_console_automation_task.apply_async(
                args=[shop_id]
            )
        
        # Mark automation job as completed
        automation_job.status = 'completed'
        automation_job.result_data = json.dumps({
            'products_queued': quota_info['remaining']['products'],
            'collections_queued': min(len(available_tags) if 'available_tags' in locals() else 0,
                                     quota_info['remaining']['collections'])
        })
        db.session.commit()

        return {
            'success': True,
            'quota_info': {
                'products_queued': quota_info['remaining']['products'],
                'collections_queued': min(len(available_tags) if 'available_tags' in locals() else 0,
                                         quota_info['remaining']['collections'])
            }
        }
        
    except Exception as e:
        print(f"Daily automation error for shop {shop_id}: {str(e)}")
        # Mark automation job as failed if it exists
        if 'automation_job' in locals():
            automation_job.status = 'failed'
            automation_job.error_message = str(e)
            db.session.commit()
        return {'success': False, 'error': str(e)}


@celery.task(bind=True, name='check_and_create_collections')
def check_and_create_collections_task(self, shop_id, collection_quota, seo_keywords=None):
    """Check for new tags and create collections after products are optimized"""
    try:
        print(f"\n{'='*60}")
        print(f"CHECKING FOR NEW COLLECTION TAGS")
        print(f"Shop ID: {shop_id}")
        print(f"Collection Quota: {collection_quota}")
        print(f"{'='*60}\n")
        
        from app.services.change_tracking_service import ChangeTrackingService
        
        # Get available tags from optimized products
        available_tags = ChangeTrackingService.get_new_tags_for_collections(shop_id)
        
        if available_tags:
            # Limit to quota
            tags_to_process = available_tags[:collection_quota]
            print(f"Found {len(available_tags)} new tags, processing {len(tags_to_process)} based on quota")
            
            # Queue collection creation
            result = create_all_collections_task.apply_async(
                args=[shop_id, tags_to_process, 'smart', seo_keywords or []]
            )
            print(f"Queued collection creation task: {result.id}")
            
            # Schedule translation task to run after collections are created
            # Estimate time: 30 seconds per collection + 60 seconds buffer
            translation_delay = (len(tags_to_process) * 30) + 60
            
            # Check if translations are enabled
            from app.models.ai_config import AIConfig
            config = AIConfig.query.filter_by(
                shop_id=shop_id,
                module_type='seo_automation'
            ).first()
            
            config_data = get_config_data(config)
            if config and config_data.get('translation_enabled') and config_data.get('target_languages'):
                print(f"Scheduling translations to start in {translation_delay} seconds (after collections)")
                trigger_all_translations_task.apply_async(
                    args=[shop_id],
                    countdown=translation_delay
                )
            
            return {
                'success': True,
                'tags_found': len(available_tags),
                'tags_queued': len(tags_to_process)
            }
        else:
            print("No new tags found for collection creation")
            
            # Even if no collections, check if we should translate existing content
            from app.models.ai_config import AIConfig
            config = AIConfig.query.filter_by(
                shop_id=shop_id,
                module_type='seo_automation'
            ).first()
            
            config_data = get_config_data(config)
            if config and config_data.get('translation_enabled') and config_data.get('target_languages'):
                print("No collections to create, but checking if translations are needed")
                trigger_all_translations_task.apply_async(
                    args=[shop_id],
                    countdown=10  # Start translations in 10 seconds
                )
            
            return {
                'success': True,
                'tags_found': 0,
                'tags_queued': 0
            }
            
    except Exception as e:
        print(f"Error checking for collections: {e}")
        return {'success': False, 'error': str(e)}


@celery.task(bind=True, name='trigger_all_translations')
def trigger_all_translations_task(self, shop_id):
    """Trigger translations for all optimized content after collections are created"""
    try:
        print(f"\n{'='*60}")
        print(f"TRIGGERING TRANSLATIONS FOR ALL CONTENT")
        print(f"Shop ID: {shop_id}")
        print(f"{'='*60}\n")
        
        from app.models.ai_config import AIConfig
        from app.services.change_tracking_service import ChangeTrackingService
        from app.models.change_log import ChangeLog
        
        # Get automation config
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type='seo_automation'
        ).first()
        
        config_data = get_config_data(config)
        if not config or not config_data.get('translation_enabled'):
            print("Translations not enabled")
            return {'success': True, 'message': 'Translations not enabled'}
        
        target_languages = config_data.get('target_languages', [])
        if not target_languages:
            print("No target languages configured")
            return {'success': True, 'message': 'No target languages configured'}
        
        seo_keywords = config_data.get('optimization_keywords', [])
        
        # Get shop
        shop = Shop.query.get(shop_id)
        if not shop:
            return {'success': False, 'error': 'Shop not found'}
        
        # Initialize Shopify service
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured':
            return {'success': False, 'error': 'Shop domain not configured'}
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Get shop locales and filter to enabled target languages
        shop_locales = shopify_service.get_shop_locales()
        enabled_locales = [l['locale'] for l in shop_locales if l.get('published') and not l.get('primary')]
        target_languages = [lang for lang in target_languages if lang in enabled_locales]
        
        if not target_languages:
            print("No enabled target languages found")
            return {'success': True, 'message': 'No enabled target languages'}
        
        print(f"Target languages: {target_languages}")
        
        # Get all optimized products
        optimized_product_ids = ChangeTrackingService.get_all_optimized_product_ids(shop_id)
        print(f"Found {len(optimized_product_ids)} optimized products")
        
        # Get all created collections with their data
        collection_changes = ChangeLog.query.filter_by(
            shop_id=shop_id,
            resource_type='collection',
            action='create'
        ).all()
        
        # Build a list of collections with their data from change logs
        collections_to_translate = []
        for change in collection_changes:
            if change.after_data:
                collections_to_translate.append({
                    'id': change.resource_id,
                    'data': change.after_data
                })
        
        print(f"Found {len(collections_to_translate)} created collections")
        
        task_count = 0
        
        # Queue translations for products
        for idx, product_id in enumerate(optimized_product_ids):
            # Get product data
            product = shopify_service.get_product(product_id)
            if not product:
                continue
                
            for lang_idx, language in enumerate(target_languages):
                # Check if already translated
                status = ChangeTrackingService.get_translation_status(
                    shop_id=shop_id,
                    resource_type='product',
                    resource_id=product_id,
                    language=language
                )
                
                if status and status.is_translated:
                    continue  # Skip already translated
                
                # Calculate delay to spread out API calls
                countdown_seconds = (task_count * 30)  # 30 seconds per task
                
                translate_content_task.apply_async(
                    args=[shop_id, 'product', product_id, {
                        'title': product.get('title'),
                        'body_html': product.get('body_html'),
                        'meta_title': product.get('metafields', {}).get('title_tag', ''),
                        'meta_description': product.get('metafields', {}).get('description_tag', '')
                    }, language, seo_keywords],
                    countdown=countdown_seconds
                )
                task_count += 1
        
        # Queue translations for collections
        for idx, collection_info in enumerate(collections_to_translate):
            collection_id = collection_info['id']
            collection_data = collection_info['data']
                
            for lang_idx, language in enumerate(target_languages):
                # Check if already translated
                status = ChangeTrackingService.get_translation_status(
                    shop_id=shop_id,
                    resource_type='collection',
                    resource_id=collection_id,
                    language=language
                )
                
                if status and status.is_translated:
                    continue  # Skip already translated
                
                # Calculate delay to spread out API calls
                countdown_seconds = (task_count * 30)  # 30 seconds per task
                
                translate_content_task.apply_async(
                    args=[shop_id, 'collection', collection_id, {
                        'title': collection_data.get('title'),
                        'body_html': collection_data.get('body_html'),
                        'meta_title': '',  # Collections don't have meta fields in our create data
                        'meta_description': ''
                    }, language, seo_keywords],
                    countdown=countdown_seconds
                )
                task_count += 1
        
        print(f"Queued {task_count} translation tasks")
        
        return {
            'success': True,
            'tasks_queued': task_count,
            'languages': target_languages,
            'products': len(optimized_product_ids),
            'collections': len(collections_to_translate)
        }
        
    except Exception as e:
        print(f"Error triggering translations: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}


# REMOVED: check_automation_queues task - replaced by individual shop scheduling
# This task was causing conflicts with the per-shop Celery Beat scheduling
# Each shop now has its own scheduled time slot with 20-minute intervals


@celery.task(name='cleanup_old_jobs')
def cleanup_old_jobs():
    """Clean up old automation job records"""
    try:
        from datetime import datetime, timedelta
        
        # Delete jobs older than 30 days
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        old_jobs = AutomationJob.query.filter(
            AutomationJob.created_at < cutoff_date
        ).all()
        
        count = len(old_jobs)
        for job in old_jobs:
            db.session.delete(job)
        
        db.session.commit()
        print(f"Cleaned up {count} old automation jobs")
        
        return {'success': True, 'deleted': count}
        
    except Exception as e:
        print(f"Error cleaning up old jobs: {str(e)}")
        return {'success': False, 'error': str(e)}


@celery.task(bind=True, name='search_console_automation')
def search_console_automation_task(self, shop_id):
    """Search Console automation task - submits sitemaps after other automations complete"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: search_console_automation")
        print(f"Task ID: {self.request.id}")
        print(f"Shop ID: {shop_id}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        from app.models.ai_config import AIConfig
        from app.models.google_oauth import GoogleOAuth
        from app.services.search_console_service import SearchConsoleService
        from app.models.automation_job import AutomationJob
        from datetime import datetime
        import requests
        from urllib.parse import urlparse
        import xml.etree.ElementTree as ET
        
        # Check if Search Console automation is enabled
        config = AIConfig.query.filter_by(
            shop_id=shop_id,
            module_type='seo_automation'
        ).first()
        
        config_data = get_config_data(config)
        if not config or not config_data.get('search_console_enabled'):
            print("Search Console automation not enabled")
            return {'success': True, 'message': 'Search Console automation not enabled'}
        
        # Check if Google OAuth is connected
        google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
        if not google_oauth:
            print("Google OAuth not connected")
            return {'success': False, 'error': 'Google OAuth not connected'}
        
        # Get shop
        shop = Shop.query.get(shop_id)
        if not shop:
            return {'success': False, 'error': 'Shop not found'}
        
        # Get the store's actual live URL
        from app.models.access_control import UserSettings, AccessKey
        user_settings = UserSettings.query.join(AccessKey).filter(
            AccessKey.user_shops.any(shop_id=shop_id)
        ).first()
        
        if user_settings and user_settings.store_actual_live_url:
            site_url = user_settings.store_actual_live_url.rstrip('/')
        else:
            # Fallback to shop domain
            shop_domain = shop.shopify_shop_domain or shop.shop_domain
            site_url = f"https://{shop_domain}"
        
        # Initialize Search Console service
        try:
            service = SearchConsoleService(shop_id)
        except Exception as e:
            print(f"Failed to initialize Search Console service: {e}")
            return {'success': False, 'error': str(e)}
        
        # Get the Search Console property
        property_url = service.get_default_property()
        if not property_url:
            print(f"No Search Console property found for {site_url}")
            return {'success': False, 'error': 'No Search Console property configured'}
        
        print(f"Using Search Console property: {property_url}")
        
        # Create automation job
        job = AutomationJob(
            shop_id=shop_id,
            task_type='search_console_automation',
            status='processing',
            job_metadata=json.dumps({'property': property_url})
        )
        db.session.add(job)
        db.session.commit()
        
        try:
            # Check for sitemap changes by looking at recent automation jobs
            last_job = AutomationJob.query.filter_by(
                shop_id=shop_id,
                task_type='search_console_automation',
                status='completed'
            ).order_by(AutomationJob.created_at.desc()).first()
            
            # For sitemap fetching, we need to use the Shopify domain
            shop_domain = shop.shopify_shop_domain or shop.shop_domain
            fetch_sitemap_url = f"https://{shop_domain}/sitemap.xml"
            submit_sitemap_url = f"{property_url.rstrip('/')}/sitemap.xml"
            
            # Fetch current sitemap
            print(f"Fetching main sitemap from: {fetch_sitemap_url}")
            response = requests.get(fetch_sitemap_url)
            response.raise_for_status()
            
            # Parse sitemap XML
            root = ET.fromstring(response.content)
            namespace = {'ns': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
            
            # Get current sitemap URLs
            current_sitemaps = set()
            sitemap_elements = root.findall('.//ns:sitemap/ns:loc', namespace) or root.findall('.//sitemap/loc')
            
            if sitemap_elements:
                # This is a sitemap index
                current_sitemaps.add(submit_sitemap_url)
                for loc in sitemap_elements:
                    if loc.text:
                        current_sitemaps.add(loc.text)
            else:
                # Regular sitemap
                current_sitemaps.add(submit_sitemap_url)
            
            # Check if sitemaps have changed since last run
            sitemap_changed = True
            if last_job and last_job.result_data:
                try:
                    last_result = json.loads(last_job.result_data)
                    last_sitemaps = set(last_result.get('sitemaps', []))
                    
                    if current_sitemaps == last_sitemaps:
                        sitemap_changed = False
                        print("No changes detected in sitemaps since last run")
                except:
                    pass
            
            sitemaps_submitted = []
            
            if sitemap_changed:
                print(f"Sitemap changes detected, submitting {len(current_sitemaps)} sitemaps")
                
                # Submit sitemaps
                for sitemap_url in current_sitemaps:
                    if service.submit_sitemap(property_url, sitemap_url):
                        sitemaps_submitted.append(sitemap_url)
                        print(f"✓ Submitted sitemap: {sitemap_url}")
                    else:
                        print(f"✗ Failed to submit sitemap: {sitemap_url}")
                
                print(f"\n✅ Sitemap submission completed: {len(sitemaps_submitted)} submitted")
            else:
                print("✅ Skipping sitemap submission - no changes detected")
            
            # Always fetch metrics after sitemap check
            print("\n" + "="*60)
            print("Moving on to metrics fetching...")
            print("="*60)
            
            # Queue metrics fetch task
            metrics_result = fetch_search_console_metrics_task.apply_async(
                args=[shop_id, False]  # False = not initial fetch
            )
            
            # Update job with results
            job.status = 'completed'
            job.result_data = json.dumps({
                'success': True,
                'sitemaps_submitted': len(sitemaps_submitted),
                'sitemaps': list(current_sitemaps),
                'sitemap_changed': sitemap_changed,
                'metrics_task_id': metrics_result.id if 'metrics_result' in locals() else None
            })
            job.job_metadata = json.dumps({
                'property': property_url,
                'completed_at': datetime.utcnow().isoformat()
            })
            
            print(f"\n✅ Search Console automation completed")
            print(f"   Sitemaps checked: {len(current_sitemaps)}")
            print(f"   Sitemaps submitted: {len(sitemaps_submitted)} {'(no changes)' if not sitemap_changed else ''}")
            print(f"   Metrics task queued: {metrics_result.id if 'metrics_result' in locals() else 'No'}")
            
        except Exception as e:
            print(f"Error during Search Console automation: {str(e)}")
            job.status = 'failed'
            job.error_message = str(e)
            job.result_data = json.dumps({'success': False, 'error': str(e)})
            raise
        
        finally:
            db.session.commit()
        
        return {
            'success': True,
            'sitemaps_submitted': len(sitemaps_submitted),
            'property': property_url
        }
        
    except Exception as e:
        print(f"Search Console automation error for shop {shop_id}: {str(e)}")
        return {'success': False, 'error': str(e)}


@celery.task(bind=True, name='fetch_search_console_metrics')
def fetch_search_console_metrics_task(self, shop_id, initial_fetch=False):
    """Fetch Search Console performance metrics for all created URLs"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: fetch_search_console_metrics")
        print(f"Task ID: {self.request.id}")
        print(f"Shop ID: {shop_id}")
        print(f"Initial fetch: {initial_fetch}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        from app.models import GoogleOAuth, SearchConsoleMetrics
        from app.models.change_log import ChangeLog, TranslationStatus
        from app.services.search_console_service import SearchConsoleService
        from app.models.automation_job import AutomationJob
        from datetime import datetime, timedelta, date
        from app.models.access_control import UserSettings, AccessKey
        
        # Check if Google OAuth is connected
        google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
        if not google_oauth:
            print("Google OAuth not connected")
            return {'success': False, 'error': 'Google OAuth not connected'}
        
        # Get shop
        shop = Shop.query.get(shop_id)
        if not shop:
            return {'success': False, 'error': 'Shop not found'}
        
        # Initialize Search Console service
        try:
            service = SearchConsoleService(shop_id)
        except Exception as e:
            print(f"Failed to initialize Search Console service: {e}")
            return {'success': False, 'error': str(e)}
        
        # Get the Search Console property
        property_url = service.get_default_property()
        if not property_url:
            print("No Search Console property configured")
            return {'success': False, 'error': 'No Search Console property configured'}
        
        print(f"Using Search Console property: {property_url}")
        
        # Create automation job
        job = AutomationJob(
            shop_id=shop_id,
            task_type='search_console_metrics',
            status='processing',
            job_metadata=json.dumps({'property': property_url, 'initial_fetch': initial_fetch})
        )
        db.session.add(job)
        db.session.commit()
        
        try:
            # Get date range for metrics
            # Search Console data is typically 2-3 days behind
            end_date = date.today() - timedelta(days=2)
            if initial_fetch:
                # For initial fetch, get last 28 days
                start_date = end_date - timedelta(days=28)
            else:
                # For daily updates, get last 7 days to catch any delayed data
                start_date = end_date - timedelta(days=7)
            
            # Get shop domain for URL construction
            shop_domain = shop.shopify_shop_domain or shop.shop_domain
            
            # Collect all URLs to check
            urls_to_check = []
            
            # 1. Get all optimized products
            product_changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='product',
                action='optimize_seo'
            ).all()
            
            for change in product_changes:
                if change.after_data and change.resource_id:
                    # Add main product URL
                    handle = change.after_data.get('handle')
                    if handle:
                        urls_to_check.append({
                            'url': f"/products/{handle}",
                            'type': 'product',
                            'resource_id': change.resource_id,
                            'language': 'en',
                            'is_translation': False
                        })
            
            # 2. Get all created collections
            collection_changes = ChangeLog.query.filter_by(
                shop_id=shop_id,
                resource_type='collection',
                action='create'
            ).all()
            
            for change in collection_changes:
                if change.after_data and change.resource_id:
                    handle = change.after_data.get('handle')
                    if handle:
                        urls_to_check.append({
                            'url': f"/collections/{handle}",
                            'type': 'collection',
                            'resource_id': change.resource_id,
                            'language': 'en',
                            'is_translation': False
                        })
            
            # 3. Get all translations
            translations = TranslationStatus.query.filter_by(
                shop_id=shop_id,
                is_translated=True
            ).all()
            
            for trans in translations:
                # Get translated handle if available
                handle = None
                if trans.translated_data and 'handle' in trans.translated_data:
                    handle = trans.translated_data['handle']
                else:
                    # Try to get from the original resource
                    if trans.resource_type == 'product':
                        product_change = ChangeLog.query.filter_by(
                            shop_id=shop_id,
                            resource_type='product',
                            resource_id=trans.resource_id
                        ).first()
                        if product_change and product_change.after_data:
                            handle = product_change.after_data.get('handle')
                    elif trans.resource_type == 'collection':
                        collection_change = ChangeLog.query.filter_by(
                            shop_id=shop_id,
                            resource_type='collection',
                            resource_id=trans.resource_id
                        ).first()
                        if collection_change and collection_change.after_data:
                            handle = collection_change.after_data.get('handle')
                
                if handle:
                    lang_code = trans.language.lower()
                    urls_to_check.append({
                        'url': f"/{lang_code}/{trans.resource_type}s/{handle}",
                        'type': trans.resource_type,
                        'resource_id': trans.resource_id,
                        'language': trans.language,
                        'is_translation': True
                    })
            
            print(f"Found {len(urls_to_check)} URLs to check for metrics")
            
            # Process URLs in batches with delay
            metrics_fetched = 0
            errors = []
            
            for i, url_data in enumerate(urls_to_check):
                try:
                    # Add delay between requests (0.3 seconds as requested)
                    if i > 0:
                        time.sleep(0.3)
                    
                    url_path = url_data['url']
                    print(f"Fetching metrics for: {url_path}")
                    
                    # Use dimension filter to get data for specific URL
                    dimension_filter_groups = [{
                        'filters': [{
                            'dimension': 'page',
                            'operator': 'contains',
                            'expression': url_path
                        }]
                    }]
                    
                    # Fetch performance data
                    result = service.query_search_analytics(
                        site_url=property_url,
                        start_date=start_date,
                        end_date=end_date,
                        dimensions=['page', 'query'],
                        dimension_filter_groups=dimension_filter_groups,
                        row_limit=100
                    )
                    
                    # Process results
                    rows = result.get('rows', [])
                    if rows:
                        # Aggregate metrics for this URL
                        total_clicks = 0
                        total_impressions = 0
                        position_sum = 0
                        position_count = 0
                        top_queries = []
                        
                        for row in rows:
                            # Check if this row matches our exact URL
                            page_url = row.get('keys', [None])[0]
                            if page_url and url_path in page_url:
                                query = row.get('keys', [None, None])[1]
                                clicks = row.get('clicks', 0)
                                impressions = row.get('impressions', 0)
                                position = row.get('position', 0)
                                
                                total_clicks += clicks
                                total_impressions += impressions
                                if position > 0:
                                    position_sum += position * impressions  # Weighted by impressions
                                    position_count += impressions
                                
                                if query and (clicks > 0 or impressions > 10):
                                    top_queries.append({
                                        'query': query,
                                        'clicks': clicks,
                                        'impressions': impressions,
                                        'position': round(position, 1)
                                    })
                        
                        # Sort top queries by clicks
                        top_queries.sort(key=lambda x: x['clicks'], reverse=True)
                        top_queries = top_queries[:10]  # Keep top 10
                        
                        # Calculate average position
                        avg_position = round(position_sum / position_count, 1) if position_count > 0 else 0
                        
                        # Calculate CTR
                        ctr = round((total_clicks / total_impressions * 100), 2) if total_impressions > 0 else 0
                        
                        # Get previous metrics for comparison
                        previous_metrics = SearchConsoleMetrics.query.filter_by(
                            shop_id=shop_id,
                            url=url_path
                        ).order_by(SearchConsoleMetrics.metrics_date.desc()).first()
                        
                        # Store metrics
                        metrics = SearchConsoleMetrics(
                            shop_id=shop_id,
                            url=url_path,
                            resource_type=url_data['type'],
                            resource_id=url_data['resource_id'],
                            language=url_data['language'],
                            is_translation=url_data['is_translation'],
                            clicks=total_clicks,
                            impressions=total_impressions,
                            ctr=ctr,
                            position=avg_position,
                            top_queries=top_queries,
                            metrics_date=end_date
                        )
                        
                        # Calculate changes
                        metrics.calculate_changes(previous_metrics)
                        
                        db.session.add(metrics)
                        metrics_fetched += 1
                        
                        print(f"  ✓ Metrics: {total_clicks} clicks, {total_impressions} impressions, {ctr}% CTR")
                    else:
                        print(f"  - No data available yet")
                
                except Exception as e:
                    error_msg = f"Error fetching metrics for {url_path}: {str(e)}"
                    print(f"  ✗ {error_msg}")
                    errors.append(error_msg)
            
            # Commit all metrics
            db.session.commit()
            
            # Update job status
            job.status = 'completed'
            job.result_data = json.dumps({
                'success': True,
                'metrics_fetched': metrics_fetched,
                'urls_checked': len(urls_to_check),
                'errors': len(errors)
            })
            job.job_metadata = json.dumps({
                'property': property_url,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'completed_at': datetime.utcnow().isoformat()
            })
            
            print(f"\n✅ Search Console metrics fetch completed")
            print(f"   URLs checked: {len(urls_to_check)}")
            print(f"   Metrics fetched: {metrics_fetched}")
            print(f"   Errors: {len(errors)}")
            
        except Exception as e:
            print(f"Error during Search Console metrics fetch: {str(e)}")
            job.status = 'failed'
            job.error_message = str(e)
            job.result_data = json.dumps({'success': False, 'error': str(e)})
            raise
        
        finally:
            db.session.commit()
        
        return {
            'success': True,
            'metrics_fetched': metrics_fetched,
            'urls_checked': len(urls_to_check)
        }
        
    except Exception as e:
        print(f"Search Console metrics error for shop {shop_id}: {str(e)}")
        return {'success': False, 'error': str(e)}


@celery.task(bind=True, name='optimize_product_image_alt_text')
def optimize_product_image_alt_text_task(self, shop_id, product_id, limit_per_product=5):
    """Background task to optimize alt text for a single product's images"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: optimize_product_image_alt_text")
        print(f"Task ID: {self.request.id}")
        print(f"Product ID: {product_id}")
        print(f"Shop ID: {shop_id}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        # Update task state
        self.update_state(state='PROGRESS', meta={'status': 'Starting alt text optimization...'})
        
        # Get shop and API keys - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            # Try to find by user settings ID
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                # Try both fields
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    # Update API keys if needed
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception("Shop not found")
        
        # Check if OpenAI is available - get from user settings
        from app.models.access_control import UserSettings
        user_settings = UserSettings.query.filter_by(shopify_shop_domain=shop.shopify_shop_domain).first()
        if not user_settings or not user_settings.openai_api_key:
            raise Exception("OpenAI API key not configured for this store - required for alt text optimization")
        
        openai_api_key = user_settings.openai_api_key
        
        # Create job record
        job = AutomationJob(
            shop_id=shop.id,
            task_type='alt_text_optimization',
            resource_id=str(product_id),
            status='processing',
            task_id=self.request.id
        )
        db.session.add(job)
        db.session.commit()
        time.sleep(0.3)  # Prevent database locking
        
        # Initialize services
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured' or shop_domain == 'none':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Import OpenAI service here to handle potential import errors
        try:
            from app.services.openai_service import OpenAIService
            openai_service = OpenAIService(api_key=openai_api_key)
        except Exception as e:
            raise Exception(f"Failed to initialize OpenAI service: {str(e)}")
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'status': 'Fetching product images...'})
        
        # Get product with images and context
        product_data = shopify_service.get_product_images_with_context(product_id)
        if not product_data:
            raise Exception(f"Product {product_id} not found or has no images")
        
        images = product_data['images']
        product_context = product_data['product']
        variants = product_data['variants']
        
        # Filter images that need alt text
        images_needing_alt_text = []
        for image in images:
            if not image.get('altText') or image['altText'].strip() == '':
                images_needing_alt_text.append(image)
        
        if not images_needing_alt_text:
            # Mark as completed but skipped
            job.status = 'completed'
            job.result_data = json.dumps({
                'success': True,
                'skipped': True,
                'reason': 'All images already have alt text',
                'total_images': len(images)
            })
            db.session.commit()
            return {
                'success': True,
                'skipped': True,
                'message': 'All images already have alt text'
            }
        
        # Limit images per product to control API costs
        images_to_process = images_needing_alt_text[:limit_per_product]
        
        self.update_state(state='PROGRESS', meta={'status': f'Generating alt text for {len(images_to_process)} images...'})
        
        # Process each image
        processed_images = 0
        successful_updates = []
        failed_updates = []
        
        for i, image in enumerate(images_to_process):
            try:
                self.update_state(state='PROGRESS', meta={
                    'status': f'Processing image {i+1}/{len(images_to_process)}...',
                    'current': i,
                    'total': len(images_to_process)
                })
                
                # Generate alt text using OpenAI
                result = openai_service.optimize_image_alt_text(
                    image_url=image['url'],
                    product_context=product_context,
                    variants=variants
                )
                
                if result.get('success') and result.get('alt_text'):
                    # Prepare image update for Shopify GraphQL
                    image_update = {
                        'id': image['id'],
                        'alt': result['alt_text']
                    }
                    
                    # Update alt text in Shopify
                    update_success = shopify_service.update_product_image_alt_text(
                        product_id, [image_update]
                    )
                    
                    if update_success:
                        successful_updates.append({
                            'image_id': image['id'],
                            'image_url': image['url'],
                            'before_alt_text': image.get('altText', '') or '(no alt text)',
                            'after_alt_text': result['alt_text'],
                            'used_vision': result.get('used_vision', False),
                            'tokens_used': result.get('tokens_used', 0),
                            'shopify_edit_url': f"https://{shop_domain}/admin/products/{product_id}"
                        })
                        print(f"✓ Updated alt text for image {image['id']}: {result['alt_text']}")
                    else:
                        failed_updates.append({
                            'image_id': image['id'],
                            'image_url': image['url'],
                            'error': 'Failed to update in Shopify',
                            'alt_text_attempted': result.get('alt_text', 'N/A')
                        })
                        print(f"✗ Failed to update image {image['id']} in Shopify")
                else:
                    error_msg = result.get('error', 'Unknown error generating alt text')
                    failed_updates.append({
                        'image_id': image['id'],
                        'image_url': image['url'],
                        'error': error_msg,
                        'before_alt_text': image.get('altText', '') or '(no alt text)'
                    })
                    print(f"✗ Failed to generate alt text for image {image['id']}: {error_msg}")
                
                processed_images += 1
                
                # Rate limiting - small delay between images
                if i < len(images_to_process) - 1:
                    time.sleep(1)
                    
            except Exception as e:
                failed_updates.append({
                    'image_id': image['id'],
                    'image_url': image.get('url', ''),
                    'error': str(e),
                    'before_alt_text': image.get('altText', '') or '(no alt text)'
                })
                processed_images += 1
                print(f"Error processing image {image['id']}: {str(e)}")
        
        # Log the changes if any successful updates
        if successful_updates:
            ChangeTrackingService.log_change(
                shop_id=shop.id,
                resource_type='product_images',
                resource_id=str(product_id),
                action='optimize_alt_text',
                before_data={
                    'images_without_alt_text': len(images_needing_alt_text)
                },
                after_data={
                    'images_updated': len(successful_updates),
                    'alt_texts_added': [update['after_alt_text'] for update in successful_updates]
                },
                metadata={
                    'model_used': 'gpt-4o-mini',
                    'automated': True,
                    'total_tokens_used': sum(update.get('tokens_used', 0) for update in successful_updates),
                    'vision_used_count': sum(1 for update in successful_updates if update.get('used_vision'))
                }
            )
        
        # Update job status with comprehensive results
        job.status = 'completed'
        job.result_data = json.dumps({
            'success': True,
            'product_id': product_id,
            'product_title': product_context.get('title', 'Unknown Product'),
            'shopify_edit_url': f"https://{shop_domain}/admin/products/{product_id}",
            'total_images': len(images),
            'images_needing_alt_text': len(images_needing_alt_text),
            'images_processed': processed_images,
            'successful_updates': len(successful_updates),
            'failed_updates': len(failed_updates),
            'total_tokens_used': sum(update.get('tokens_used', 0) for update in successful_updates),
            'vision_api_calls': sum(1 for update in successful_updates if update.get('used_vision', False)),
            'updates': successful_updates,
            'errors': failed_updates
        })
        db.session.commit()
        time.sleep(0.3)  # Prevent database locking
        
        return {
            'success': True,
            'product_id': product_id,
            'images_updated': len(successful_updates),
            'images_failed': len(failed_updates)
        }
            
    except SoftTimeLimitExceeded:
        job.status = 'failed'
        job.error_message = 'Task timed out'
        db.session.commit()
        time.sleep(0.3)
        raise
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
            time.sleep(0.3)
        raise


@celery.task(bind=True, name='daily_alt_text_optimization')
def daily_alt_text_optimization_task(self, shop_id, daily_limit=25):
    """Daily task to optimize alt text for images across the store"""
    try:
        # Log task receipt
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: daily_alt_text_optimization")
        print(f"Task ID: {self.request.id}")
        print(f"Shop ID: {shop_id}")
        print(f"Daily Limit: {daily_limit}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        self.update_state(state='PROGRESS', meta={
            'status': 'Starting daily alt text optimization...',
            'current': 0,
            'total': 0
        })
        
        # Get shop - handle both shop_id and user_settings_id
        shop = Shop.query.get(shop_id)
        
        if not shop:
            from app.models.access_control import UserSettings
            user_settings = UserSettings.query.get(shop_id)
            if user_settings and user_settings.shopify_shop_domain:
                shop = Shop.query.filter_by(shopify_shop_domain=user_settings.shopify_shop_domain).first()
                if not shop:
                    shop = Shop.query.filter_by(shop_domain=user_settings.shopify_shop_domain).first()
                
                if shop:
                    if shop.shopify_access_token != user_settings.shopify_access_token:
                        shop.shopify_access_token = user_settings.shopify_access_token
                        shop.anthropic_api_key = user_settings.anthropic_api_key
                        db.session.commit()
        
        if not shop:
            raise Exception(f"Shop not found for ID: {shop_id}")
        
        # Check for existing running alt text optimization to prevent duplicates
        existing_job = AutomationJob.query.filter_by(
            shop_id=shop.id,
            task_type='daily_alt_text_optimization',
            status='processing'
        ).first()
        
        if existing_job:
            print(f"Daily alt text optimization already running for shop {shop.id} (Job ID: {existing_job.task_id})")
            return {'success': False, 'message': 'Daily alt text optimization already running'}
        
        # Check if OpenAI is available - get from user settings
        from app.models.access_control import UserSettings
        user_settings = UserSettings.query.filter_by(shopify_shop_domain=shop.shopify_shop_domain).first()
        if not user_settings or not user_settings.openai_api_key:
            print("User OpenAI API key not configured - skipping alt text optimization")
            return {
                'success': True,
                'skipped': True,
                'message': 'OpenAI API key not configured for this store'
            }
        
        openai_api_key = user_settings.openai_api_key
        
        # Create master job record
        job = AutomationJob(
            shop_id=shop.id,
            task_type='daily_alt_text_optimization',
            status='processing',
            task_id=self.request.id
        )
        db.session.add(job)
        db.session.commit()
        
        # Initialize Shopify service
        shop_domain = shop.shopify_shop_domain or shop.shop_domain
        if not shop_domain or shop_domain == 'None' or shop_domain == 'Not configured':
            raise Exception(f"Invalid shop domain: {shop_domain}")
            
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        
        # Get products that need alt text optimization
        products_needing_alt_text = shopify_service.get_products_for_alt_text_optimization(limit=daily_limit)
        
        if not products_needing_alt_text:
            job.status = 'completed'
            job.result_data = json.dumps({
                'success': True,
                'skipped': True,
                'message': 'No products need alt text optimization',
                'products_checked': 0
            })
            db.session.commit()
            
            print("No products found that need alt text optimization")
            return {
                'success': True,
                'skipped': True,
                'message': 'No products need alt text optimization'
            }
        
        print(f"Found {len(products_needing_alt_text)} products needing alt text optimization")
        
        # Calculate image limit per product to stay within daily limit
        total_images_needed = sum(p['images_without_alt'] for p in products_needing_alt_text)
        images_per_product = max(1, min(5, daily_limit // len(products_needing_alt_text)))
        
        print(f"Processing up to {images_per_product} images per product")
        
        processed_products = 0
        successful_products = 0
        failed_products = 0
        total_images_updated = 0
        
        # Process each product
        for product in products_needing_alt_text:
            try:
                self.update_state(state='PROGRESS', meta={
                    'status': f'Processing {product["title"]}...',
                    'current': processed_products,
                    'total': len(products_needing_alt_text)
                })
                
                # Queue individual product optimization with countdown to spread load
                countdown_seconds = processed_products * 60  # 1 minute between products
                
                print(f"[PRODUCT {processed_products + 1}/{len(products_needing_alt_text)}] Queuing: {product['title']}")
                print(f"  - Product ID: {product['id']}")
                print(f"  - Images without alt text: {product['images_without_alt']}")
                print(f"  - Will start in: {countdown_seconds} seconds")
                
                result = optimize_product_image_alt_text_task.apply_async(
                    args=[shop.id, product['id'], images_per_product],
                    countdown=countdown_seconds
                )
                
                # For now, count as successful since we can't track subtasks
                successful_products += 1
                total_images_updated += min(product['images_without_alt'], images_per_product)
                    
                processed_products += 1
                
            except Exception as e:
                failed_products += 1
                processed_products += 1
                print(f"Error queuing alt text optimization for product {product['id']}: {str(e)}")
        
        # Update job with results
        job.status = 'completed'
        job.result_data = json.dumps({
            'success': True,
            'products_found': len(products_needing_alt_text),
            'products_queued': successful_products,
            'products_failed': failed_products,
            'estimated_images_to_update': total_images_updated,
            'images_per_product': images_per_product
        })
        db.session.commit()
        
        print(f"\n✅ Daily alt text optimization completed")
        print(f"   Products found: {len(products_needing_alt_text)}")
        print(f"   Products queued: {successful_products}")
        print(f"   Estimated images to update: {total_images_updated}")
        
        return {
            'success': True,
            'products_queued': successful_products,
            'estimated_images': total_images_updated
        }
        
    except Exception as e:
        if 'job' in locals():
            job.status = 'failed'
            job.error_message = str(e)
            db.session.commit()
        raise


@celery.task(bind=True, name='fetch_search_console_metrics_all')
def fetch_search_console_metrics_all_task(self):
    """Fetch Search Console metrics for all shops with Google OAuth connected"""
    try:
        print(f"\n{'='*60}")
        print(f"CELERY TASK RECEIVED: fetch_search_console_metrics_all")
        print(f"Task ID: {self.request.id}")
        print(f"Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
        from app.models import GoogleOAuth
        
        # Get all shops with Google OAuth connected
        oauth_connections = GoogleOAuth.query.all()
        
        results = []
        for oauth in oauth_connections:
            print(f"\nProcessing shop {oauth.shop_id}...")
            try:
                # Queue individual metrics fetch task for each shop
                result = fetch_search_console_metrics_task.apply_async(
                    args=[oauth.shop_id, False]
                )
                results.append({
                    'shop_id': oauth.shop_id,
                    'task_id': result.id,
                    'status': 'queued'
                })
            except Exception as e:
                print(f"Error queuing metrics fetch for shop {oauth.shop_id}: {e}")
                results.append({
                    'shop_id': oauth.shop_id,
                    'error': str(e),
                    'status': 'failed'
                })
        
        print(f"\n✅ Queued metrics fetch for {len(results)} shops")
        return {
            'success': True,
            'shops_processed': len(results),
            'results': results
        }
        
    except Exception as e:
        print(f"Error in fetch_search_console_metrics_all: {str(e)}")
        return {'success': False, 'error': str(e)}