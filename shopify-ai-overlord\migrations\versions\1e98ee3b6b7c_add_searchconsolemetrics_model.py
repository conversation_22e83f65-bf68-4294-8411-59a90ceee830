"""Add SearchConsoleMetrics model

Revision ID: 1e98ee3b6b7c
Revises: 6a11ece9d581
Create Date: 2025-05-31 15:42:02.902580

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1e98ee3b6b7c'
down_revision = '6a11ece9d581'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('search_console_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('url', sa.String(length=500), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('is_translation', sa.<PERSON>(), nullable=True),
    sa.Column('clicks', sa.Integer(), nullable=True),
    sa.Column('impressions', sa.Integer(), nullable=True),
    sa.Column('ctr', sa.Float(), nullable=True),
    sa.Column('position', sa.Float(), nullable=True),
    sa.Column('top_queries', sa.JSON(), nullable=True),
    sa.Column('metrics_date', sa.Date(), nullable=False),
    sa.Column('fetched_at', sa.DateTime(), nullable=True),
    sa.Column('previous_clicks', sa.Integer(), nullable=True),
    sa.Column('previous_impressions', sa.Integer(), nullable=True),
    sa.Column('clicks_change', sa.Integer(), nullable=True),
    sa.Column('impressions_change', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('shop_id', 'url', 'metrics_date', name='unique_shop_url_date')
    )
    with op.batch_alter_table('search_console_metrics', schema=None) as batch_op:
        batch_op.create_index('idx_shop_url_date', ['shop_id', 'url', 'metrics_date'], unique=False)

    with op.batch_alter_table('automation_jobs', schema=None) as batch_op:
        batch_op.drop_column('metadata')

    with op.batch_alter_table('translation_status', schema=None) as batch_op:
        batch_op.alter_column('translated_data',
               existing_type=sa.TEXT(),
               type_=sa.JSON(),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('translation_status', schema=None) as batch_op:
        batch_op.alter_column('translated_data',
               existing_type=sa.JSON(),
               type_=sa.TEXT(),
               existing_nullable=True)

    with op.batch_alter_table('automation_jobs', schema=None) as batch_op:
        batch_op.add_column(sa.Column('metadata', sa.TEXT(), nullable=True))

    with op.batch_alter_table('search_console_metrics', schema=None) as batch_op:
        batch_op.drop_index('idx_shop_url_date')

    op.drop_table('search_console_metrics')
    # ### end Alembic commands ###
