"""Add UserShop model for multi-store management

Revision ID: add_user_shop_model
Revises: add_translated_data_to_translation_status
Create Date: 2025-01-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'add_user_shop_model'
down_revision = 'add_translated_data'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_shops',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('access_key_id', sa.String(length=36), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('automation_hour', sa.Integer(), nullable=False),
    sa.Column('automation_minute', sa.Integer(), nullable=False),
    sa.Column('automation_enabled', sa.<PERSON>(), nullable=False),
    sa.Column('is_current', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['access_key_id'], ['access_keys.id'], ),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('access_key_id', 'shop_id', name='unique_user_shop')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_shops')
    # ### end Alembic commands ###