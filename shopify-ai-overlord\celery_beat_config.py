"""
Celery Beat Configuration - Only loaded when beat scheduler runs
"""
import os
from celery import Celery
from celerybeat_schedule import get_beat_schedule

def setup_beat_scheduler():
    """Setup Celery Beat scheduler with proper configuration"""
    # Use Upstash Redis as broker and backend
    broker_url = os.getenv('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
    backend_url = os.getenv('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
    
    beat_app = Celery(
        'shopify_ai_control_beat',
        broker=broker_url,
        backend=backend_url,
        include=['app.tasks']
    )
    
    # Configure beat scheduler
    beat_app.conf.update(
        timezone='UTC',
        enable_utc=True,
        beat_schedule=get_beat_schedule(),
        # Upstash Redis SSL configuration
        broker_connection_retry_on_startup=True,
        redis_socket_keepalive=True,
        redis_socket_keepalive_options={},
        redis_retry_on_timeout=True,
        redis_ssl_cert_reqs=None,
    )
    
    return beat_app

if __name__ == '__main__':
    app = setup_beat_scheduler()
    app.start()