from datetime import datetime
from app import db
from sqlalchemy import Index


class GoogleOAuth(db.Model):
    """Store Google OAuth tokens per shop"""
    __tablename__ = 'google_oauth'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.<PERSON>ey('shops.id'), nullable=False)
    
    # OAuth tokens
    access_token = db.Column(db.Text, nullable=False)
    refresh_token = db.Column(db.Text, nullable=True)
    token_type = db.Column(db.String(50), default='Bearer')
    expires_at = db.Column(db.DateTime, nullable=True)
    
    # Google account info
    google_email = db.Column(db.String(255), nullable=True)
    google_id = db.Column(db.String(255), nullable=True)
    
    # Selected Search Console property
    search_console_property = db.Column(db.String(255), nullable=True)
    
    # Scopes granted
    scopes = db.Column(db.JSON, default=list)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    shop = db.relationship('Shop', backref=db.backref('google_oauth', uselist=False))
    
    # Index for quick shop lookups
    __table_args__ = (
        Index('idx_shop_google', 'shop_id'),
    )
    
    def is_token_expired(self):
        """Check if the access token is expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() >= self.expires_at
    
    def __repr__(self):
        return f'<GoogleOAuth shop_id={self.shop_id} email={self.google_email}>'