{% extends "base_auth.html" %}

{% block title %}Automation Dashboard - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    .module-card {
        border-radius: 12px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .module-card:not(.disabled):hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }
    
    .module-card.disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }
    
    .module-card.disabled::after {
        content: 'COMING SOON';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-15deg);
        font-size: 1.5rem;
        font-weight: bold;
        color: rgba(0,0,0,0.2);
        background: rgba(255,255,255,0.9);
        padding: 10px 20px;
        border-radius: 8px;
        border: 2px solid rgba(0,0,0,0.1);
    }
    
    .module-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px 12px 0 0;
    }
    
    .module-header.seo {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    
    .module-header.ads {
        background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    }
    
    .module-header.email {
        background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    }
    
    .module-header.support {
        background: linear-gradient(135deg, #dc3545 0%, #e91e63 100%);
    }
    
    .module-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }
    
    .module-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: #333;
    }
    
    .stat-label {
        font-size: 0.85rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .module-actions {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }
    
    .activity-timeline {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .activity-item {
        display: flex;
        align-items: start;
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .activity-icon.seo {
        background-color: #d4edda;
        color: #28a745;
    }
    
    .activity-icon.ads {
        background-color: #fff3cd;
        color: #ffc107;
    }
    
    .activity-icon.email {
        background-color: #d1ecf1;
        color: #17a2b8;
    }
    
    .activity-icon.support {
        background-color: #f8d7da;
        color: #dc3545;
    }
    
    .automation-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }
    
    .automation-badge.active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .automation-badge.inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .coming-soon-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-1">AI Automation Dashboard</h1>
            <p class="text-muted mb-0">Manage all your AI-powered automation modules in one place</p>
        </div>
        <div>
            <a href="{{ url_for('auth.settings') }}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-2"></i>Global Settings
            </a>
        </div>
    </div>

    <!-- Automation Modules Grid -->
    <div class="row mb-4">
        <!-- SEO Module -->
        <div class="col-lg-6 mb-4">
            <div class="card module-card">
                <div class="module-header seo">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <i class="fas fa-search module-icon"></i>
                            <h3 class="mb-1">SEO Automation</h3>
                            <p class="mb-0 opacity-75">Optimize products, create collections, translate content</p>
                        </div>
                        <span class="automation-badge {% if seo_stats.enabled %}active{% else %}inactive{% endif %}">
                            {% if seo_stats.enabled %}Active{% else %}Inactive{% endif %}
                        </span>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ seo_stats.products_optimized }}</div>
                        <div class="stat-label">Products Optimized</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ seo_stats.collections_created }}</div>
                        <div class="stat-label">Collections Created</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ seo_stats.translations_completed }}</div>
                        <div class="stat-label">Translations</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ seo_stats.automations_active }}/4</div>
                        <div class="stat-label">Automations Active</div>
                    </div>
                </div>
                <div class="module-actions">
                    <div class="quick-actions">
                        <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                        <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                        <a href="{{ url_for('ai_modules.seo_collections') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-layer-group me-1"></i>Collections
                        </a>
                        {% if seo_stats.search_console_connected %}
                        <span class="text-success ms-auto">
                            <i class="fas fa-check-circle me-1"></i>Search Console Connected
                        </span>
                        {% else %}
                        <a href="{{ url_for('ai_modules.seo_search_console') }}" class="btn btn-outline-warning btn-sm ms-auto">
                            <i class="fas fa-chart-line me-1"></i>Connect Search Console
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Ads Module -->
        <div class="col-lg-6 mb-4">
            <div class="card module-card disabled">
                <div class="module-header ads">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <i class="fas fa-ad module-icon"></i>
                            <h3 class="mb-1">Ads Automation</h3>
                            <p class="mb-0 opacity-75">Manage Google, Facebook, and TikTok campaigns</p>
                        </div>
                        <span class="automation-badge inactive">Inactive</span>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Active Campaigns</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">$0</div>
                        <div class="stat-label">Total Spend</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Conversions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0/4</div>
                        <div class="stat-label">Automations Active</div>
                    </div>
                </div>
                <div class="module-actions">
                    <div class="quick-actions">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fab fa-google me-1"></i>Google Ads
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fab fa-facebook me-1"></i>Facebook
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fab fa-tiktok me-1"></i>TikTok
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Module -->
        <div class="col-lg-6 mb-4">
            <div class="card module-card disabled">
                <div class="module-header email">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <i class="fas fa-envelope module-icon"></i>
                            <h3 class="mb-1">Email Marketing</h3>
                            <p class="mb-0 opacity-75">Automated campaigns and customer segmentation</p>
                        </div>
                        <span class="automation-badge inactive">Inactive</span>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Campaigns Sent</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Subscribers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">Open Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0/5</div>
                        <div class="stat-label">Automations Active</div>
                    </div>
                </div>
                <div class="module-actions">
                    <div class="quick-actions">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-paper-plane me-1"></i>Campaigns
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-robot me-1"></i>Automation
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-users me-1"></i>Segments
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Support Module -->
        <div class="col-lg-6 mb-4">
            <div class="card module-card disabled">
                <div class="module-header support">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <i class="fas fa-headset module-icon"></i>
                            <h3 class="mb-1">Customer Support</h3>
                            <p class="mb-0 opacity-75">AI chatbot and automated ticket management</p>
                        </div>
                        <span class="automation-badge inactive">Inactive</span>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Tickets Resolved</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0m</div>
                        <div class="stat-label">Avg Response Time</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">Satisfaction Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0/3</div>
                        <div class="stat-label">Automations Active</div>
                    </div>
                </div>
                <div class="module-actions">
                    <div class="quick-actions">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-robot me-1"></i>Chatbot
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-ticket-alt me-1"></i>Tickets
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-question-circle me-1"></i>FAQs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h4>
                </div>
                <div class="card-body p-0">
                    <div class="activity-timeline">
                        {% if recent_activity %}
                            {% for activity in recent_activity %}
                            <div class="activity-item">
                                <div class="activity-icon {{ activity.type }}">
                                    <i class="fas fa-{{ activity.icon }}"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong>{{ activity.description }}</strong>
                                            <div class="text-muted small">
                                                {% if activity.status == 'completed' %}
                                                    <i class="fas fa-check-circle text-success me-1"></i>Completed
                                                {% elif activity.status == 'processing' %}
                                                    <i class="fas fa-spinner fa-spin text-primary me-1"></i>Processing
                                                {% elif activity.status == 'failed' %}
                                                    <i class="fas fa-times-circle text-danger me-1"></i>Failed
                                                {% else %}
                                                    <i class="fas fa-clock text-warning me-1"></i>Pending
                                                {% endif %}
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ activity.timestamp.strftime('%b %d, %I:%M %p') }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5 text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>No recent activity to display</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh activity every 30 seconds
    setInterval(function() {
        // Could implement AJAX refresh here if needed
    }, 30000);
});
</script>
{% endblock %}