from flask import Blueprint, request, redirect, render_template, session, flash, url_for, current_app
from flask_login import login_user, current_user
import hmac
import hashlib
import base64
import requests
import json
from urllib.parse import urlencode
from app import db
from app.models.shop import Shop
from app.models.access_control import UserShop, AccessKey, UserSettings
from app.services.shopify_service import ShopifyService

bp = Blueprint('shopify', __name__)

@bp.route('/connect', methods=['GET', 'POST'])
def connect():
    """Connect using custom app credentials"""
    # Check if user is logged in via access key
    if 'access_key_id' not in session:
        flash('Please log in first', 'error')
        return redirect(url_for('auth.login'))
    
    if request.method == 'GET':
        return render_template('shopify/connect.html')
    
    # Get form data
    shop_domain = request.form.get('shop_domain')
    access_token = request.form.get('access_token')
    anthropic_api_key = request.form.get('anthropic_api_key')
    store_actual_live_url = request.form.get('store_actual_live_url', '').strip()
    
    if not shop_domain or not access_token or not anthropic_api_key:
        flash('Shop domain, access token, and Anthropic API key are required', 'error')
        return render_template('shopify/connect.html')
    
    # Ensure shop domain is properly formatted
    if not shop_domain.endswith('.myshopify.com'):
        shop_domain = f"{shop_domain}.myshopify.com"
    
    try:
        # Test the credentials
        shopify_service = ShopifyService(shop_domain, access_token)
        shop_data = shopify_service.get_shop_details()
        
        # Save or update shop in database
        existing_shop = Shop.query.filter_by(shop_domain=shop_domain).first()
        if existing_shop:
            existing_shop.access_token = access_token
            existing_shop.shopify_access_token = access_token
            existing_shop.shopify_shop_domain = shop_domain
            existing_shop.anthropic_api_key = anthropic_api_key
            existing_shop.shop_name = shop_data.get('name')
            existing_shop.email = shop_data.get('email')
            existing_shop.owner = shop_data.get('shop_owner')
        else:
            new_shop = Shop(
                shop_domain=shop_domain,
                access_token=access_token,
                shopify_access_token=access_token,
                shopify_shop_domain=shop_domain,
                anthropic_api_key=anthropic_api_key,
                shop_name=shop_data.get('name'),
                email=shop_data.get('email'),
                owner=shop_data.get('shop_owner')
            )
            db.session.add(new_shop)
            existing_shop = new_shop
        
        db.session.commit()
        
        # Update UserSettings with store information
        access_key_id = session['access_key_id']
        access_key = AccessKey.query.get(access_key_id)
        if access_key:
            user_settings = access_key.user_settings
            if not user_settings:
                user_settings = UserSettings(access_key_id=access_key.id)
                db.session.add(user_settings)
            
            # Update settings with shop information
            user_settings.shopify_shop_domain = shop_domain
            user_settings.shopify_access_token = access_token
            user_settings.anthropic_api_key = anthropic_api_key
            if store_actual_live_url:
                user_settings.store_actual_live_url = store_actual_live_url
            
            db.session.commit()
        
        # Create UserShop relationship
        existing_user_shop = UserShop.query.filter_by(
            access_key_id=access_key_id,
            shop_id=existing_shop.id
        ).first()
        
        if not existing_user_shop:
            # Calculate a unique automation hour for this store
            existing_count = UserShop.query.count()
            automation_hour = (2 + existing_count) % 24  # Start at 2 AM, increment by 1 hour per store
            
            user_shop = UserShop(
                access_key_id=access_key_id,
                shop_id=existing_shop.id,
                automation_hour=automation_hour,
                automation_minute=0,
                is_current=True  # Make this the current store
            )
            
            # Clear other current flags for this user
            UserShop.query.filter_by(access_key_id=access_key_id).update(
                {'is_current': False}
            )
            
            db.session.add(user_shop)
            db.session.commit()
        else:
            # Make this the current store
            UserShop.set_current_shop(access_key_id, existing_shop.id)
            db.session.commit()
        
        flash('Successfully connected to Shopify!', 'success')
        return redirect(url_for('ai_modules.seo_products'))
        
    except Exception as e:
        flash(f'Error connecting to Shopify: {str(e)}', 'error')
        return render_template('shopify/connect.html')

# OAuth callback removed - using custom app authentication instead

@bp.route('/disconnect')
def disconnect():
    """Disconnect current shop from app"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    access_key_id = session['access_key_id']
    current_shop = UserShop.get_current_shop(access_key_id)
    
    if current_shop:
        # Here you could implement webhook cleanup, etc.
        db.session.delete(current_shop)
        db.session.commit()
        flash('Shop disconnected', 'info')
    
    return redirect(url_for('ai_modules.seo_products'))

# OAuth request-scopes removed - custom apps have all permissions granted when created