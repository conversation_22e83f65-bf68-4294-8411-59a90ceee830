from celery import Celery
from config import Config
import os

def make_celery(app_name=__name__):
    """Create and configure Celery app"""
    # Use Upstash Redis as broker and backend
    broker_url = os.getenv('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
    backend_url = os.getenv('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
    
    celery = Celery(
        app_name,
        broker=broker_url,
        backend=backend_url,
        include=['app.tasks']  # Include our tasks module
    )
    
    # Update task settings
    celery.conf.update(
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        result_expires=3600,  # Results expire after 1 hour
        task_track_started=True,
        task_time_limit=30 * 60,  # 30 minutes max per task
        task_soft_time_limit=25 * 60,  # Soft limit at 25 minutes
        worker_prefetch_multiplier=1,  # Prevent worker from prefetching too many tasks
        worker_max_tasks_per_child=50,  # Restart worker after 50 tasks to prevent memory leaks
        # Upstash Redis SSL configuration
        broker_connection_retry_on_startup=True,
        redis_socket_keepalive=True,
        redis_socket_keepalive_options={},
        redis_retry_on_timeout=True,
        redis_ssl_cert_reqs=None,  # Disable SSL certificate verification for Upstash
    )
    
    # Configure Flask app context for tasks
    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context"""
        def __call__(self, *args, **kwargs):
            from app import create_app
            app = create_app()
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    
    return celery

# Create celery instance
celery_app = make_celery('shopify_ai_control')