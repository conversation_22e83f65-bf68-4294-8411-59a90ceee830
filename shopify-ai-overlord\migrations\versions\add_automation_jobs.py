"""Add automation jobs and settings

Revision ID: add_automation_jobs
Revises: c0ba407f5fad
Create Date: 2025-01-29

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_automation_jobs'
down_revision = 'c0ba407f5fad'
branch_labels = None
depends_on = None

def upgrade():
    # Create automation_jobs table
    op.create_table('automation_jobs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('shop_id', sa.Integer(), nullable=False),
        sa.Column('task_type', sa.String(50), nullable=False),
        sa.Column('task_id', sa.String(255), nullable=True),
        sa.Column('resource_id', sa.String(255), nullable=True),
        sa.Column('status', sa.String(20), nullable=True, default='pending'),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('result_data', sa.Text(), nullable=True),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add automation_settings to shops table
    with op.batch_alter_table('shops', schema=None) as batch_op:
        batch_op.add_column(sa.Column('automation_settings', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('shopify_shop_domain', sa.String(255), nullable=True))
        batch_op.add_column(sa.Column('shopify_access_token', sa.String(255), nullable=True))
        batch_op.add_column(sa.Column('anthropic_api_key', sa.String(255), nullable=True))

def downgrade():
    # Drop automation_jobs table
    op.drop_table('automation_jobs')
    
    # Remove columns from shops table
    with op.batch_alter_table('shops', schema=None) as batch_op:
        batch_op.drop_column('automation_settings')
        batch_op.drop_column('shopify_shop_domain')
        batch_op.drop_column('shopify_access_token')
        batch_op.drop_column('anthropic_api_key')