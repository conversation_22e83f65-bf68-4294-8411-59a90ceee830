{"permissions": {"allow": ["mcp__digitalocean__get_app", "mcp__digitalocean__update_app", "Bash(git add:*)", "WebFetch(domain:supabase.com)", "mcp__digitalocean__list_apps", "Bash(grep:*)", "Bash(rg:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(git push:*)", "mcp__digitalocean__create_app", "WebFetch(domain:platform.openai.com)", "Bash(python test_openai_service.py:*)", "<PERSON><PERSON>(python3:*)", "mcp__shopify-dev-mcp__get_started", "mcp__shopify-dev-mcp__search_dev_docs", "mcp__shopify-dev-mcp__introspect_admin_schema", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(ls:*)", "mcp__supabase__list_organizations", "mcp__supabase__get_cost", "mcp__supabase__confirm_cost", "mcp__supabase__create_project", "mcp__supabase__get_project", "mcp__supabase__apply_migration", "mcp__supabase__execute_sql", "mcp__supabase__get_project_url", "mcp__digitalocean__get_deployment", "Bash(git pull:*)", "Bash(git merge:*)", "mcp__digitalocean__list_deployments", "mcp__digitalocean__create_deployment", "Bash(find:*)", "Bash(sqlite3:*)", "Bash(git reset:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(redis-cli:*)"], "deny": []}}