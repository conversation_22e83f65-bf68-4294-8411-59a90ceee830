from datetime import datetime
from app import db
from flask_login import UserMixin
import json

class Shop(UserMixin, db.Model):
    __tablename__ = 'shops'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_domain = db.Column(db.String(255), unique=True, nullable=False)
    access_token = db.Column(db.String(255), nullable=False)
    shop_name = db.Column(db.String(255))
    email = db.Column(db.String(255))
    owner = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # AI module configurations
    seo_enabled = db.Column(db.Boolean, default=False)
    ads_enabled = db.Column(db.Bo<PERSON>an, default=False)
    email_marketing_enabled = db.Column(db.<PERSON>, default=False)
    customer_support_enabled = db.Column(db.Boolean, default=False)
    
    # Automation settings (JSON)
    automation_settings = db.Column(db.Text)
    
    # API Keys
    shopify_shop_domain = db.Column(db.String(255))
    shopify_access_token = db.Column(db.String(255))
    anthropic_api_key = db.Column(db.String(255))
    
    # Relationships
    ai_configs = db.relationship('AIConfig', backref='shop', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_id(self):
        return str(self.id)
    
    def get_automation_settings(self):
        """Get automation settings as dict"""
        if self.automation_settings:
            return json.loads(self.automation_settings)
        return {
            'auto_optimize_products': False,
            'auto_create_collections': False,
            'auto_translate': False,
            'daily_optimization_limit': 10,
            'target_languages': [],
            'optimization_keywords': []
        }
    
    def set_automation_settings(self, settings):
        """Set automation settings from dict"""
        self.automation_settings = json.dumps(settings)