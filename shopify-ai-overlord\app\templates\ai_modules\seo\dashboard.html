{% extends "base_auth.html" %}

{% block title %}SEO Dashboard - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    .stats-card {
        transition: transform 0.2s;
        cursor: pointer;
        border-radius: 10px;
        overflow: hidden;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    }
    .stats-card .card-body {
        padding: 1.5rem;
    }
    .progress-circle {
        position: relative;
        display: inline-block;
        width: 120px;
        height: 120px;
    }
    .progress-circle canvas {
        transform: rotate(-90deg);
    }
    .progress-circle .percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        font-weight: bold;
    }
    .automation-card {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s;
    }
    .automation-card:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .automation-card.active {
        border-color: #28a745;
        background-color: #f0fff4;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-active {
        background-color: #28a745;
        animation: pulse 2s infinite;
    }
    .status-inactive {
        background-color: #6c757d;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    .job-row {
        transition: background-color 0.2s;
    }
    .job-row:hover {
        background-color: #f8f9fa;
    }
    .task-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        font-size: 18px;
    }
    .task-icon.products { background-color: #e3f2fd; color: #1976d2; }
    .task-icon.collections { background-color: #e8f5e9; color: #388e3c; }
    .task-icon.translations { background-color: #fff3e0; color: #f57c00; }
    .quick-action-btn {
        padding: 15px 20px;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s;
        border: 2px solid transparent;
    }
    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }
    .metric-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .metric-card h3 {
        font-size: 36px;
        margin: 10px 0;
    }
    .stats-card .card-body * {
        color: white !important;
    }
    .stats-card h2 {
        color: white !important;
        font-weight: bold;
    }
    .stats-card h6 {
        color: rgba(255, 255, 255, 0.9) !important;
    }
    .stats-card small {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    .progress-circle .percentage {
        color: white !important;
        font-weight: bold;
    }
    .text-white-50 {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
    }
    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-2">SEO Automation Dashboard</h1>
            <p class="text-muted mb-0">One-stop shop to automate and grow your Shopify store's SEO</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#automationSettingsModal">
                <i class="fas fa-cog me-2"></i>Settings
            </button>
            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#resetAllModal">
                <i class="fas fa-trash-alt me-2"></i>Reset All
            </button>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white !important;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-2 text-white" style="color: white !important; opacity: 1 !important;">Products Optimized</h6>
                            <h2 class="mb-0 text-white" style="color: white !important; font-weight: bold;">{{ stats.products_optimized }}</h2>
                            <div class="progress mt-2" style="height: 6px; background: rgba(255,255,255,0.2);">
                                <div class="progress-bar bg-white" style="width: {{ stats.products_percentage }}%"></div>
                            </div>
                            <small class="d-block mt-2 text-white" style="color: white !important; opacity: 0.9;">{{ stats.total_products - stats.products_optimized }} remaining</small>
                        </div>
                        <div class="progress-circle">
                            <canvas id="productsProgress" width="80" height="80"></canvas>
                            <div class="percentage" style="color: white !important;">{{ stats.products_percentage }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); color: white !important;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-2 text-white" style="color: white !important; opacity: 1 !important;">Collections Created</h6>
                            <h2 class="mb-0 text-white" style="color: white !important; font-weight: bold;">{{ stats.collections_created }}</h2>
                            <small class="d-block mt-2 text-white" style="color: white !important; opacity: 0.9;">From AI-generated tags</small>
                        </div>
                        <div style="color: white !important; opacity: 0.5;">
                            <i class="fas fa-layer-group fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0" style="background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%); color: white !important;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-2 text-white" style="color: white !important; opacity: 1 !important;">Translations</h6>
                            <h2 class="mb-0 text-white" style="color: white !important; font-weight: bold;">{{ stats.translations_completed }}</h2>
                            <small class="d-block mt-2 text-white" style="color: white !important; opacity: 0.9;">{{ stats.languages_count }} languages</small>
                        </div>
                        <div style="color: white !important; opacity: 0.5;">
                            <i class="fas fa-globe fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white !important;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-2 text-white" style="color: white !important; opacity: 1 !important;">Active Tasks</h6>
                            <h2 class="mb-0 text-white" style="color: white !important; font-weight: bold;">{{ stats.tasks_remaining }}</h2>
                            <small class="d-block mt-2 text-white" style="color: white !important; opacity: 0.9;">{{ stats.tasks_today }} today</small>
                        </div>
                        <div style="color: white !important; opacity: 0.5;">
                            <i class="fas fa-tasks fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Quota Section -->
    {% if quota_info %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Today's Quota & Progress</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Products Quota -->
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Product Optimization</h6>
                            <div class="d-flex justify-content-between align-items-end mb-2">
                                <div>
                                    <span class="h4 mb-0">{{ quota_info.usage.products.optimized }}</span>
                                    <span class="text-muted">/ {{ quota_info.effective_limits.products }} products</span>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ quota_info.remaining.products }} remaining</small>
                                </div>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar bg-primary" role="progressbar" 
                                     style="width: {{ quota_info.progress.products_percentage }}%"
                                     aria-valuenow="{{ quota_info.progress.products_percentage }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            {% if quota_info.deficit.products > 0 %}
                            <small class="text-warning mt-1 d-block">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Includes {{ quota_info.deficit.products }} from yesterday's failures
                            </small>
                            {% endif %}
                        </div>
                        
                        <!-- Collections Quota -->
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">Collection Creation</h6>
                            <div class="d-flex justify-content-between align-items-end mb-2">
                                <div>
                                    <span class="h4 mb-0">{{ quota_info.usage.collections.created }}</span>
                                    <span class="text-muted">/ {{ quota_info.effective_limits.collections }} collections</span>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ quota_info.remaining.collections }} remaining</small>
                                </div>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ quota_info.progress.collections_percentage }}%"
                                     aria-valuenow="{{ quota_info.progress.collections_percentage }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            {% if quota_info.deficit.collections > 0 %}
                            <small class="text-warning mt-1 d-block">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Includes {{ quota_info.deficit.collections }} from yesterday's failures
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- ETA Row -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Estimated Completion:</strong> 
                                {{ quota_info.eta_days }} days remaining • 
                                Expected to complete by {{ quota_info.completion_date }}
                                <small class="d-block mt-1">
                                    Calculated dynamically: {{ stats.total_products }} products ÷ {{ quota_info.base_limits.products }} per day = {{ quota_info.eta_days }} days
                                </small>
                                <small class="d-block text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Daily quotas are automatically calculated based on your store's {{ stats.total_products }} products to complete in ~90 days
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Automation Status -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Automation Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <!-- Product SEO Status -->
                        <div class="automation-card {{ 'active' if settings.seo_enabled else '' }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <span class="status-indicator {{ 'status-active' if settings.seo_enabled else 'status-inactive' }}"></span>
                                    Product SEO
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="seoToggle" 
                                           {{ 'checked' if settings.seo_enabled else '' }}
                                           onchange="toggleAutomation('seo', this.checked)">
                                </div>
                            </div>
                            <p class="text-muted small mb-0">
                                {% if settings.seo_enabled %}
                                <i class="fas fa-check-circle text-success me-1"></i>Running daily at {{ settings.seo_schedule }}
                                {% else %}
                                <i class="fas fa-pause-circle text-muted me-1"></i>Paused - Enable to start
                                {% endif %}
                            </p>
                        </div>

                        <!-- Collections Status -->
                        <div class="automation-card {{ 'active' if settings.collection_enabled else '' }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <span class="status-indicator {{ 'status-active' if settings.collection_enabled else 'status-inactive' }}"></span>
                                    Smart Collections
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="collectionToggle" 
                                           {{ 'checked' if settings.collection_enabled else '' }}
                                           onchange="toggleAutomation('collection', this.checked)">
                                </div>
                            </div>
                            <p class="text-muted small mb-0">
                                {% if settings.collection_enabled %}
                                <i class="fas fa-check-circle text-success me-1"></i>Creating from new tags daily
                                {% else %}
                                <i class="fas fa-pause-circle text-muted me-1"></i>Paused - Enable to start
                                {% endif %}
                            </p>
                        </div>

                        <!-- Translation Status -->
                        <div class="automation-card {{ 'active' if settings.translation_enabled else '' }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <span class="status-indicator {{ 'status-active' if settings.translation_enabled else 'status-inactive' }}"></span>
                                    Translations
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="translationToggle" 
                                           {{ 'checked' if settings.translation_enabled else '' }}
                                           onchange="toggleAutomation('translation', this.checked)">
                                </div>
                            </div>
                            <p class="text-muted small mb-0">
                                {% if settings.translation_enabled %}
                                <i class="fas fa-check-circle text-success me-1"></i>Auto-translating to {{ settings.target_languages|length }} languages
                                {% else %}
                                <i class="fas fa-pause-circle text-muted me-1"></i>Paused - Configure to start
                                {% endif %}
                            </p>
                            {% if settings.translation_enabled %}
                            <button class="btn btn-sm btn-outline-info w-100 mt-2" onclick="showTranslationModal()">
                                <i class="fas fa-cog me-1"></i>Configure Languages
                            </button>
                            {% endif %}
                        </div>

                        <!-- Alt Text Optimization Status -->
                        <div class="automation-card {{ 'active' if settings.alt_text_enabled else '' }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <span class="status-indicator {{ 'status-active' if settings.alt_text_enabled else 'status-inactive' }}"></span>
                                    Alt Text Optimization
                                    {% if not openai_available %}
                                        <small class="badge badge-warning ms-1">OpenAI Required</small>
                                    {% endif %}
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="altTextToggle" 
                                           {{ 'checked' if settings.alt_text_enabled else '' }}
                                           {{ 'disabled' if not openai_available else '' }}
                                           onchange="toggleAutomation('alt_text', this.checked)">
                                </div>
                            </div>
                            <p class="text-muted small mb-0">
                                {% if not openai_available %}
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>OpenAI API key required
                                {% elif settings.alt_text_enabled %}
                                <i class="fas fa-check-circle text-success me-1"></i>Processing 25 images daily
                                {% else %}
                                <i class="fas fa-pause-circle text-muted me-1"></i>Paused - Enable to start
                                {% endif %}
                            </p>
                        </div>

                        <!-- Search Console Status -->
                        <div class="automation-card {{ 'active' if settings.search_console_enabled else '' }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <span class="status-indicator {{ 'status-active' if settings.search_console_enabled else 'status-inactive' }}"></span>
                                    Search Console
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="searchConsoleToggle" 
                                           {{ 'checked' if settings.search_console_enabled else '' }}
                                           onchange="toggleAutomation('search_console', this.checked)">
                                </div>
                            </div>
                            <p class="text-muted small mb-0">
                                {% if settings.search_console_enabled %}
                                <i class="fas fa-check-circle text-success me-1"></i>Auto-submitting sitemaps daily
                                {% else %}
                                <i class="fas fa-pause-circle text-muted me-1"></i>Paused - Enable to start
                                {% endif %}
                            </p>
                            {% if google_oauth %}
                            <small class="text-success d-block mt-1">
                                <i class="fab fa-google me-1"></i>Connected
                            </small>
                            {% else %}
                            <a href="{{ url_for('ai_modules.seo_search_console') }}" class="btn btn-sm btn-outline-warning w-100 mt-2">
                                <i class="fab fa-google me-1"></i>Connect Google
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <hr class="my-4">
                    
                    <!-- Trigger Manual Run -->
                    <div class="d-grid gap-2 mb-3">
                        <button class="btn btn-primary" onclick="triggerDailyAutomation()">
                            <i class="fas fa-play me-2"></i>Run Daily Automation Now
                        </button>
                        <button class="btn btn-success" onclick="checkAndCreateCollections()">
                            <i class="fas fa-layer-group me-2"></i>Check & Create Collections
                        </button>
                        <small class="text-muted text-center">Manually trigger today's automation tasks</small>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-outline-primary">
                            <i class="fas fa-box me-2"></i>Product SEO Status
                        </a>
                        <a href="{{ url_for('ai_modules.seo_collections') }}" class="btn btn-outline-success">
                            <i class="fas fa-layer-group me-2"></i>Collection Status
                        </a>
                        <a href="{{ url_for('ai_modules.seo_languages') }}" class="btn btn-outline-info">
                            <i class="fas fa-language me-2"></i>Translation Status
                        </a>
                        <a href="{{ url_for('ai_modules.seo_technical') }}" class="btn btn-outline-warning">
                            <i class="fas fa-cogs me-2"></i>Technical SEO
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent SEO Activity</h5>
                        <a href="{{ url_for('ai_modules.changes') }}" class="btn btn-sm btn-outline-primary">
                            View All History
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50px;"></th>
                                    <th>Task</th>
                                    <th>Status</th>
                                    <th>Results</th>
                                    <th>Duration</th>
                                    <th>Started</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in recent_jobs %}
                                <tr class="job-row">
                                    <td>
                                        <div class="task-icon {{ 'products' if 'product' in job.task_type else 'collections' if 'collection' in job.task_type else 'translations' }}">
                                            <i class="fas fa-{{ job.icon }}"></i>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ job.task_type|title|replace('_', ' ') }}</strong>
                                    </td>
                                    <td>
                                        {% if job.status == 'completed' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Completed
                                        </span>
                                        {% elif job.status == 'processing' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-spinner fa-spin me-1"></i>Processing
                                        </span>
                                        {% elif job.status == 'failed' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Failed
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ job.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ job.result_summary }}</small>
                                    </td>
                                    <td>
                                        <small>{{ job.duration }}</small>
                                    </td>
                                    <td>
                                        <small>{{ job.created_at|timeago }}</small>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showJobDetails({{ job.id }}, '{{ job.task_type }}')">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                                        No automation jobs yet. Start optimizing your store!
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Activity pagination" class="mt-3">
                        <ul class="pagination justify-content-center" id="activityPagination">
                            <!-- Pagination will be populated by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Performance Metrics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>SEO Performance Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <i class="fas fa-search fa-2x mb-3"></i>
                                <h6>SEO Score</h6>
                                <h3>{{ ((stats.products_optimized / stats.total_products * 100) if stats.total_products > 0 else 0)|int }}%</h3>
                                <small>Overall optimization</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <i class="fas fa-tags fa-2x mb-3"></i>
                                <h6>AI Tags Generated</h6>
                                <h3>{{ stats.collections_created * 5 }}</h3>
                                <small>Improving discoverability</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <i class="fas fa-language fa-2x mb-3"></i>
                                <h6>Market Reach</h6>
                                <h3>{{ stats.languages_count + 1 }}</h3>
                                <small>Languages available</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <i class="fas fa-rocket fa-2x mb-3"></i>
                                <h6>Automation Rate</h6>
                                <h3>{{ stats.tasks_today * 10 }}%</h3>
                                <small>Daily efficiency</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="jobDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Automation Settings Modal -->
<div class="modal fade" id="automationSettingsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SEO Automation Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="automationSettingsForm">
                    <!-- Quota Configuration (Environment Variables Info) -->
                    {% if quota_info %}
                    <div class="alert alert-info mb-4">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Daily Quota Configuration</h6>
                        <p class="mb-2">Your daily quotas are automatically calculated based on your store size ({{ stats.total_products }} products):</p>
                        <ul class="mb-2">
                            <li><strong>Products per day:</strong> {{ quota_info.base_limits.products }}</li>
                            <li><strong>Collections per day:</strong> {{ quota_info.base_limits.collections }}</li>
                            <li><strong>Estimated completion:</strong> {{ quota_info.eta_days }} days</li>
                        </ul>
                        <small>These limits are configured via environment variables and ensure optimal performance.</small>
                    </div>
                    {% endif %}
                    
                    <!-- Automation Toggles -->
                    <h6 class="mb-3">Automation Features</h6>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="seoEnabledSettings" name="seo_enabled"
                                       {{ 'checked' if settings.seo_enabled else '' }}>
                                <label class="form-check-label" for="seoEnabledSettings">
                                    <strong>Product SEO</strong>
                                    <small class="d-block text-muted">Automatically optimize product titles, descriptions, and tags</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="collectionEnabledSettings" name="collection_enabled"
                                       {{ 'checked' if settings.collection_enabled else '' }}>
                                <label class="form-check-label" for="collectionEnabledSettings">
                                    <strong>Smart Collections</strong>
                                    <small class="d-block text-muted">Create collections from AI-generated tags</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="translationEnabledSettings" name="translation_enabled"
                                       {{ 'checked' if settings.translation_enabled else '' }}>
                                <label class="form-check-label" for="translationEnabledSettings">
                                    <strong>Auto Translation</strong>
                                    <small class="d-block text-muted">Translate content after optimization</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="altTextEnabledSettings" name="alt_text_enabled"
                                       {{ 'checked' if settings.alt_text_enabled else '' }}
                                       {% if not openai_available %}disabled{% endif %}>
                                <label class="form-check-label" for="altTextEnabledSettings">
                                    <strong>Alt Text Optimization</strong>
                                    <small class="d-block text-muted">AI-powered alt text for all images</small>
                                    {% if not openai_available %}
                                    <small class="d-block text-danger">
                                        <i class="fas fa-exclamation-circle me-1"></i>Requires OpenAI API key
                                    </small>
                                    {% endif %}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="searchConsoleEnabledSettings" name="search_console_enabled"
                                       {{ 'checked' if settings.search_console_enabled else '' }}
                                       {% if not google_oauth %}disabled{% endif %}>
                                <label class="form-check-label" for="searchConsoleEnabledSettings">
                                    <strong>Search Console</strong>
                                    <small class="d-block text-muted">Submit sitemaps and monitor performance</small>
                                    {% if not google_oauth %}
                                    <small class="d-block text-danger">
                                        <i class="fas fa-exclamation-circle me-1"></i>Requires Google OAuth connection
                                    </small>
                                    {% endif %}
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mb-4">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Search Console Setup Instructions</h6>
                        {% if not google_oauth %}
                        <p class="mb-2"><strong>You haven't connected Google Search Console yet.</strong> To enable this automation:</p>
                        {% else %}
                        <p class="mb-2"><strong>Google Search Console is connected!</strong> When enabled, this automation will:</p>
                        {% endif %}
                        
                        {% if not google_oauth %}
                        <ol class="mb-2">
                            <li>Go to the <a href="{{ url_for('ai_modules.seo_search_console') }}" target="_blank">Search Console page</a></li>
                            <li>Click "Connect to Google" button</li>
                            <li>Authorize access to your Google Search Console account</li>
                            <li>Return here to enable the automation</li>
                        </ol>
                        <div class="mt-3">
                            <a href="{{ url_for('ai_modules.seo_search_console') }}" class="btn btn-warning btn-sm">
                                <i class="fab fa-google me-2"></i>Connect Google Account Now
                            </a>
                        </div>
                        {% else %}
                        <ul class="mb-0">
                            <li>Automatically submit sitemaps when new content is created</li>
                            <li>Track performance metrics (clicks, impressions, CTR, position)</li>
                            <li>Monitor all optimized URLs including translations</li>
                            <li>Update metrics daily at 3 AM GMT</li>
                        </ul>
                        <div class="mt-3">
                            <a href="{{ url_for('ai_modules.seo_search_console') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-chart-line me-2"></i>View Search Console Data
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- Debug button -->
                    <button type="button" class="btn btn-warning btn-sm mb-3" onclick="debugCheckboxes()">Debug Checkboxes</button>
                    
                    <h6 class="mb-3">API Configuration</h6>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">OpenAI API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="openai_api_key" 
                                       value="{{ user_settings.openai_api_key or '' }}" placeholder="sk-proj-...">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility('openai_api_key')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="form-text">Required for alt text optimization (GPT-4o-mini)</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Anthropic API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="anthropic_api_key" 
                                       value="{{ user_settings.anthropic_api_key or '' }}" placeholder="sk-ant-...">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility('anthropic_api_key')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="form-text">Required for Claude-powered content generation</small>
                        </div>
                    </div>
                    
                    <h6 class="mb-3">SEO Optimization Settings</h6>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">Daily Product Limit Override</label>
                            <input type="number" class="form-control" name="daily_optimization_limit" 
                                   value="{{ settings.daily_optimization_limit }}" min="1" max="{{ quota_info.base_limits.products if quota_info else 100 }}">
                            <small class="form-text">Override calculated limit (max: {{ quota_info.base_limits.products if quota_info else 100 }})</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Schedule Time</label>
                            <input type="time" class="form-control" name="seo_schedule" 
                                   value="{{ settings.seo_schedule|default('02:00') }}">
                            <small class="form-text">When to run daily optimization</small>
                        </div>
                    </div>
                    
                    <h6 class="mb-3">Target Languages</h6>
                    <div class="mb-4">
                        <select class="form-select" name="target_languages" multiple style="height: 120px;">
                            {% for locale in available_languages %}
                            <option value="{{ locale.locale }}" 
                                    {{ 'selected' if locale.locale in settings.target_languages else '' }}>
                                {{ locale.name }} ({{ locale.locale }})
                            </option>
                            {% endfor %}
                        </select>
                        <small class="form-text">Hold Ctrl/Cmd to select multiple languages</small>
                    </div>
                    
                    <h6 class="mb-3">SEO Keywords</h6>
                    <div class="mb-4">
                        <textarea class="form-control" name="optimization_keywords" rows="3"
                                  placeholder="Enter keywords separated by commas">{{ settings.optimization_keywords|join(', ') }}</textarea>
                        <small class="form-text">Global keywords to focus on during optimization</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAutomationSettings()">Save Settings</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset All Modal -->
<div class="modal fade" id="resetAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>Reset All SEO Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Warning!</strong> This action will:
                </div>
                <ul class="text-danger">
                    <li>Cancel all pending optimization tasks</li>
                    <li>Delete all collections created by AI from Shopify</li>
                    <li>Revert all product optimizations in Shopify</li>
                    <li>Clear all optimization records and history</li>
                    <li>Reset all tracking data</li>
                </ul>
                <p class="mt-3"><strong>This action cannot be undone!</strong></p>
                <div class="mt-4">
                    <label class="form-label">Type <code>RESET ALL</code> to confirm:</label>
                    <input type="text" class="form-control" id="resetConfirmation" placeholder="Type here to confirm">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmResetAll()">
                    <i class="fas fa-trash-alt me-2"></i>Reset Everything
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Translation Modal -->
<div class="modal fade" id="translationModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-language me-2"></i>Translation Manager</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Language Selection Column -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Select Languages</h6>
                        <div class="language-selection-panel border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                            <div class="mb-2">
                                <input type="text" class="form-control form-control-sm" id="languageSearch" 
                                       placeholder="Search languages..." onkeyup="filterLanguages()">
                            </div>
                            <div id="languageList">
                                {% if available_languages %}
                                    {% for lang in available_languages %}
                                    {% if not lang.primary %}
                                    <div class="language-item form-check py-2 border-bottom" data-language="{{ lang.name|lower }}">
                                        <input class="form-check-input language-checkbox" type="checkbox" 
                                               value="{{ lang.locale }}" id="modal_lang_{{ lang.locale }}"
                                               {{ 'checked' if lang.locale in settings.target_languages else '' }}
                                               onchange="updateTranslationPreview()">
                                        <label class="form-check-label d-flex justify-content-between align-items-center" 
                                               for="modal_lang_{{ lang.locale }}" style="width: 100%;">
                                            <span>
                                                {{ lang.name }}
                                            </span>
                                            <small class="text-muted">{{ lang.locale }}</small>
                                        </label>
                                    </div>
                                    {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted">No additional languages available</p>
                                {% endif %}
                            </div>
                            <div class="mt-2 text-center">
                                <button class="btn btn-sm btn-outline-primary" onclick="selectAllLanguages()">Select All</button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="deselectAllLanguages()">Clear All</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resource Selection Column -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Select Content Types</h6>
                        <div class="resource-selection border rounded p-3">
                            <div class="form-check py-2">
                                <input class="form-check-input resource-checkbox" type="checkbox" 
                                       value="product" id="resource_products" checked onchange="updateTranslationPreview()">
                                <label class="form-check-label d-flex justify-content-between" for="resource_products">
                                    <span><i class="fas fa-box me-2"></i>Products</span>
                                    <span class="badge bg-secondary" id="productCount">{{ stats.total_products|default(0) }}</span>
                                </label>
                            </div>
                            <div class="form-check py-2">
                                <input class="form-check-input resource-checkbox" type="checkbox" 
                                       value="collection" id="resource_collections" checked onchange="updateTranslationPreview()">
                                <label class="form-check-label d-flex justify-content-between" for="resource_collections">
                                    <span><i class="fas fa-layer-group me-2"></i>Collections</span>
                                    <span class="badge bg-secondary" id="collectionCount">{{ stats.collections_created|default(0) }}</span>
                                </label>
                            </div>
                            <div class="form-check py-2">
                                <input class="form-check-input resource-checkbox" type="checkbox" 
                                       value="page" id="resource_pages" checked onchange="updateTranslationPreview()">
                                <label class="form-check-label d-flex justify-content-between" for="resource_pages">
                                    <span><i class="fas fa-file-alt me-2"></i>Pages</span>
                                    <span class="badge bg-secondary" id="pageCount">0</span>
                                </label>
                            </div>
                        </div>
                        
                        <h6 class="mb-3 mt-4">Translation Options</h6>
                        <div class="translation-options border rounded p-3">
                            <div class="mb-3">
                                <label class="form-label">Items to Translate</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="modalTranslationLimit" 
                                           value="10" min="1" max="999" onchange="updateTranslationPreview()">
                                    <span class="input-group-text">per type</span>
                                </div>
                                <small class="form-text">Set to 999 to translate all items</small>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skipTranslated" checked>
                                <label class="form-check-label" for="skipTranslated">
                                    Skip already translated items
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="prioritizeNew">
                                <label class="form-check-label" for="prioritizeNew">
                                    Prioritize recently added items
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Preview Column -->
                    <div class="col-md-4">
                        <h6 class="mb-3">Translation Preview</h6>
                        <div class="translation-preview border rounded p-3 bg-light">
                            <div id="translationSummary">
                                <div class="alert alert-info mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Select languages and content types to see preview
                                </div>
                            </div>
                            
                            <div id="estimatedTime" class="mb-3" style="display: none;">
                                <h6>Estimated Time</h6>
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 0%">
                                        <span id="timeEstimate">Calculating...</span>
                                    </div>
                                </div>
                                <small class="text-muted">Based on ~30 seconds per item</small>
                            </div>
                            
                            <div id="costEstimate" class="mb-3" style="display: none;">
                                <h6>API Usage Estimate</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-robot me-1"></i>AI Calls: <strong id="aiCalls">0</strong></li>
                                    <li><i class="fas fa-server me-1"></i>API Requests: <strong id="apiRequests">0</strong></li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning" id="warningMessage" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="warningText"></span>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-primary" id="startTranslationBtn" onclick="startTranslation()" disabled>
                                <i class="fas fa-play me-2"></i>Start Translation
                            </button>
                            <button class="btn btn-outline-secondary" onclick="saveTranslationSettings()">
                                <i class="fas fa-save me-2"></i>Save Settings Only
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Translation Progress -->
                <div id="translationProgress" class="mt-4" style="display: none;">
                    <hr>
                    <h6>Translation Progress</h6>
                    <div class="progress mb-3" style="height: 30px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="translationProgressBar">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                    <div id="progressDetails" class="small text-muted"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{{ url_for('ai_modules.seo_languages') }}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-line me-2"></i>View Translation Status
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Draw progress circle
function drawProgressCircle(canvasId, percentage) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 35;
    const startAngle = -Math.PI / 2;
    const endAngle = startAngle + (2 * Math.PI * percentage / 100);
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 8;
    ctx.stroke();
    
    // Draw progress arc
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 8;
    ctx.lineCap = 'round';
    ctx.stroke();
}

// Initialize progress circles
document.addEventListener('DOMContentLoaded', function() {
    drawProgressCircle('productsProgress', {{ stats.products_percentage }});
});

// Toggle automation
function toggleAutomation(type, enabled) {
    fetch(`/ai/automation/toggle/${type}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ enabled: enabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${type} automation ${enabled ? 'enabled' : 'disabled'}`, 'success');
            // Update UI
            const card = document.querySelector(`#${type}Toggle`).closest('.automation-card');
            if (enabled) {
                card.classList.add('active');
            } else {
                card.classList.remove('active');
            }
        } else {
            showNotification('Failed to update automation setting', 'error');
            document.getElementById(`${type}Toggle`).checked = !enabled;
        }
    });
}

// Check and create collections manually
function checkAndCreateCollections() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking...';
    
    fetch('/ai/api/check-and-create-collections', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.tags_found > 0) {
                showNotification(`Found ${data.tags_found} new tags! Creating ${data.tags_queued} collections...`, 'success');
            } else {
                showNotification('No new tags found. Optimize products first to generate tags.', 'info');
            }
        } else {
            showNotification(data.error || 'Failed to check collections', 'error');
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Trigger daily automation manually
function triggerDailyAutomation() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Starting...';
    
    fetch('/ai/api/trigger-daily-automation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Daily automation triggered successfully!`, 'success');
            // Start polling for status
            pollTaskStatus(data.task_id);
        } else {
            showNotification(data.error || 'Failed to start optimization', 'error');
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Create collections from dashboard
function createCollectionsFromDashboard() {
    const limit = document.getElementById('collectionLimit').value || 10;
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    
    // First get available tags
    fetch('/ai/api/get-available-tags')
        .then(response => response.json())
        .then(data => {
            if (!data.success || !data.tags || data.tags.length === 0) {
                showNotification('No new tags available. Please optimize products first.', 'warning');
                btn.disabled = false;
                btn.innerHTML = originalText;
                return;
            }
            
            // Take only the requested number of tags
            const tagsToCreate = data.tags.slice(0, parseInt(limit));
            
            // Submit collection creation task
            return fetch('/ai/api/tasks/create-all-collections', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tags_data: tagsToCreate,
                    collection_type: 'smart',
                    seo_keywords: ''
                })
            });
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Creating ${limit} collections! Task ID: ${data.task_id}`, 'success');
                pollTaskStatus(data.task_id);
            } else {
                showNotification(data.error || 'Failed to start collection creation', 'error');
            }
        })
        .catch(error => {
            showNotification('Error: ' + error.message, 'error');
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
}

function createAllCollectionsFromDashboard() {
    document.getElementById('collectionLimit').value = 999;
    createCollectionsFromDashboard();
}

// Translate content from dashboard
function translateContentFromDashboard() {
    const limit = document.getElementById('translationLimit').value || 10;
    const selectedLanguages = Array.from(document.querySelectorAll('.language-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selectedLanguages.length === 0) {
        showNotification('Please select at least one language', 'warning');
        return;
    }
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Starting...';
    
    fetch('/ai/api/tasks/translate-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            target_languages: selectedLanguages,
            resource_types: ['product', 'collection', 'page'],
            seo_keywords: '',
            limit: parseInt(limit)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Translation started for ${selectedLanguages.length} languages! Task ID: ${data.task_id}`, 'success');
            pollTaskStatus(data.task_id);
        } else {
            showNotification(data.error || 'Failed to start translation', 'error');
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'error');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

function translateAllFromDashboard() {
    document.getElementById('translationLimit').value = 999;
    translateContentFromDashboard();
}

// Poll task status
function pollTaskStatus(taskId) {
    const interval = setInterval(() => {
        fetch(`/ai/api/tasks/status/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.state === 'SUCCESS') {
                    clearInterval(interval);
                    showNotification('Task completed successfully!', 'success');
                    // Reload page to show updated stats
                    setTimeout(() => location.reload(), 2000);
                } else if (data.state === 'FAILURE') {
                    clearInterval(interval);
                    showNotification('Task failed: ' + data.info, 'error');
                } else if (data.state === 'PROGRESS') {
                    // Update progress if needed
                    console.log(`Progress: ${data.current}/${data.total} - ${data.status}`);
                }
            })
            .catch(error => {
                clearInterval(interval);
                console.error('Error polling task status:', error);
            });
    }, 3000); // Poll every 3 seconds
}

// Save automation settings
function saveAutomationSettings() {
    // IMMEDIATELY capture checkbox states before ANYTHING else
    const seoEnabled = document.getElementById('seoEnabledSettings').checked;
    const collectionEnabled = document.getElementById('collectionEnabledSettings').checked;
    const translationEnabled = document.getElementById('translationEnabledSettings').checked;
    const altTextEnabled = document.getElementById('altTextEnabledSettings') ? 
        document.getElementById('altTextEnabledSettings').checked : false;
    const searchConsoleEnabled = document.getElementById('searchConsoleEnabledSettings') ? 
        document.getElementById('searchConsoleEnabledSettings').checked : false;
    
    console.log('Captured checkbox values:', { seoEnabled, collectionEnabled, translationEnabled, altTextEnabled, searchConsoleEnabled });
    
    const form = document.getElementById('automationSettingsForm');
    const formData = new FormData(form);
    
    // Handle multiple select
    const languages = Array.from(form.elements['target_languages'].selectedOptions)
        .map(option => option.value);
    
    const settings = {
        seo_enabled: seoEnabled,
        collection_enabled: collectionEnabled,
        translation_enabled: translationEnabled,
        alt_text_enabled: altTextEnabled,
        search_console_enabled: searchConsoleEnabled,
        daily_optimization_limit: parseInt(formData.get('daily_optimization_limit')),
        seo_schedule: formData.get('seo_schedule'),
        target_languages: languages,
        optimization_keywords: formData.get('optimization_keywords').split(',').map(k => k.trim()).filter(k => k),
        openai_api_key: formData.get('openai_api_key'),
        anthropic_api_key: formData.get('anthropic_api_key')
    };
    
    console.log('Saving settings:', settings);
    console.log('Settings JSON:', JSON.stringify(settings));
    
    fetch('/ai/automation/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            automation_type: 'seo_automation',
            settings: settings
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Settings saved successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('automationSettingsModal')).hide();
            // Update the toggles on the main page to reflect new settings
            document.getElementById('seoToggle').checked = settings.seo_enabled;
            document.getElementById('collectionToggle').checked = settings.collection_enabled;
            document.getElementById('translationToggle').checked = settings.translation_enabled;
            if (document.getElementById('altTextToggle')) {
                document.getElementById('altTextToggle').checked = settings.alt_text_enabled;
            }
            if (document.getElementById('searchConsoleToggle')) {
                document.getElementById('searchConsoleToggle').checked = settings.search_console_enabled;
            }
            // Reload after a delay to refresh stats
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification('Failed to save settings', 'error');
        }
    });
}

// Reset all
function confirmResetAll() {
    const confirmation = document.getElementById('resetConfirmation').value;
    
    if (confirmation !== 'RESET ALL') {
        showNotification('Please type "RESET ALL" exactly to confirm', 'error');
        return;
    }
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
    
    fetch('/ai/api/reset-all-changes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ confirmation: confirmation })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Reset completed successfully!', 'success');
            const resultsDiv = document.createElement('div');
            resultsDiv.className = 'alert alert-info mt-3';
            resultsDiv.innerHTML = '<h6>Reset Results:</h6><pre>' + data.message + '</pre>';
            document.querySelector('#resetAllModal .modal-body').appendChild(resultsDiv);
            
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            showNotification(data.error || 'Reset failed', 'error');
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'error');
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Toggle API key visibility
function toggleApiKeyVisibility(fieldName) {
    const field = document.querySelector(`input[name="${fieldName}"]`);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        button.className = 'fas fa-eye';
    }
}

// Debug checkboxes
function debugCheckboxes() {
    const seo = document.getElementById('seoEnabledSettings');
    const collection = document.getElementById('collectionEnabledSettings');
    const translation = document.getElementById('translationEnabledSettings');
    
    alert(`SEO Checkbox: ${seo ? seo.checked : 'NOT FOUND'}\n` +
          `Collection Checkbox: ${collection ? collection.checked : 'NOT FOUND'}\n` +
          `Translation Checkbox: ${translation ? translation.checked : 'NOT FOUND'}`);
}

// Show notification
function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => alertDiv.remove(), 5000);
}

// Auto-refresh activity
setInterval(() => {
    fetch(window.location.href)
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Update recent jobs table
            const newTable = doc.querySelector('.table tbody');
            const currentTable = document.querySelector('.table tbody');
            if (newTable && currentTable) {
                currentTable.innerHTML = newTable.innerHTML;
            }
            
            // Update stats if changed
            // This would require extracting stats from the HTML
        })
        .catch(error => {
            console.error('Error refreshing data:', error);
        });
}, 10000); // Every 10 seconds

// Pagination handling
let currentPage = 1;
const itemsPerPage = 10;
let allJobs = {{ recent_jobs|tojson }};

function renderJobsTable() {
    const tbody = document.querySelector('.table tbody');
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const jobsToShow = allJobs.slice(start, end);
    
    tbody.innerHTML = '';
    
    if (jobsToShow.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                    No automation jobs yet. Start optimizing your store!
                </td>
            </tr>
        `;
        return;
    }
    
    jobsToShow.forEach(job => {
        const row = document.createElement('tr');
        row.className = 'job-row';
        row.innerHTML = `
            <td>
                <div class="task-icon ${job.task_type.includes('product') ? 'products' : job.task_type.includes('collection') ? 'collections' : 'translations'}">
                    <i class="fas fa-${job.icon}"></i>
                </div>
            </td>
            <td>
                <strong>${job.task_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</strong>
            </td>
            <td>
                ${job.status === 'completed' ? '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Completed</span>' :
                  job.status === 'processing' ? '<span class="badge bg-primary"><i class="fas fa-spinner fa-spin me-1"></i>Processing</span>' :
                  job.status === 'failed' ? '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>Failed</span>' :
                  `<span class="badge bg-secondary">${job.status.charAt(0).toUpperCase() + job.status.slice(1)}</span>`}
            </td>
            <td>
                <small class="text-muted">${job.result_summary}</small>
            </td>
            <td>
                <small>${job.duration}</small>
            </td>
            <td>
                <small>${job.created_at}</small>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showJobDetails(${job.id}, '${job.task_type}')">
                    <i class="fas fa-eye"></i> Details
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function initPagination() {
    const totalPages = Math.ceil(allJobs.length / itemsPerPage);
    const pagination = document.getElementById('activityPagination');
    
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); changePage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers (show max 5 pages)
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); changePage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

function changePage(page) {
    const totalPages = Math.ceil(allJobs.length / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderJobsTable();
    initPagination();
}

// Show job details
function showJobDetails(jobId, taskType) {
    fetch(`/ai/api/automation-job/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const job = data.job;
                let content = `
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Job ID:</strong> ${job.id}<br>
                            <strong>Task Type:</strong> ${job.task_type}<br>
                            <strong>Status:</strong> <span class="badge bg-${job.status === 'completed' ? 'success' : job.status === 'failed' ? 'danger' : 'primary'}">${job.status}</span><br>
                            <strong>Created:</strong> ${new Date(job.created_at).toLocaleString()}<br>
                            <strong>Duration:</strong> ${job.duration || 'N/A'}
                        </div>
                        <div class="col-md-6">
                            <strong>Celery Task ID:</strong> <code>${job.task_id || 'N/A'}</code><br>
                            <strong>Shop ID:</strong> ${job.shop_id}<br>
                            <strong>Resource ID:</strong> ${job.resource_id || 'N/A'}
                        </div>
                    </div>
                `;
                
                // Add result details based on task type
                if (job.result_data) {
                    content += '<hr><h6>Results:</h6>';
                    const results = JSON.parse(job.result_data);
                    
                    if (taskType.includes('product')) {
                        content += `
                            <div class="alert alert-info">
                                <strong>Products Optimized:</strong> ${results.optimized || 0}<br>
                                <strong>Failed:</strong> ${results.failed || 0}<br>
                                <strong>Already Optimized:</strong> ${results.already_optimized || 0}<br>
                                <strong>Total Processed:</strong> ${results.processed || results.total || 0}
                            </div>
                        `;
                        
                        // Add links to view optimized products
                        if (results.product_ids && results.product_ids.length > 0) {
                            content += '<h6>Optimized Product IDs:</h6><div class="d-flex flex-wrap gap-2">';
                            results.product_ids.forEach(id => {
                                content += `<a href="/ai/seo/products#product-${id}" class="btn btn-sm btn-outline-primary">${id}</a>`;
                            });
                            content += '</div>';
                        }
                    } else if (taskType.includes('collection')) {
                        content += `
                            <div class="alert alert-info">
                                <strong>Collections Created:</strong> ${results.created || 0}<br>
                                <strong>Skipped (Already Exist):</strong> ${results.skipped || 0}<br>
                                <strong>Failed:</strong> ${results.failed || 0}
                            </div>
                        `;
                        
                        if (results.collection_ids && results.collection_ids.length > 0) {
                            content += '<h6>Created Collection IDs:</h6><div class="d-flex flex-wrap gap-2">';
                            results.collection_ids.forEach(id => {
                                content += `<span class="badge bg-success">${id}</span>`;
                            });
                            content += '</div>';
                        }
                    } else if (taskType.includes('translate')) {
                        content += `
                            <div class="alert alert-info">
                                <strong>Items Translated:</strong> ${results.translated || 0}<br>
                                <strong>Languages:</strong> ${results.languages ? results.languages.join(', ') : 'N/A'}<br>
                                <strong>Failed:</strong> ${results.failed || 0}<br>
                                <strong>Skipped:</strong> ${results.skipped || 0}
                            </div>
                        `;
                    }
                }
                
                // Add error message if failed
                if (job.status === 'failed' && job.error_message) {
                    content += `
                        <hr>
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${job.error_message}
                        </div>
                    `;
                }
                
                document.getElementById('jobDetailsContent').innerHTML = content;
                new bootstrap.Modal(document.getElementById('jobDetailsModal')).show();
            } else {
                showNotification('Failed to load job details', 'error');
            }
        })
        .catch(error => {
            // If endpoint doesn't exist, show basic details
            const job = allJobs.find(j => j.id === jobId);
            if (job) {
                document.getElementById('jobDetailsContent').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Task:</strong> ${job.task_type}<br>
                        <strong>Status:</strong> ${job.status}<br>
                        <strong>Results:</strong> ${job.result_summary}<br>
                        <strong>Duration:</strong> ${job.duration}<br>
                        <strong>Started:</strong> ${new Date(job.created_at).toLocaleString()}
                    </div>
                `;
                new bootstrap.Modal(document.getElementById('jobDetailsModal')).show();
            }
        });
}

// Initialize pagination on page load
document.addEventListener('DOMContentLoaded', function() {
    renderJobsTable();
    initPagination();
});

// Show translation modal
function showTranslationModal() {
    const modal = new bootstrap.Modal(document.getElementById('translationModal'));
    modal.show();
    updateTranslationPreview();
}

// Filter languages in modal
function filterLanguages() {
    const searchTerm = document.getElementById('languageSearch').value.toLowerCase();
    const languageItems = document.querySelectorAll('.language-item');
    
    languageItems.forEach(item => {
        const languageName = item.dataset.language;
        if (languageName.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// Select/deselect all languages
function selectAllLanguages() {
    document.querySelectorAll('#languageList .language-checkbox').forEach(cb => {
        cb.checked = true;
    });
    updateTranslationPreview();
}

function deselectAllLanguages() {
    document.querySelectorAll('#languageList .language-checkbox').forEach(cb => {
        cb.checked = false;
    });
    updateTranslationPreview();
}

// Update translation preview
function updateTranslationPreview() {
    const selectedLanguages = Array.from(document.querySelectorAll('#languageList .language-checkbox:checked'));
    const selectedResources = Array.from(document.querySelectorAll('.resource-checkbox:checked'));
    const limit = parseInt(document.getElementById('modalTranslationLimit').value) || 10;
    
    const summaryDiv = document.getElementById('translationSummary');
    const estimatedTimeDiv = document.getElementById('estimatedTime');
    const costEstimateDiv = document.getElementById('costEstimate');
    const warningDiv = document.getElementById('warningMessage');
    const startBtn = document.getElementById('startTranslationBtn');
    
    if (selectedLanguages.length === 0 || selectedResources.length === 0) {
        summaryDiv.innerHTML = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle me-2"></i>
                Select languages and content types to see preview
            </div>
        `;
        estimatedTimeDiv.style.display = 'none';
        costEstimateDiv.style.display = 'none';
        warningDiv.style.display = 'none';
        startBtn.disabled = true;
        return;
    }
    
    // Calculate totals
    let totalItems = 0;
    selectedResources.forEach(cb => {
        const count = parseInt(document.getElementById(cb.value + 'Count').textContent) || 0;
        totalItems += Math.min(count, limit);
    });
    
    const totalTranslations = totalItems * selectedLanguages.length;
    
    // Update summary
    summaryDiv.innerHTML = `
        <div class="mb-3">
            <h6>Translation Summary</h6>
            <ul class="list-unstyled">
                <li><strong>${selectedLanguages.length}</strong> language${selectedLanguages.length > 1 ? 's' : ''} selected</li>
                <li><strong>${selectedResources.length}</strong> content type${selectedResources.length > 1 ? 's' : ''} selected</li>
                <li><strong>${totalItems}</strong> items to translate</li>
                <li class="text-primary"><strong>${totalTranslations}</strong> total translations</li>
            </ul>
        </div>
    `;
    
    // Show estimated time
    estimatedTimeDiv.style.display = 'block';
    const estimatedMinutes = Math.ceil(totalTranslations * 0.5); // 30 seconds per item
    const hours = Math.floor(estimatedMinutes / 60);
    const minutes = estimatedMinutes % 60;
    const timeText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
    
    document.getElementById('timeEstimate').textContent = timeText;
    const progressBar = estimatedTimeDiv.querySelector('.progress-bar');
    progressBar.style.width = Math.min((estimatedMinutes / 120) * 100, 100) + '%';
    
    // Show cost estimate
    costEstimateDiv.style.display = 'block';
    document.getElementById('aiCalls').textContent = totalTranslations;
    document.getElementById('apiRequests').textContent = totalTranslations * 2; // Read + Write
    
    // Show warnings if needed
    if (totalTranslations > 100) {
        warningDiv.style.display = 'block';
        document.getElementById('warningText').textContent = 
            'Large translation job. Consider running in smaller batches to avoid timeouts.';
    } else {
        warningDiv.style.display = 'none';
    }
    
    // Enable start button
    startBtn.disabled = false;
}

// Start translation
function startTranslation() {
    const selectedLanguages = Array.from(document.querySelectorAll('#languageList .language-checkbox:checked'))
        .map(cb => cb.value);
    const selectedResources = Array.from(document.querySelectorAll('.resource-checkbox:checked'))
        .map(cb => cb.value);
    const limit = parseInt(document.getElementById('modalTranslationLimit').value) || 10;
    const skipTranslated = document.getElementById('skipTranslated').checked;
    const prioritizeNew = document.getElementById('prioritizeNew').checked;
    
    const btn = document.getElementById('startTranslationBtn');
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting Translation...';
    
    // Show progress section
    document.getElementById('translationProgress').style.display = 'block';
    
    fetch('/ai/api/translate/all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            languages: selectedLanguages,
            resource_types: selectedResources,
            limit: limit,
            skip_translated: skipTranslated,
            prioritize_new: prioritizeNew
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Translation job started successfully!', 'success');
            pollTranslationProgress(data.task_id);
        } else {
            showNotification(data.error || 'Failed to start translation', 'error');
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    })
    .catch(error => {
        showNotification('Error: ' + error.message, 'error');
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Poll translation progress
function pollTranslationProgress(taskId) {
    const progressBar = document.getElementById('translationProgressBar');
    const progressText = document.getElementById('progressText');
    const progressDetails = document.getElementById('progressDetails');
    
    const interval = setInterval(() => {
        fetch(`/ai/api/tasks/status/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.state === 'SUCCESS') {
                    clearInterval(interval);
                    progressBar.style.width = '100%';
                    progressText.textContent = '100%';
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.add('bg-success');
                    progressDetails.innerHTML = `<i class="fas fa-check-circle text-success me-2"></i>Translation completed successfully!`;
                    
                    // Re-enable button
                    const btn = document.getElementById('startTranslationBtn');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-check me-2"></i>Translation Complete';
                    
                    // Reload stats after 2 seconds
                    setTimeout(() => location.reload(), 2000);
                } else if (data.state === 'FAILURE') {
                    clearInterval(interval);
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.add('bg-danger');
                    progressDetails.innerHTML = `<i class="fas fa-exclamation-circle text-danger me-2"></i>Translation failed: ${data.info}`;
                    
                    const btn = document.getElementById('startTranslationBtn');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-play me-2"></i>Start Translation';
                } else if (data.state === 'PROGRESS') {
                    const percentage = Math.round((data.current / data.total) * 100);
                    progressBar.style.width = percentage + '%';
                    progressText.textContent = percentage + '%';
                    progressDetails.innerHTML = `Translating: ${data.current} of ${data.total} items - ${data.status}`;
                }
            })
            .catch(error => {
                clearInterval(interval);
                console.error('Error polling translation progress:', error);
            });
    }, 2000);
}

// Save translation settings only
function saveTranslationSettings() {
    const selectedLanguages = Array.from(document.querySelectorAll('#languageList .language-checkbox:checked'))
        .map(cb => cb.value);
    
    fetch('/ai/automation/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            automation_type: 'seo_automation',
            settings: {
                target_languages: selectedLanguages
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Translation settings saved successfully', 'success');
        } else {
            showNotification('Failed to save settings', 'error');
        }
    });
}
</script>
{% endblock %}