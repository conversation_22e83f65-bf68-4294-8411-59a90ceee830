from flask import Blueprint, render_template, request, redirect, url_for, session, flash
from app import db
from app.models.access_control import AccessKey, UserSettings, UserShop
from app.models.shop import Shop
from app.auth import admin_required
import uuid
import secrets
import string
import os

auth_bp = Blueprint('auth', __name__)


def generate_access_code():
    """Generate a secure access code"""
    # Format: XXXX-XXXX-XXXX-XXXX
    parts = []
    for _ in range(4):
        part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
        parts.append(part)
    return '-'.join(parts)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login with access key"""
    if request.method == 'POST':
        access_code = request.form.get('access_code', '').strip()
        
        # Find the access key
        access_key = AccessKey.query.filter_by(code=access_code, is_active=True).first()
        
        if access_key:
            session['access_key_id'] = str(access_key.id)
            session['user_email'] = access_key.email
            
            # Check if user has settings, if not redirect to settings
            if not access_key.user_settings:
                return redirect(url_for('auth.settings'))
            
            # Check if user has any stores, if not redirect to store connection
            user_shops = UserShop.query.filter_by(access_key_id=access_key.id).all()
            if not user_shops:
                flash('Please connect your first Shopify store', 'info')
                return redirect(url_for('shopify.connect'))
            
            # Ensure one shop is marked as current
            current_shop = UserShop.get_current_shop(access_key.id)
            if not current_shop and user_shops:
                user_shops[0].is_current = True
                db.session.commit()
            
            # Redirect to originally requested page or home
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('ai_modules.seo_products'))
        else:
            flash('Invalid or inactive access code', 'error')
    
    return render_template('auth/login.html')


@auth_bp.route('/logout')
def logout():
    """Logout user"""
    session.clear()
    return redirect(url_for('auth.login'))


@auth_bp.route('/settings', methods=['GET', 'POST'])
def settings():
    """User settings page for API keys"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    access_key = AccessKey.query.get(session['access_key_id'])
    if not access_key:
        return redirect(url_for('auth.login'))
    
    # Get or create user settings
    user_settings = access_key.user_settings
    if not user_settings:
        user_settings = UserSettings(access_key_id=access_key.id)
        db.session.add(user_settings)
        db.session.commit()
    
    if request.method == 'POST':
        # Update API keys
        shop_domain = request.form.get('shopify_shop_domain', '').strip()
        
        # Ensure shop domain has .myshopify.com
        if shop_domain and not shop_domain.endswith('.myshopify.com'):
            if '.' not in shop_domain:
                shop_domain = f"{shop_domain}.myshopify.com"
            else:
                flash('Shop domain must be in the format: your-store.myshopify.com', 'error')
                return render_template('auth/settings.html', settings=user_settings)
        
        user_settings.shopify_shop_domain = shop_domain
        user_settings.shopify_api_key = request.form.get('shopify_api_key', '').strip()
        user_settings.shopify_api_secret = request.form.get('shopify_api_secret', '').strip()
        user_settings.shopify_access_token = request.form.get('shopify_access_token', '').strip()
        user_settings.anthropic_api_key = request.form.get('anthropic_api_key', '').strip()
        user_settings.openai_api_key = request.form.get('openai_api_key', '').strip()
        user_settings.store_actual_live_url = request.form.get('store_actual_live_url', '').strip()
        
        db.session.commit()
        flash('Settings updated successfully', 'success')
        return redirect(url_for('ai_modules.seo_products'))
    
    return render_template('auth/settings.html', settings=user_settings)


# Admin routes
@auth_bp.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Admin login page"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Get admin credentials from environment variables
        admin_username = os.environ.get('ADMIN_USERNAME')
        admin_password = os.environ.get('ADMIN_PASSWORD')
        
        # Check admin credentials
        if username == admin_username and password == admin_password:
            session['is_admin'] = True
            return redirect(url_for('auth.admin_dashboard'))
        else:
            flash('Invalid admin credentials', 'error')
    
    return render_template('auth/admin_login.html')


@auth_bp.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    """Admin dashboard to manage access keys"""
    access_keys = AccessKey.query.order_by(AccessKey.created_at.desc()).all()
    return render_template('auth/admin_dashboard.html', access_keys=access_keys)


@auth_bp.route('/admin/create-key', methods=['POST'])
@admin_required
def create_access_key():
    """Create a new access key"""
    email = request.form.get('email', '').strip()
    
    if not email:
        flash('Email is required', 'error')
        return redirect(url_for('auth.admin_dashboard'))
    
    # Generate new access key
    access_key = AccessKey(
        code=generate_access_code(),
        email=email
    )
    db.session.add(access_key)
    db.session.commit()
    
    flash(f'Access key created: {access_key.code}', 'success')
    return redirect(url_for('auth.admin_dashboard'))


@auth_bp.route('/admin/toggle-key/<key_id>')
@admin_required
def toggle_access_key(key_id):
    """Enable/disable an access key"""
    access_key = AccessKey.query.get(key_id)
    if access_key:
        access_key.is_active = not access_key.is_active
        db.session.commit()
        status = 'enabled' if access_key.is_active else 'disabled'
        flash(f'Access key {status}', 'success')
    return redirect(url_for('auth.admin_dashboard'))


@auth_bp.route('/stores')
def store_management():
    """Store management page"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    access_key_id = session['access_key_id']
    user_shops = UserShop.query.filter_by(access_key_id=access_key_id).join(Shop).all()
    
    return render_template('auth/store_management.html', user_shops=user_shops)


@auth_bp.route('/switch-store', methods=['POST'])
def switch_store():
    """Switch to a different store"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    shop_id = request.form.get('shop_id')
    access_key_id = session['access_key_id']
    
    if shop_id:
        result = UserShop.set_current_shop(access_key_id, int(shop_id))
        if result:
            db.session.commit()
            flash(f'Switched to {result.shop.shop_name or result.shop.shop_domain}', 'success')
        else:
            flash('Store not found', 'error')
    
    return redirect(url_for('auth.store_management'))


@auth_bp.route('/remove-store', methods=['POST'])
def remove_store():
    """Remove a store from user's list"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    shop_id = request.form.get('shop_id')
    access_key_id = session['access_key_id']
    
    if shop_id:
        user_shop = UserShop.query.filter_by(
            access_key_id=access_key_id,
            shop_id=int(shop_id)
        ).first()
        
        if user_shop and not user_shop.is_current:
            db.session.delete(user_shop)
            db.session.commit()
            flash('Store removed successfully', 'success')
        else:
            flash('Cannot remove current store', 'error')
    
    return redirect(url_for('auth.store_management'))


@auth_bp.route('/update-schedule', methods=['POST'])
def update_schedule():
    """Update automation schedule for a store"""
    if 'access_key_id' not in session:
        return redirect(url_for('auth.login'))
    
    user_shop_id = request.form.get('user_shop_id')
    automation_enabled = request.form.get('automation_enabled') == '1'
    automation_hour = int(request.form.get('automation_hour', 2))
    automation_minute = int(request.form.get('automation_minute', 0))
    
    access_key_id = session['access_key_id']
    
    user_shop = UserShop.query.filter_by(
        id=user_shop_id,
        access_key_id=access_key_id
    ).first()
    
    if user_shop:
        user_shop.automation_enabled = automation_enabled
        user_shop.set_automation_time(automation_hour, automation_minute)
        db.session.commit()
        
        schedule_str = f"{automation_hour:02d}:{automation_minute:02d} UTC" if automation_enabled else "Disabled"
        flash(f'Schedule updated: {schedule_str}', 'success')
    else:
        flash('Store not found', 'error')
    
    return redirect(url_for('auth.store_management'))


@auth_bp.route('/admin/logout')
def admin_logout():
    """Admin logout"""
    session.pop('is_admin', None)
    return redirect(url_for('auth.admin_login'))