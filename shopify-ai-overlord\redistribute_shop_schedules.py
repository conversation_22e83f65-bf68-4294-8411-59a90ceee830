#!/usr/bin/env python3
"""
Redistribute Shop Automation Schedules to 20-Minute Intervals

This script redistributes all existing shops to use proper 20-minute intervals
starting from 4:00 AM UTC to avoid conflicts with system tasks.

Run this script to fix existing shop schedules after implementing the new
20-minute interval system.
"""

import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.access_control import UserShop
from app.models.ai_config import AIConfig
import time

def redistribute_schedules():
    """Redistribute all shops to 20-minute intervals"""
    print(f"\n{'='*60}")
    print("REDISTRIBUTING SHOP AUTOMATION SCHEDULES")
    print("Setting up 20-minute intervals starting from 4:00 AM UTC")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    app = create_app()
    
    with app.app_context():
        # Get all user shops ordered by shop_id for consistency
        user_shops = UserShop.query.order_by(UserShop.shop_id).all()
        
        print(f"Found {len(user_shops)} total shops")
        
        # Filter shops that have automation configurations
        shops_with_automation = []
        for user_shop in user_shops:
            config = AIConfig.query.filter_by(
                shop_id=user_shop.shop_id,
                module_type='seo_automation'
            ).first()
            
            if config and config.config_data:
                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])
                
                if has_enabled_automation:
                    shops_with_automation.append(user_shop)
                    print(f"Shop {user_shop.shop_id}: Has enabled automations")
                else:
                    print(f"Shop {user_shop.shop_id}: No enabled automations")
            else:
                print(f"Shop {user_shop.shop_id}: No automation config")
        
        print(f"\nFound {len(shops_with_automation)} shops with enabled automations")
        
        if not shops_with_automation:
            print("No shops to redistribute. Exiting.")
            return
        
        # Redistribute schedules with 20-minute intervals
        start_time = 4 * 60  # 4:00 AM in minutes
        interval = 20  # 20-minute intervals
        
        print(f"\nRedistributing {len(shops_with_automation)} shops:")
        print("-" * 50)
        
        for i, user_shop in enumerate(shops_with_automation):
            # Calculate new time slot
            slot_minutes = start_time + (i * interval)
            new_hour = (slot_minutes // 60) % 24
            new_minute = slot_minutes % 60
            
            old_time = f"{user_shop.automation_hour:02d}:{user_shop.automation_minute:02d}"
            new_time = f"{new_hour:02d}:{new_minute:02d}"
            
            # Update the shop's automation time
            user_shop.automation_hour = new_hour
            user_shop.automation_minute = new_minute
            
            print(f"Shop {user_shop.shop_id:3d}: {old_time} UTC → {new_time} UTC")
        
        # Commit all changes
        try:
            db.session.commit()
            print(f"\n✓ Successfully redistributed {len(shops_with_automation)} shop schedules")
            print("\nNew schedule summary:")
            print("-" * 50)
            
            for i, user_shop in enumerate(shops_with_automation):
                time_str = f"{user_shop.automation_hour:02d}:{user_shop.automation_minute:02d}"
                print(f"Shop {user_shop.shop_id:3d}: {time_str} UTC")
            
            print(f"\nSchedule spans from 04:00 UTC to {shops_with_automation[-1].automation_hour:02d}:{shops_with_automation[-1].automation_minute:02d} UTC")
            print("Each shop runs 20 minutes apart from the next")
            
        except Exception as e:
            print(f"\n✗ Error committing changes: {e}")
            db.session.rollback()
            return False
    
    return True

def verify_schedules():
    """Verify that schedules are properly distributed"""
    print(f"\n{'='*60}")
    print("VERIFYING SCHEDULE DISTRIBUTION")
    print(f"{'='*60}\n")
    
    app = create_app()
    
    with app.app_context():
        user_shops = UserShop.query.filter_by(automation_enabled=True).order_by(
            UserShop.automation_hour, 
            UserShop.automation_minute
        ).all()
        
        if not user_shops:
            print("No shops with automation enabled found.")
            return
        
        print("Current automation schedule:")
        print("-" * 40)
        
        prev_minutes = None
        conflicts = []
        
        for user_shop in user_shops:
            current_minutes = user_shop.automation_hour * 60 + user_shop.automation_minute
            time_str = f"{user_shop.automation_hour:02d}:{user_shop.automation_minute:02d}"
            
            if prev_minutes is not None:
                interval = current_minutes - prev_minutes
                if interval < 20:
                    conflicts.append(f"Shop {user_shop.shop_id} at {time_str} (only {interval} min after previous)")
                print(f"Shop {user_shop.shop_id:3d}: {time_str} UTC (+{interval:2d} min)")
            else:
                print(f"Shop {user_shop.shop_id:3d}: {time_str} UTC")
            
            prev_minutes = current_minutes
        
        if conflicts:
            print(f"\n⚠️  Found {len(conflicts)} scheduling conflicts:")
            for conflict in conflicts:
                print(f"   {conflict}")
        else:
            print(f"\n✓ All {len(user_shops)} shops have proper 20+ minute intervals")

if __name__ == '__main__':
    print("Shop Schedule Redistribution Utility")
    print("This will redistribute all shops to 20-minute intervals")
    
    response = input("\nProceed with redistribution? (y/N): ").strip().lower()
    
    if response == 'y':
        success = redistribute_schedules()
        if success:
            verify_schedules()
            print(f"\n{'='*60}")
            print("REDISTRIBUTION COMPLETE")
            print("Restart your Celery Beat scheduler to apply the new schedules")
            print(f"{'='*60}\n")
    else:
        print("Redistribution cancelled.")
        verify_schedules()
