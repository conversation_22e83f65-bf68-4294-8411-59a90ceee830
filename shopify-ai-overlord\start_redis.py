#!/usr/bin/env python3
"""Start Redis server or check if it's running"""
import subprocess
import time
import sys
import os
import platform
import socket

def is_redis_running(host='localhost', port=6379):
    """Check if Redis is running"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def start_redis_windows():
    """Start Redis on Windows using WSL or Docker"""
    print("Starting Redis on Windows...")
    
    # First check if Redis is already running
    if is_redis_running():
        print("✓ Redis is already running on port 6379")
        return True
    
    # Try WSL first
    try:
        # Check if WSL is available
        result = subprocess.run(['wsl', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("Starting Redis in WSL...")
            # Start Redis in background using WSL
            subprocess.Popen(['wsl', 'redis-server', '--daemonize', 'yes'])
            time.sleep(2)
            if is_redis_running():
                print("✓ Redis started successfully in WSL")
                return True
    except:
        pass
    
    # Try Docker
    try:
        # Check if Docker is available
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("Starting Redis using Docker...")
            # Stop any existing Redis container
            subprocess.run(['docker', 'stop', 'shopify-ai-redis'], capture_output=True)
            subprocess.run(['docker', 'rm', 'shopify-ai-redis'], capture_output=True)
            # Start new Redis container
            subprocess.run([
                'docker', 'run', '-d',
                '--name', 'shopify-ai-redis',
                '-p', '6379:6379',
                'redis:latest'
            ])
            time.sleep(3)
            if is_redis_running():
                print("✓ Redis started successfully in Docker")
                return True
    except:
        pass
    
    print("✗ Could not start Redis. Please install Redis manually:")
    print("  - WSL: wsl sudo apt-get install redis-server")
    print("  - Docker: docker run -d -p 6379:6379 redis:latest")
    print("  - Or download Redis for Windows from: https://github.com/microsoftarchive/redis/releases")
    return False

def start_redis_unix():
    """Start Redis on Unix-like systems"""
    print("Starting Redis...")
    
    if is_redis_running():
        print("✓ Redis is already running on port 6379")
        return True
    
    try:
        # Try to start Redis
        subprocess.Popen(['redis-server', '--daemonize', 'yes'])
        time.sleep(2)
        if is_redis_running():
            print("✓ Redis started successfully")
            return True
    except FileNotFoundError:
        print("✗ Redis not found. Please install it:")
        print("  - Ubuntu/Debian: sudo apt-get install redis-server")
        print("  - macOS: brew install redis")
        print("  - CentOS/RHEL: sudo yum install redis")
        return False
    
    return False

def main():
    """Main function"""
    system = platform.system()
    
    if system == 'Windows':
        success = start_redis_windows()
    else:
        success = start_redis_unix()
    
    if not success:
        sys.exit(1)
    
    # Save Redis URL to environment
    redis_url = 'redis://localhost:6379/0'
    print(f"\nRedis URL: {redis_url}")
    print("Add this to your .env file: REDIS_URL=redis://localhost:6379/0")

if __name__ == '__main__':
    main()