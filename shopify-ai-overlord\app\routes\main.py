from flask import Blueprint, render_template, redirect, url_for, session, jsonify
from ..auth import login_required, get_current_user_settings
from app.models.shop import Shop
from app.models.automation_job import AutomationJob
from app.models.change_log import ChangeLog, OptimizationStatus
from app.services.shopify_service import ShopifyService
from app.services.change_tracking_service import ChangeTrackingService
from datetime import datetime, timedelta
from app.routes.ai_modules import get_current_user_id

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    # Check if user has access key in session
    if 'access_key_id' in session:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard showing all automation modules"""
    from app.models.ai_config import AIConfig
    from app.models import GoogleOAuth
    
    # Get user settings from session
    user_settings = get_current_user_settings()
    
    if not user_settings:
        return redirect(url_for('auth.login'))
    
    # Get unified shop ID
    shop_id = get_current_user_id()
    
    # Get automation configs for all modules
    seo_config = AIConfig.query.filter_by(
        shop_id=shop_id,
        module_type='seo_automation'
    ).first()
    
    # Check if Google OAuth is connected
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first() if shop_id else None
    
    # Get SEO statistics
    seo_stats = {
        'products_optimized': 0,
        'collections_created': 0,
        'translations_completed': 0,
        'search_console_connected': bool(google_oauth),
        'enabled': False,
        'automations_active': 0
    }
    
    if shop_id:
        # Get optimization stats
        seo_stats['products_optimized'] = OptimizationStatus.query.filter_by(
            shop_id=shop_id,
            resource_type='product',
            optimization_type='seo',
            is_optimized=True
        ).count()
        
        # Get collection stats
        seo_stats['collections_created'] = ChangeLog.query.filter_by(
            shop_id=shop_id,
            resource_type='collection',
            action='create'
        ).count()
        
        # Get translation stats
        from app.models.change_log import TranslationStatus
        seo_stats['translations_completed'] = TranslationStatus.query.filter_by(
            shop_id=shop_id,
            is_translated=True
        ).count()
        
        # Check enabled automations
        if seo_config and seo_config.config_data:
            import json
            config_data = seo_config.config_data
            
            # Ensure config_data is a dictionary (handle cases where it might be a string)
            if isinstance(config_data, str):
                try:
                    config_data = json.loads(config_data)
                except (json.JSONDecodeError, TypeError):
                    config_data = {}
            elif not isinstance(config_data, dict):
                config_data = {}
            
            seo_stats['enabled'] = any([
                config_data.get('seo_enabled', False),
                config_data.get('collection_enabled', False),
                config_data.get('translation_enabled', False),
                config_data.get('search_console_enabled', False)
            ])
            
            # Count active automations
            automations = ['seo_enabled', 'collection_enabled', 'translation_enabled', 'search_console_enabled']
            seo_stats['automations_active'] = sum(1 for a in automations if config_data.get(a, False))
    
    # Placeholder stats for other modules (coming soon)
    ads_stats = {
        'enabled': False,
        'campaigns_active': 0,
        'total_spend': 0,
        'conversions': 0,
        'automations_active': 0
    }
    
    email_stats = {
        'enabled': False,
        'campaigns_sent': 0,
        'subscribers': 0,
        'open_rate': 0,
        'automations_active': 0
    }
    
    support_stats = {
        'enabled': False,
        'tickets_resolved': 0,
        'avg_response_time': 0,
        'satisfaction_rate': 0,
        'automations_active': 0
    }
    
    # Get recent activity across all modules
    recent_activity = []
    if shop_id:
        # Get recent automation jobs
        recent_jobs = AutomationJob.get_recent_jobs(shop_id, limit=5)
        for job in recent_jobs:
            activity_type = 'seo'  # Default to SEO for now
            activity_icon = {
                'product_seo': 'box',
                'bulk_product_seo': 'boxes',
                'create_collection': 'layer-group',
                'bulk_create_collections': 'layers',
                'translate': 'language',
                'bulk_translate': 'globe',
                'search_console_automation': 'chart-line',
                'search_console_metrics': 'chart-bar'
            }.get(job.task_type, 'tasks')
            
            recent_activity.append({
                'type': activity_type,
                'icon': activity_icon,
                'description': job.task_type.replace('_', ' ').title(),
                'status': job.status,
                'timestamp': job.created_at
            })
    
    return render_template('dashboard.html',
                         seo_stats=seo_stats,
                         ads_stats=ads_stats,
                         email_stats=email_stats,
                         support_stats=support_stats,
                         recent_activity=recent_activity[:10]  # Limit to 10 most recent
                         )

def get_dashboard_stats(shop_id):
    """Get dashboard statistics"""
    stats = {
        'products_optimized': 0,
        'total_products': 0,
        'products_percentage': 0,
        'collections_created': 0,
        'translations_completed': 0,
        'languages_count': 0,
        'tasks_today': 0,
        'tasks_remaining': 0,
        'daily_products': 0,
        'api_calls': 0
    }
    
    try:
        # Get user settings for API access
        from app.auth import get_user_api_keys
        api_keys = get_user_api_keys()
        
        # Get product stats
        if api_keys.get('shopify_shop_domain') and api_keys.get('shopify_access_token'):
            shopify_service = ShopifyService(api_keys['shopify_shop_domain'], api_keys['shopify_access_token'])
            stats['total_products'] = shopify_service.get_products_count()
        
        # Get optimization stats
        optimized = OptimizationStatus.query.filter_by(
            shop_id=shop_id,
            resource_type='product',
            optimization_type='seo',
            is_optimized=True
        ).count()
        stats['products_optimized'] = optimized
        
        if stats['total_products'] > 0:
            stats['products_percentage'] = int((optimized / stats['total_products']) * 100)
        
        # Get collection stats
        collection_changes = ChangeLog.query.filter_by(
            shop_id=shop_id,
            resource_type='collection',
            action='create'
        ).count()
        stats['collections_created'] = collection_changes
        
        # Get translation stats
        translation_jobs = AutomationJob.query.filter_by(
            shop_id=shop_id,
            task_type='translate',
            status='completed'
        ).count()
        stats['translations_completed'] = translation_jobs
        
        # Get enabled languages count
        shop = Shop.query.get(shop_id)
        if shop:
            settings = shop.get_automation_settings()
            stats['languages_count'] = len(settings.get('target_languages', []))
        
        # Get today's stats
        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        
        today_jobs = AutomationJob.query.filter(
            AutomationJob.shop_id == shop_id,
            AutomationJob.created_at >= today_start
        ).all()
        
        stats['tasks_today'] = len(today_jobs)
        stats['tasks_remaining'] = len([j for j in today_jobs if j.status in ['pending', 'processing']])
        
        # Count products optimized today
        today_products = AutomationJob.query.filter(
            AutomationJob.shop_id == shop_id,
            AutomationJob.task_type.in_(['product_seo', 'bulk_product_seo']),
            AutomationJob.status == 'completed',
            AutomationJob.created_at >= today_start
        ).count()
        stats['daily_products'] = today_products
        
        # Estimate API calls (rough estimate)
        stats['api_calls'] = len(today_jobs) * 3  # Rough estimate
        
    except Exception as e:
        print(f"Error getting dashboard stats: {e}")
    
    return stats

@bp.route('/dashboard/automation/status')
@login_required
def automation_status():
    """API endpoint for automation status"""
    user_settings = get_current_user_settings()
    
    if not user_settings:
        return jsonify({'error': 'Not authenticated'}), 401
    
    # Get shop ID for the user
    shop_id = get_current_user_id()
    
    # Get recent jobs
    recent_jobs = AutomationJob.get_recent_jobs(shop_id, limit=5) if shop_id else []
    
    jobs_data = []
    for job in recent_jobs:
        job_dict = job.to_dict()
        # Add icon
        job_dict['icon'] = {
            'product_seo': 'box',
            'bulk_product_seo': 'boxes',
            'create_collection': 'layer-group',
            'bulk_create_collections': 'layers',
            'translate': 'language',
            'bulk_translate': 'globe',
        }.get(job.task_type, 'tasks')
        jobs_data.append(job_dict)
    
    # Get active jobs count
    active_jobs = AutomationJob.query.filter_by(
        shop_id=shop_id,
        status='processing'
    ).count() if shop_id else 0
    
    return jsonify({
        'status': 'active' if active_jobs > 0 else 'idle',
        'active_jobs': active_jobs,
        'recent_jobs': jobs_data,
        'last_run': recent_jobs[0].created_at.isoformat() if recent_jobs else None
    })