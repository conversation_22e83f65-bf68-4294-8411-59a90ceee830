#!/usr/bin/env python3
"""
Manual script to create the user_shops table
Run this if migrations are giving trouble
"""

import sqlite3
import os

def create_user_shops_table():
    """Create the user_shops table manually"""
    
    # Database path
    db_path = 'instance/shopify_ai_control.db'
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='user_shops'
        """)
        
        if cursor.fetchone():
            print("user_shops table already exists")
            conn.close()
            return True
        
        # Create the user_shops table
        cursor.execute("""
            CREATE TABLE user_shops (
                id VARCHAR(36) NOT NULL PRIMARY KEY,
                access_key_id VARCHAR(36) NOT NULL,
                shop_id INTEGER NOT NULL,
                automation_hour INTEGER NOT NULL DEFAULT 2,
                automation_minute INTEGER NOT NULL DEFAULT 0,
                automation_enabled BOOLEAN NOT NULL DEFAULT 1,
                is_current BOOLEAN NOT NULL DEFAULT 0,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (access_key_id) REFERENCES access_keys(id),
                FOREIGN KEY (shop_id) REFERENCES shops(id),
                UNIQUE (access_key_id, shop_id)
            )
        """)
        
        conn.commit()
        print("user_shops table created successfully")
        
        # Verify the table was created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_shops'")
        if cursor.fetchone():
            print("Table verification: SUCCESS")
            return True
        else:
            print("Table verification: FAILED")
            return False
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = create_user_shops_table()
    if success:
        print("\n✅ user_shops table is ready!")
        print("You can now run your Flask application.")
    else:
        print("\n❌ Failed to create user_shops table.")
        print("Please check the error messages above.")