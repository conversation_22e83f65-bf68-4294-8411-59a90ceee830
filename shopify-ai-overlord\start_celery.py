#!/usr/bin/env python3
"""Start Celery worker and beat scheduler"""
import subprocess
import sys
import os
import platform
import time
import signal
from multiprocessing import Process

# Add project directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_celery_worker():
    """Start Celery worker process"""
    print("Starting Celery worker...")
    
    # Celery command for different platforms
    if platform.system() == 'Windows':
        # Windows doesn't support fork, use threads
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'celery_app.celery_app',
            'worker',
            '--loglevel=info',
            '--pool=threads',  # Use threads on Windows
            '--concurrency=4'
        ]
    else:
        # Unix systems can use prefork
        cmd = [
            'celery',
            '-A', 'celery_app.celery_app',
            'worker',
            '--loglevel=info',
            '--concurrency=4'
        ]
    
    try:
        process = subprocess.Popen(cmd)
        print("✓ Celery worker started")
        return process
    except Exception as e:
        print(f"✗ Failed to start Celery worker: {e}")
        return None

def start_celery_beat():
    """Start Celery beat scheduler for periodic tasks"""
    print("Starting Celery beat scheduler...")
    
    cmd = [
        'celery',
        '-A', 'celery_app.celery_app',
        'beat',
        '--loglevel=info'
    ]
    
    if platform.system() == 'Windows':
        cmd = [sys.executable, '-m'] + cmd
    
    try:
        process = subprocess.Popen(cmd)
        print("✓ Celery beat started")
        return process
    except Exception as e:
        print(f"✗ Failed to start Celery beat: {e}")
        return None

def start_flower():
    """Start Flower for monitoring (optional)"""
    print("Starting Flower monitoring dashboard...")
    
    cmd = [
        'celery',
        '-A', 'celery_app.celery_app',
        'flower',
        '--port=5555'
    ]
    
    if platform.system() == 'Windows':
        cmd = [sys.executable, '-m'] + cmd
    
    try:
        process = subprocess.Popen(cmd)
        print("✓ Flower started at http://localhost:5555")
        return process
    except Exception as e:
        print(f"Note: Flower monitoring not available: {e}")
        return None

def main():
    """Main function"""
    processes = []
    
    # Check if Redis is running
    from start_redis import is_redis_running
    if not is_redis_running():
        print("✗ Redis is not running. Please start Redis first:")
        print("  python start_redis.py")
        sys.exit(1)
    
    # Start Celery worker
    worker = start_celery_worker()
    if worker:
        processes.append(worker)
    else:
        sys.exit(1)
    
    # Wait a bit for worker to start
    time.sleep(2)
    
    # Start Celery beat (for scheduled tasks)
    beat = start_celery_beat()
    if beat:
        processes.append(beat)
    
    # Optionally start Flower
    flower = start_flower()
    if flower:
        processes.append(flower)
    
    print("\n✓ All Celery services started!")
    print("\nPress Ctrl+C to stop all services...")
    
    # Handle shutdown
    def signal_handler(sig, frame):
        print("\n\nShutting down Celery services...")
        for p in processes:
            if p:
                p.terminate()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Keep running
    try:
        for p in processes:
            if p:
                p.wait()
    except KeyboardInterrupt:
        signal_handler(None, None)

if __name__ == '__main__':
    main()