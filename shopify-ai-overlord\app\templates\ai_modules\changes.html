{% extends "base_auth.html" %}

{% block title %}Change History - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    .change-row {
        transition: background-color 0.2s;
    }
    
    .change-row:hover {
        background-color: #f8f9fa;
    }
    
    .change-details {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .before-after-badge {
        font-size: 0.75rem;
        font-weight: normal;
    }
    
    .change-checkbox {
        width: 20px;
        height: 20px;
        cursor: pointer;
    }
    
    .select-all-checkbox {
        width: 20px;
        height: 20px;
        cursor: pointer;
    }
    
    .change-metadata {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .reset-danger-zone {
        border: 2px dashed #dc3545;
        border-radius: 8px;
        padding: 20px;
        margin-top: 30px;
        background-color: #fff5f5;
    }
    
    .confirmation-input {
        font-family: monospace;
        font-weight: bold;
    }
    
    .action-badge {
        font-size: 0.75rem;
    }
    
    .resource-link {
        text-decoration: none;
        color: inherit;
    }
    
    .resource-link:hover {
        text-decoration: underline;
    }
    
    .time-ago {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.3;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Change History</h1>
                    <p class="text-muted mb-0">View and manage all changes made to your store</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                        <i class="fas fa-refresh me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-history fa-2x text-primary mb-2"></i>
                    <h3 class="mb-0" id="total-changes">0</h3>
                    <small class="text-muted">Total Changes</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="mb-0" id="active-changes">0</h3>
                    <small class="text-muted">Active Changes</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-undo fa-2x text-warning mb-2"></i>
                    <h3 class="mb-0" id="reverted-changes">0</h3>
                    <small class="text-muted">Reverted</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-box fa-2x text-info mb-2"></i>
                    <h3 class="mb-0" id="affected-products">0</h3>
                    <small class="text-muted">Products Modified</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label class="form-label">Resource Type</label>
                <select class="form-select" id="filter-resource-type">
                    <option value="">All Types</option>
                    <option value="product">Products</option>
                    <option value="collection">Collections</option>
                    <option value="homepage">Homepage</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="filter-status">
                    <option value="">All Statuses</option>
                    <option value="completed">Active</option>
                    <option value="reverted">Reverted</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Action</label>
                <select class="form-select" id="filter-action">
                    <option value="">All Actions</option>
                    <option value="optimize_seo">SEO Optimization</option>
                    <option value="update_title">Title Update</option>
                    <option value="update_description">Description Update</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter me-2"></i>Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Changes Table -->
    <div class="card">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Changes Log
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-warning" id="revert-selected-btn" disabled onclick="revertSelected()">
                        <i class="fas fa-undo me-1"></i>Revert Selected
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="select-all-checkbox" id="select-all">
                            </th>
                            <th>Resource</th>
                            <th>Action</th>
                            <th>Changes</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th width="100">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="changes-tbody">
                        <!-- Changes will be loaded here -->
                    </tbody>
                </table>
            </div>
            
            <!-- Empty State -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <i class="fas fa-history"></i>
                <h5>No Changes Found</h5>
                <p class="mb-0">Start optimizing your products to see changes here.</p>
            </div>
            
            <!-- Loading State -->
            <div class="text-center p-4" id="loading-state">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading change history...</p>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="reset-danger-zone">
        <h5 class="text-danger mb-3">
            <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
        </h5>
        <p class="mb-3">
            This action will reset ALL changes made to your store. This cannot be undone.
            All optimizations will be reverted and you'll need to start over.
        </p>
        <div class="row align-items-end">
            <div class="col-md-8">
                <label class="form-label text-danger">
                    Type exactly: <code>YES RESET EVERYTHING I FUCKED UP</code>
                </label>
                <input type="text" class="form-control confirmation-input" id="reset-confirmation" 
                       placeholder="Enter confirmation text">
            </div>
            <div class="col-md-4">
                <button class="btn btn-danger w-100" onclick="resetAllChanges()">
                    <i class="fas fa-trash-alt me-2"></i>Reset All Changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Change Details Modal -->
<div class="modal fade" id="changeDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="change-details-content">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allChanges = [];
let selectedChanges = new Set();

// Load changes on page load
document.addEventListener('DOMContentLoaded', function() {
    loadChanges();
    
    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.change-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = this.checked;
            if (this.checked) {
                selectedChanges.add(cb.value);
            } else {
                selectedChanges.delete(cb.value);
            }
        });
        updateRevertButton();
    });
});

async function loadChanges() {
    try {
        console.log('Loading change history...');
        const response = await fetch('/ai/api/change-history?limit=100');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        if (data.success) {
            allChanges = data.changes || [];
            displayChanges(allChanges);
            updateStats();
        } else {
            throw new Error(data.error || 'Failed to load changes');
        }
    } catch (error) {
        console.error('Error loading changes:', error);
        showError('Failed to load change history: ' + error.message);
        
        // Hide loading state and show error
        document.getElementById('loading-state').style.display = 'none';
        document.getElementById('empty-state').style.display = 'block';
        document.getElementById('empty-state').innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <h5>Error Loading Changes</h5>
            <p class="mb-0">${error.message}</p>
            <button class="btn btn-primary mt-3" onclick="location.reload()">
                <i class="fas fa-refresh me-2"></i>Retry
            </button>
        `;
    }
}

function displayChanges(changes) {
    const tbody = document.getElementById('changes-tbody');
    const emptyState = document.getElementById('empty-state');
    const loadingState = document.getElementById('loading-state');
    
    loadingState.style.display = 'none';
    
    if (changes.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    tbody.innerHTML = changes.map(change => `
        <tr class="change-row ${change.status === 'reverted' ? 'table-secondary' : ''}">
            <td>
                <input type="checkbox" class="change-checkbox" value="${change.id}" 
                       ${change.status === 'reverted' ? 'disabled' : ''}
                       onchange="handleCheckboxChange(this)">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-${getResourceIcon(change.resource_type)} me-2 text-primary"></i>
                    <div>
                        <strong>${escapeHtml(change.resource_type)}</strong>
                        <br>
                        <small class="text-muted">ID: ${change.resource_id}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-info action-badge">
                    ${formatAction(change.action)}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-link" onclick="showChangeDetails(${change.id})">
                    <i class="fas fa-eye me-1"></i>View Details
                </button>
            </td>
            <td>
                ${change.status === 'completed' ? 
                    '<span class="badge bg-success">Active</span>' : 
                    '<span class="badge bg-secondary">Reverted</span>'}
            </td>
            <td>
                <small class="time-ago">${formatTimeAgo(change.created_at)}</small>
            </td>
            <td>
                ${change.status === 'completed' ? 
                    `<button class="btn btn-sm btn-outline-warning" onclick="revertChange(${change.id})">
                        <i class="fas fa-undo"></i>
                    </button>` : 
                    '<span class="text-muted">-</span>'}
            </td>
        </tr>
    `).join('');
}

function updateStats() {
    const totalChanges = allChanges.length;
    const activeChanges = allChanges.filter(c => c.status === 'completed').length;
    const revertedChanges = allChanges.filter(c => c.status === 'reverted').length;
    const affectedProducts = new Set(allChanges.filter(c => c.resource_type === 'product').map(c => c.resource_id)).size;
    
    document.getElementById('total-changes').textContent = totalChanges;
    document.getElementById('active-changes').textContent = activeChanges;
    document.getElementById('reverted-changes').textContent = revertedChanges;
    document.getElementById('affected-products').textContent = affectedProducts;
}

function getResourceIcon(type) {
    const icons = {
        'product': 'box',
        'collection': 'folder',
        'homepage': 'home',
        'page': 'file-alt'
    };
    return icons[type] || 'file';
}

function formatAction(action) {
    const formatted = action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    return formatted;
}

function formatTimeAgo(dateString) {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
}

function handleCheckboxChange(checkbox) {
    if (checkbox.checked) {
        selectedChanges.add(checkbox.value);
    } else {
        selectedChanges.delete(checkbox.value);
    }
    updateRevertButton();
}

function updateRevertButton() {
    const btn = document.getElementById('revert-selected-btn');
    btn.disabled = selectedChanges.size === 0;
    btn.textContent = selectedChanges.size > 0 ? 
        `Revert Selected (${selectedChanges.size})` : 
        'Revert Selected';
}

async function revertChange(changeId) {
    if (!confirm('Are you sure you want to revert this change? This will restore the previous state.')) {
        return;
    }
    
    try {
        const response = await fetch(`/ai/api/revert-change/${changeId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            showSuccess('Change reverted successfully');
            loadChanges();
        } else {
            showError(data.error || 'Failed to revert change');
        }
    } catch (error) {
        showError('Error reverting change');
    }
}

async function revertSelected() {
    if (selectedChanges.size === 0) return;
    
    if (!confirm(`Are you sure you want to revert ${selectedChanges.size} selected changes?`)) {
        return;
    }
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const changeId of selectedChanges) {
        try {
            const response = await fetch(`/ai/api/revert-change/${changeId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                successCount++;
            } else {
                errorCount++;
            }
        } catch (error) {
            errorCount++;
        }
    }
    
    if (successCount > 0) {
        showSuccess(`Successfully reverted ${successCount} changes`);
    }
    if (errorCount > 0) {
        showError(`Failed to revert ${errorCount} changes`);
    }
    
    selectedChanges.clear();
    loadChanges();
}

function showChangeDetails(changeId) {
    const change = allChanges.find(c => c.id === changeId);
    if (!change) return;
    
    const modal = new bootstrap.Modal(document.getElementById('changeDetailsModal'));
    const content = document.getElementById('change-details-content');
    
    content.innerHTML = `
        <h6>Resource Information</h6>
        <table class="table table-sm">
            <tr>
                <th width="150">Type:</th>
                <td>${escapeHtml(change.resource_type)}</td>
            </tr>
            <tr>
                <th>ID:</th>
                <td>${change.resource_id}</td>
            </tr>
            <tr>
                <th>Action:</th>
                <td>${formatAction(change.action)}</td>
            </tr>
            <tr>
                <th>Date:</th>
                <td>${new Date(change.created_at).toLocaleString()}</td>
            </tr>
            <tr>
                <th>Status:</th>
                <td>${change.status === 'completed' ? 
                    '<span class="badge bg-success">Active</span>' : 
                    '<span class="badge bg-secondary">Reverted</span>'}</td>
            </tr>
        </table>
        
        ${change.before_data && Object.keys(change.before_data).length > 0 ? `
            <h6 class="mt-4">Before</h6>
            <div class="bg-light p-3 rounded">
                <pre class="mb-0">${JSON.stringify(change.before_data, null, 2)}</pre>
            </div>
        ` : ''}
        
        ${change.after_data && Object.keys(change.after_data).length > 0 ? `
            <h6 class="mt-4">After</h6>
            <div class="bg-light p-3 rounded">
                <pre class="mb-0">${JSON.stringify(change.after_data, null, 2)}</pre>
            </div>
        ` : ''}
        
        ${change.metadata && Object.keys(change.metadata).length > 0 ? `
            <h6 class="mt-4">Additional Information</h6>
            <div class="bg-light p-3 rounded">
                <pre class="mb-0">${JSON.stringify(change.metadata, null, 2)}</pre>
            </div>
        ` : ''}
    `;
    
    modal.show();
}

function applyFilters() {
    const resourceType = document.getElementById('filter-resource-type').value;
    const status = document.getElementById('filter-status').value;
    const action = document.getElementById('filter-action').value;
    
    let filteredChanges = allChanges;
    
    if (resourceType) {
        filteredChanges = filteredChanges.filter(c => c.resource_type === resourceType);
    }
    if (status) {
        filteredChanges = filteredChanges.filter(c => c.status === status);
    }
    if (action) {
        filteredChanges = filteredChanges.filter(c => c.action === action);
    }
    
    displayChanges(filteredChanges);
}

async function resetAllChanges() {
    const confirmation = document.getElementById('reset-confirmation').value;
    
    if (confirmation !== 'YES RESET EVERYTHING I FUCKED UP') {
        showError('Confirmation text does not match. Please type exactly: YES RESET EVERYTHING I FUCKED UP');
        return;
    }
    
    if (!confirm('This will reset ALL changes and cannot be undone. Are you absolutely sure?')) {
        return;
    }
    
    try {
        const response = await fetch('/ai/api/reset-all-changes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                confirmation: confirmation
            })
        });
        
        const data = await response.json();
        if (data.success) {
            showSuccess(data.message);
            document.getElementById('reset-confirmation').value = '';
            setTimeout(() => {
                loadChanges();
            }, 1500);
        } else {
            showError(data.error || 'Failed to reset changes');
        }
    } catch (error) {
        showError('Error resetting changes');
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showSuccess(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}

function showError(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}
</script>
{% endblock %}