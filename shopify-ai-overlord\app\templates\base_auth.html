<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Shopify AI Control{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar bg-dark text-white p-3">
            <div class="mb-4">
                <h4 class="text-center">Shopify AI Control</h4>
                {% if session.get('current_shop_name') %}
                <p class="text-center small text-muted">{{ session.get('current_shop_name') }}</p>
                {% endif %}
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item mb-2">
                    <a class="nav-link text-white" href="{{ url_for('main.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i> Main Dashboard
                    </a>
                </li>
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white" href="{{ url_for('ai_modules.changes') }}">
                        <i class="fas fa-history me-2"></i> Change History
                    </a>
                </li>
                
                <li class="nav-item mb-2">
                    <span class="text-muted small">AI MODULES</span>
                </li>
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white d-flex justify-content-between align-items-center" 
                       data-bs-toggle="collapse" href="#seoSubmenu" role="button" aria-expanded="true">
                        <span><i class="fas fa-search me-2"></i> SEO</span>
                        {% if current_user.seo_enabled %}
                        <span class="badge bg-success">Active</span>
                        {% endif %}
                    </a>
                    <div class="collapse show" id="seoSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i> SEO Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_products') }}">
                                    <i class="fas fa-box me-1"></i> Product SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_collections') }}">
                                    <i class="fas fa-layer-group me-1"></i> Collection SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_languages') }}">
                                    <i class="fas fa-language me-1"></i> Multi-Language SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_homepage') }}">
                                    <i class="fas fa-home me-1"></i> Homepage SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_technical') }}">
                                    <i class="fas fa-cog me-1"></i> Technical SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.seo_search_console') }}">
                                    <i class="fas fa-search me-1"></i> Search Console
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white d-flex justify-content-between align-items-center" 
                       data-bs-toggle="collapse" href="#adsSubmenu" role="button" aria-expanded="true">
                        <span><i class="fas fa-ad me-2"></i> Ads</span>
                        {% if current_user.ads_enabled %}
                        <span class="badge bg-success">Active</span>
                        {% endif %}
                    </a>
                    <div class="collapse show" id="adsSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.ads_google') }}">
                                    <i class="fab fa-google me-1"></i> Google Ads
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.ads_facebook') }}">
                                    <i class="fab fa-facebook me-1"></i> Facebook/Meta Ads
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.ads_tiktok') }}">
                                    <i class="fab fa-tiktok me-1"></i> TikTok Ads
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.ads_analytics') }}">
                                    <i class="fas fa-chart-line me-1"></i> Ad Analytics
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white d-flex justify-content-between align-items-center" 
                       data-bs-toggle="collapse" href="#emailSubmenu" role="button" aria-expanded="true">
                        <span><i class="fas fa-envelope me-2"></i> Email Marketing</span>
                        {% if current_user.email_marketing_enabled %}
                        <span class="badge bg-success">Active</span>
                        {% endif %}
                    </a>
                    <div class="collapse show" id="emailSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.email_campaigns') }}">
                                    <i class="fas fa-paper-plane me-1"></i> Campaigns
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.email_automation') }}">
                                    <i class="fas fa-robot me-1"></i> Automation Flows
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.email_segments') }}">
                                    <i class="fas fa-users me-1"></i> Segmentation
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.email_templates') }}">
                                    <i class="fas fa-file-alt me-1"></i> Templates
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white d-flex justify-content-between align-items-center" 
                       data-bs-toggle="collapse" href="#supportSubmenu" role="button" aria-expanded="true">
                        <span><i class="fas fa-headset me-2"></i> Customer Support</span>
                        {% if current_user.customer_support_enabled %}
                        <span class="badge bg-success">Active</span>
                        {% endif %}
                    </a>
                    <div class="collapse show" id="supportSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.support_chatbot') }}">
                                    <i class="fas fa-comment-dots me-1"></i> AI Chatbot
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.support_tickets') }}">
                                    <i class="fas fa-ticket-alt me-1"></i> Ticket Management
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.support_faqs') }}">
                                    <i class="fas fa-question-circle me-1"></i> FAQs & Knowledge Base
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white-50 small" href="{{ url_for('ai_modules.support_insights') }}">
                                    <i class="fas fa-lightbulb me-1"></i> Customer Insights
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item mt-4">
                    <span class="text-muted small">ACCOUNT</span>
                </li>
                
                
                <li class="nav-item mb-2">
                    <a class="nav-link text-white" href="{{ url_for('auth.settings') }}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link text-white" href="{{ url_for('auth.logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- Main Content -->
        <main class="flex-grow-1 p-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>