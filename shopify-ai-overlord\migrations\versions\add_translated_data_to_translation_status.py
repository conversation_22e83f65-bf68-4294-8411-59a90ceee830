"""Add translated_data to TranslationStatus

Revision ID: add_translated_data
Revises: 43461d7823a4
Create Date: 2025-05-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_translated_data'
down_revision = '43461d7823a4'
branch_labels = None
depends_on = None


def upgrade():
    # Add translated_data column to store translated content including handles
    op.add_column('translation_status', 
        sa.Column('translated_data', sa.JSON(), nullable=True)
    )


def downgrade():
    op.drop_column('translation_status', 'translated_data')