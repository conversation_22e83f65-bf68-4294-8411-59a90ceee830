#!/usr/bin/env python3
"""
Demo script showing how quotas are dynamically calculated for each store
"""

import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up environment variables
os.environ['TARGET_COMPLETION_DAYS'] = '90'
os.environ['MIN_PRODUCTS_PER_DAY'] = '3'
os.environ['MAX_PRODUCTS_PER_DAY'] = '60'
os.environ['COLLS_PER_PRODUCT'] = '1.5'
os.environ['MAX_COLLECTIONS_PER_DAY'] = '20'

from app import create_app
from app.services.quota_service import calculate_daily_limits

print("Dynamic Quota Calculation Demo")
print("=" * 80)
print("This shows how EACH STORE gets its own quotas based on its actual product count")
print("=" * 80)

# Simulate different stores
stores = [
    {"name": "Small Boutique", "products": 80},
    {"name": "Growing Brand", "products": 350},
    {"name": "Medium Store", "products": 1200},
    {"name": "Large Retailer", "products": 5000},
    {"name": "Enterprise Store", "products": 10000},
]

app = create_app()

with app.app_context():
    for store in stores:
        print(f"\n{store['name']} - {store['products']:,} products")
        print("-" * 50)
        
        # This is what happens for EACH store - using their actual product count
        products_per_day, collections_per_day = calculate_daily_limits(store['products'])
        days_to_complete = store['products'] // products_per_day + (1 if store['products'] % products_per_day else 0)
        
        print(f"  Daily Product Quota: {products_per_day}")
        print(f"  Daily Collection Quota: {collections_per_day}")
        print(f"  Days to Complete: {days_to_complete}")
        print(f"  Completion Rate: ~{products_per_day / store['products'] * 100:.1f}% per day")
        
        # Show the calculation
        print(f"\n  Calculation:")
        print(f"    {store['products']} products ÷ 90 days = {store['products']/90:.1f} products/day")
        print(f"    Adjusted to limits: min {os.environ['MIN_PRODUCTS_PER_DAY']}, max {os.environ['MAX_PRODUCTS_PER_DAY']}")
        print(f"    Final: {products_per_day} products/day")

print("\n" + "=" * 80)
print("Key Points:")
print("- Each store's quota is calculated from its ACTUAL product count")
print("- No hardcoded quotas - everything is dynamic")
print("- Jobs are spaced 10 minutes apart to prevent API overload")
print("- Translations happen 5 minutes after each optimization")
print("=" * 80)