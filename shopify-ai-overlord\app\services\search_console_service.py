"""
Google Search Console API Service
Handles all interactions with the Search Console API
"""
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from urllib.parse import quote
from flask import current_app
from app.models import GoogleOAuth
from app import db


class SearchConsoleService:
    """Service for interacting with Google Search Console API"""
    
    BASE_URL = "https://www.googleapis.com/webmasters/v3"
    INSPECTION_URL = "https://searchconsole.googleapis.com/v1"
    
    def __init__(self, shop_id: int):
        """Initialize with shop_id to get OAuth tokens"""
        self.shop_id = shop_id
        self.oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
        
        if not self.oauth:
            raise ValueError(f"No Google OAuth connection found for shop {shop_id}")
        
        if self.oauth.is_token_expired():
            self._refresh_token()
        
        # Use the stored Search Console property if available
        self.default_property = self.oauth.search_console_property
    
    def _refresh_token(self):
        """Refresh the OAuth access token"""
        if not self.oauth.refresh_token:
            raise ValueError("No refresh token available")
        
        token_url = "https://oauth2.googleapis.com/token"
        data = {
            'client_id': current_app.config['GOOGLE_CLIENT_ID'],
            'client_secret': current_app.config['GOOGLE_CLIENT_SECRET'],
            'refresh_token': self.oauth.refresh_token,
            'grant_type': 'refresh_token'
        }
        
        response = requests.post(token_url, data=data)
        response.raise_for_status()
        
        tokens = response.json()
        self.oauth.access_token = tokens['access_token']
        
        # Update expiration
        expires_in = tokens.get('expires_in', 3600)
        self.oauth.expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
        
        db.session.commit()
    
    def _get_headers(self):
        """Get authorization headers"""
        return {
            'Authorization': f'Bearer {self.oauth.access_token}',
            'Content-Type': 'application/json'
        }
    
    def _encode_site_url(self, site_url: str) -> str:
        """Encode site URL for API calls"""
        return quote(site_url, safe='')
    
    def get_default_property(self) -> Optional[str]:
        """Get the default Search Console property for this shop"""
        if self.default_property:
            return self.default_property
        
        # Try to get from user settings
        from app.auth import get_current_user_settings
        settings = get_current_user_settings()
        if settings and settings.store_actual_live_url:
            # Check if this URL exists in Search Console
            try:
                sites = self.list_sites()
                store_url = settings.store_actual_live_url.rstrip('/')
                for site in sites:
                    site_url = site.get('siteUrl', '').rstrip('/')
                    if site_url == store_url or site_url == f"{store_url}/":
                        # Save it for future use
                        self.oauth.search_console_property = site['siteUrl']
                        db.session.commit()
                        return site['siteUrl']
            except:
                pass
        
        return None
    
    def list_sites(self) -> List[Dict[str, Any]]:
        """List all sites in the Search Console account"""
        url = f"{self.BASE_URL}/sites"
        response = requests.get(url, headers=self._get_headers())
        response.raise_for_status()
        
        data = response.json()
        return data.get('siteEntry', [])
    
    def get_site(self, site_url: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific site"""
        encoded_url = self._encode_site_url(site_url)
        url = f"{self.BASE_URL}/sites/{encoded_url}"
        
        try:
            response = requests.get(url, headers=self._get_headers())
            response.raise_for_status()
            return response.json()
        except requests.HTTPError as e:
            if e.response.status_code == 404:
                return None
            raise
    
    def add_site(self, site_url: str) -> bool:
        """Add a site to Search Console"""
        encoded_url = self._encode_site_url(site_url)
        url = f"{self.BASE_URL}/sites/{encoded_url}"
        
        response = requests.put(url, headers=self._get_headers())
        return response.status_code in [200, 204]
    
    def query_search_analytics(
        self,
        site_url: str,
        start_date: datetime,
        end_date: datetime,
        dimensions: Optional[List[str]] = None,
        dimension_filter_groups: Optional[List[Dict]] = None,
        search_type: str = 'web',
        row_limit: int = 1000,
        start_row: int = 0
    ) -> Dict[str, Any]:
        """
        Query search analytics data
        
        Args:
            site_url: The site URL to query
            start_date: Start date for the query
            end_date: End date for the query
            dimensions: List of dimensions to group by (e.g., ['query', 'page', 'country', 'device'])
            dimension_filter_groups: Filters for dimensions
            search_type: Type of search results ('web', 'image', 'video', 'discover', 'news')
            row_limit: Maximum number of rows to return (max 25000)
            start_row: Starting row (for pagination)
        
        Returns:
            Dictionary with 'rows' containing the analytics data
        """
        encoded_url = self._encode_site_url(site_url)
        url = f"{self.BASE_URL}/sites/{encoded_url}/searchAnalytics/query"
        
        # Prepare request body
        body = {
            'startDate': start_date.strftime('%Y-%m-%d'),
            'endDate': end_date.strftime('%Y-%m-%d'),
            'searchType': search_type,
            'rowLimit': min(row_limit, 25000),
            'startRow': start_row
        }
        
        if dimensions:
            body['dimensions'] = dimensions
        
        if dimension_filter_groups:
            body['dimensionFilterGroups'] = dimension_filter_groups
        
        response = requests.post(url, json=body, headers=self._get_headers())
        response.raise_for_status()
        
        return response.json()
    
    def get_performance_by_page(
        self,
        site_url: str,
        page_url: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get performance data for a specific page"""
        dimension_filter_groups = [{
            'filters': [{
                'dimension': 'page',
                'operator': 'equals',
                'expression': page_url
            }]
        }]
        
        return self.query_search_analytics(
            site_url=site_url,
            start_date=start_date,
            end_date=end_date,
            dimensions=['date'],
            dimension_filter_groups=dimension_filter_groups
        )
    
    def get_top_queries(
        self,
        site_url: str,
        start_date: datetime,
        end_date: datetime,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get top search queries for the site"""
        result = self.query_search_analytics(
            site_url=site_url,
            start_date=start_date,
            end_date=end_date,
            dimensions=['query'],
            row_limit=limit
        )
        
        return result.get('rows', [])
    
    def inspect_url(self, inspection_url: str, site_url: str) -> Dict[str, Any]:
        """
        Inspect a URL's index status
        
        Args:
            inspection_url: The full URL to inspect
            site_url: The Search Console property URL
        
        Returns:
            Dictionary containing inspection results
        """
        url = f"{self.INSPECTION_URL}/urlInspection/index:inspect"
        
        body = {
            'inspectionUrl': inspection_url,
            'siteUrl': site_url,
            'languageCode': 'en-US'
        }
        
        response = requests.post(url, json=body, headers=self._get_headers())
        response.raise_for_status()
        
        return response.json()
    
    def list_sitemaps(self, site_url: str) -> List[Dict[str, Any]]:
        """List all sitemaps for a site"""
        encoded_url = self._encode_site_url(site_url)
        url = f"{self.BASE_URL}/sites/{encoded_url}/sitemaps"
        
        response = requests.get(url, headers=self._get_headers())
        response.raise_for_status()
        
        data = response.json()
        return data.get('sitemap', [])
    
    def submit_sitemap(self, site_url: str, sitemap_url: str) -> bool:
        """Submit a sitemap to Google"""
        encoded_site_url = self._encode_site_url(site_url)
        encoded_sitemap_url = self._encode_site_url(sitemap_url)
        url = f"{self.BASE_URL}/sites/{encoded_site_url}/sitemaps/{encoded_sitemap_url}"
        
        try:
            response = requests.put(url, headers=self._get_headers())
            if response.status_code not in [200, 204]:
                print(f"Sitemap submission failed: {response.status_code} - {response.text}")
            return response.status_code in [200, 204]
        except Exception as e:
            print(f"Error submitting sitemap: {e}")
            return False