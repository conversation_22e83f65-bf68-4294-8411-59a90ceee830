from flask import Blueprint, redirect, url_for, request, session, flash, current_app
from urllib.parse import urlencode
import requests
import secrets
from datetime import datetime, timedelta
from app import db
from app.models import GoogleOAuth
from app.auth import login_required, get_current_shop

bp = Blueprint('google_oauth', __name__, url_prefix='/api/auth')


@bp.route('/google')
@login_required
def google_auth():
    """Initiate Google OAuth flow"""
    # Use the same shop resolution as SEO dashboard
    from app.routes.ai_modules import get_current_user_id
    
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    # Generate state for CSRF protection
    state = secrets.token_urlsafe(32)
    session['oauth_state'] = state
    session['oauth_shop_id'] = shop_id
    session.permanent = True  # Ensure session persists
    session.modified = True  # Force session save
    
    # Build authorization URL
    params = {
        'client_id': current_app.config['GOOGLE_CLIENT_ID'],
        'redirect_uri': current_app.config['GOOGLE_OAUTH_REDIRECT_URI'],
        'response_type': 'code',
        'scope': 'https://www.googleapis.com/auth/webmasters email',  # Full access, not readonly
        'access_type': 'offline',
        'prompt': 'consent',  # Force consent to ensure we get refresh token
        'state': state,
        'include_granted_scopes': 'false'  # Don't include previously granted scopes
    }
    
    auth_url = 'https://accounts.google.com/o/oauth2/v2/auth?' + urlencode(params)
    return redirect(auth_url)


@bp.route('/callback/google')
@login_required
def google_callback():
    """Handle Google OAuth callback"""
    # Verify state
    state = request.args.get('state')
    stored_state = session.get('oauth_state')
    
    # Debug logging
    print(f"[OAuth Debug] Received state: {state}")
    print(f"[OAuth Debug] Stored state: {stored_state}")
    print(f"[OAuth Debug] Session data: {dict(session)}")
    
    if not state or not stored_state or state != stored_state:
        flash('Invalid OAuth state. Please try connecting again.', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    # Check for errors
    error = request.args.get('error')
    if error:
        flash(f'Google OAuth error: {error}', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    # Get authorization code
    code = request.args.get('code')
    if not code:
        flash('No authorization code received', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    shop_id = session.get('oauth_shop_id')
    if not shop_id:
        # Try to get from current shop as fallback
        from app.routes.ai_modules import get_current_user_id
        shop_id = get_current_user_id()
        if not shop_id:
            flash('Shop ID lost during OAuth flow', 'error')
            return redirect(url_for('ai_modules.seo_search_console'))
    
    # Exchange code for tokens
    token_url = 'https://oauth2.googleapis.com/token'
    token_data = {
        'client_id': current_app.config['GOOGLE_CLIENT_ID'],
        'client_secret': current_app.config['GOOGLE_CLIENT_SECRET'],
        'code': code,
        'redirect_uri': current_app.config['GOOGLE_OAUTH_REDIRECT_URI'],
        'grant_type': 'authorization_code'
    }
    
    try:
        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        tokens = token_response.json()
    except requests.RequestException as e:
        flash(f'Failed to exchange code for tokens: {str(e)}', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    # Get user info
    user_info_url = 'https://www.googleapis.com/oauth2/v2/userinfo'
    headers = {'Authorization': f"Bearer {tokens['access_token']}"}
    
    try:
        user_response = requests.get(user_info_url, headers=headers)
        user_response.raise_for_status()
        user_info = user_response.json()
    except requests.RequestException as e:
        flash(f'Failed to get user info: {str(e)}', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    # Calculate token expiration
    expires_in = tokens.get('expires_in', 3600)
    expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
    
    # Store or update OAuth tokens
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
    if not google_oauth:
        google_oauth = GoogleOAuth(shop_id=shop_id)
    
    google_oauth.access_token = tokens['access_token']
    google_oauth.refresh_token = tokens.get('refresh_token', google_oauth.refresh_token)
    google_oauth.token_type = tokens.get('token_type', 'Bearer')
    google_oauth.expires_at = expires_at
    google_oauth.google_email = user_info.get('email')
    google_oauth.google_id = user_info.get('id')
    google_oauth.scopes = tokens.get('scope', '').split() if tokens.get('scope') else []
    
    # Try to auto-select Search Console property based on store_actual_live_url
    from app.auth import get_current_user_settings
    from app.services.search_console_service import SearchConsoleService
    
    user_settings = get_current_user_settings()
    if user_settings and user_settings.store_actual_live_url:
        try:
            # Save first to get the access token
            db.session.add(google_oauth)
            db.session.commit()
            
            # Initialize Search Console service
            service = SearchConsoleService(shop_id)
            
            # List all sites
            sites = service.list_sites()
            
            # Look for matching site
            store_url = user_settings.store_actual_live_url.rstrip('/')
            for site in sites:
                site_url = site.get('siteUrl', '').rstrip('/')
                if site_url == store_url or site_url == f"{store_url}/":
                    google_oauth.search_console_property = site['siteUrl']
                    db.session.commit()
                    flash(f'Automatically connected to Search Console property: {site["siteUrl"]}', 'info')
                    break
            else:
                # No matching site found
                flash(f'Note: Your store URL {store_url} was not found in Search Console. You may need to add it.', 'warning')
                
        except Exception as e:
            print(f"[OAuth] Error auto-selecting Search Console property: {e}")
            # Continue anyway - user can manually select later
    else:
        db.session.add(google_oauth)
        db.session.commit()
    
    # Clean up session
    session.pop('oauth_state', None)
    session.pop('oauth_shop_id', None)
    
    # Queue initial metrics fetch
    from app.tasks import fetch_search_console_metrics_task
    try:
        result = fetch_search_console_metrics_task.apply_async(
            args=[shop_id, True]  # True for initial fetch
        )
        flash(f'Successfully connected Google account: {google_oauth.google_email}. Fetching initial metrics...', 'success')
    except Exception as e:
        print(f"Error queuing initial metrics fetch: {e}")
        flash(f'Successfully connected Google account: {google_oauth.google_email}', 'success')
    
    return redirect(url_for('ai_modules.seo_search_console'))


@bp.route('/google/disconnect', methods=['POST'])
@login_required
def google_disconnect():
    """Disconnect Google OAuth for current shop"""
    # Use the same shop resolution as SEO dashboard
    from app.routes.ai_modules import get_current_user_id
    
    shop_id = get_current_user_id()
    if not shop_id:
        flash('Shop configuration not found. Please check your settings.', 'error')
        return redirect(url_for('ai_modules.seo_search_console'))
    
    google_oauth = GoogleOAuth.query.filter_by(shop_id=shop_id).first()
    if google_oauth:
        db.session.delete(google_oauth)
        db.session.commit()
        # Force database to expire all cached instances
        db.session.expire_all()
        flash('Google account disconnected successfully', 'success')
    else:
        flash('No Google account connected', 'info')
    
    # Redirect with a cache-busting parameter to force refresh
    from flask import request
    import time
    return redirect(url_for('ai_modules.seo_search_console') + f'?t={int(time.time())}')