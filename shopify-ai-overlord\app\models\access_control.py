from datetime import datetime, time
import uuid
from app import db


class AccessKey(db.Model):
    __tablename__ = 'access_keys'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Relationships
    user_settings = db.relationship('UserSettings', backref='access_key', uselist=False, cascade='all, delete-orphan')
    user_shops = db.relationship('UserShop', backref='access_key', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<AccessKey {self.code}>'


class UserSettings(db.Model):
    __tablename__ = 'user_settings'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    access_key_id = db.Column(db.String(36), db.ForeignKey('access_keys.id'), unique=True, nullable=False)
    
    # API Keys
    shopify_api_key = db.Column(db.String(255), nullable=True)
    shopify_api_secret = db.Column(db.String(255), nullable=True)
    shopify_access_token = db.Column(db.String(255), nullable=True)
    shopify_shop_domain = db.Column(db.String(255), nullable=True)
    anthropic_api_key = db.Column(db.String(255), nullable=True)
    openai_api_key = db.Column(db.String(255), nullable=True)
    
    # Store's actual live URL (for Search Console matching)
    store_actual_live_url = db.Column(db.String(255), nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<UserSettings for {self.access_key_id}>'


class UserShop(db.Model):
    """Links users to multiple shops they can manage"""
    __tablename__ = 'user_shops'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    access_key_id = db.Column(db.String(36), db.ForeignKey('access_keys.id'), nullable=False)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False)
    
    # Store-specific automation settings
    automation_hour = db.Column(db.Integer, default=2, nullable=False)  # Hour (0-23) when automation runs
    automation_minute = db.Column(db.Integer, default=0, nullable=False)  # Minute when automation runs
    automation_enabled = db.Column(db.Boolean, default=True, nullable=False)
    
    # Current selection
    is_current = db.Column(db.Boolean, default=False, nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    shop = db.relationship('Shop', backref='user_shops')
    
    # Unique constraint to prevent duplicate shop access for same user
    __table_args__ = (db.UniqueConstraint('access_key_id', 'shop_id', name='unique_user_shop'),)
    
    def __repr__(self):
        return f'<UserShop {self.access_key_id} -> {self.shop_id}>'
    
    @property
    def automation_time(self):
        """Get automation time as time object"""
        return time(self.automation_hour, self.automation_minute)
    
    def set_automation_time(self, hour, minute):
        """Set automation time"""
        self.automation_hour = max(0, min(23, hour))
        self.automation_minute = max(0, min(59, minute))
    
    @classmethod
    def get_current_shop(cls, access_key_id):
        """Get the currently selected shop for a user"""
        return cls.query.filter_by(
            access_key_id=access_key_id,
            is_current=True
        ).first()
    
    @classmethod
    def set_current_shop(cls, access_key_id, shop_id):
        """Set the current shop for a user"""
        # Clear all current flags for this user
        cls.query.filter_by(access_key_id=access_key_id).update(
            {'is_current': False}
        )
        
        # Set the specified shop as current
        user_shop = cls.query.filter_by(
            access_key_id=access_key_id,
            shop_id=shop_id
        ).first()
        
        if user_shop:
            user_shop.is_current = True
            return user_shop
        return None