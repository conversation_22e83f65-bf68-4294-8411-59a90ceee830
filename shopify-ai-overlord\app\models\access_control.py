from datetime import datetime, time
import uuid
from app import db


class AccessKey(db.Model):
    __tablename__ = 'access_keys'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Relationships
    user_settings = db.relationship('UserSettings', backref='access_key', uselist=False, cascade='all, delete-orphan')
    user_shops = db.relationship('UserShop', backref='access_key', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<AccessKey {self.code}>'


class UserSettings(db.Model):
    __tablename__ = 'user_settings'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    access_key_id = db.Column(db.String(36), db.ForeignKey('access_keys.id'), unique=True, nullable=False)
    
    # API Keys
    shopify_api_key = db.Column(db.String(255), nullable=True)
    shopify_api_secret = db.Column(db.String(255), nullable=True)
    shopify_access_token = db.Column(db.String(255), nullable=True)
    shopify_shop_domain = db.Column(db.String(255), nullable=True)
    anthropic_api_key = db.Column(db.String(255), nullable=True)
    openai_api_key = db.Column(db.String(255), nullable=True)
    
    # Store's actual live URL (for Search Console matching)
    store_actual_live_url = db.Column(db.String(255), nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<UserSettings for {self.access_key_id}>'


class UserShop(db.Model):
    """Links users to multiple shops they can manage"""
    __tablename__ = 'user_shops'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    access_key_id = db.Column(db.String(36), db.ForeignKey('access_keys.id'), nullable=False)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False)
    
    # Store-specific automation settings
    automation_hour = db.Column(db.Integer, default=2, nullable=False)  # Hour (0-23) when automation runs
    automation_minute = db.Column(db.Integer, default=0, nullable=False)  # Minute when automation runs
    automation_enabled = db.Column(db.Boolean, default=True, nullable=False)
    
    # Current selection
    is_current = db.Column(db.Boolean, default=False, nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    shop = db.relationship('Shop', backref='user_shops')
    
    # Unique constraint to prevent duplicate shop access for same user
    __table_args__ = (db.UniqueConstraint('access_key_id', 'shop_id', name='unique_user_shop'),)
    
    def __repr__(self):
        return f'<UserShop {self.access_key_id} -> {self.shop_id}>'
    
    @property
    def automation_time(self):
        """Get automation time as time object"""
        return time(self.automation_hour, self.automation_minute)
    
    def set_automation_time(self, hour, minute):
        """Set automation time"""
        self.automation_hour = max(0, min(23, hour))
        self.automation_minute = max(0, min(59, minute))

    @classmethod
    def assign_next_automation_slot(cls):
        """Assign the next available 20-minute automation slot"""
        # Get all existing user shops ordered by their automation time
        existing_shops = cls.query.order_by(
            cls.automation_hour,
            cls.automation_minute
        ).all()

        # Create a set of used time slots (in minutes from midnight)
        used_slots = set()
        for shop in existing_shops:
            slot_minutes = shop.automation_hour * 60 + shop.automation_minute
            used_slots.add(slot_minutes)

        # Start at 4:00 AM (240 minutes) to avoid conflicts with system tasks
        # System tasks: cleanup at 1:00 AM, search console at 3:00 AM
        start_time = 4 * 60  # 4:00 AM in minutes
        interval = 20  # 20-minute intervals

        # Find the next available slot
        current_slot = start_time
        while current_slot < 24 * 60:  # Don't go past midnight
            if current_slot not in used_slots:
                hour = current_slot // 60
                minute = current_slot % 60
                return hour, minute
            current_slot += interval

        # If we've filled all slots in a day, start wrapping around
        # This handles the case where we have more than 60 shops (20min * 60 = 20 hours)
        current_slot = start_time
        while True:
            if current_slot not in used_slots:
                hour = (current_slot // 60) % 24
                minute = current_slot % 60
                return hour, minute
            current_slot += interval
    
    @classmethod
    def get_current_shop(cls, access_key_id):
        """Get the currently selected shop for a user"""
        return cls.query.filter_by(
            access_key_id=access_key_id,
            is_current=True
        ).first()

    @classmethod
    def redistribute_all_schedules(cls):
        """Redistribute all shops to proper 20-minute intervals"""
        from app.models.ai_config import AIConfig

        # Get all shops with enabled automations, ordered by shop_id for consistency
        shops_with_automation = []
        all_shops = cls.query.order_by(cls.shop_id).all()

        for user_shop in all_shops:
            config = AIConfig.query.filter_by(
                shop_id=user_shop.shop_id,
                module_type='seo_automation'
            ).first()

            if config and config.config_data:
                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])

                if has_enabled_automation:
                    shops_with_automation.append(user_shop)

        # Redistribute with 20-minute intervals starting at 4:00 AM
        start_time = 4 * 60  # 4:00 AM in minutes
        interval = 20  # 20-minute intervals

        for i, user_shop in enumerate(shops_with_automation):
            slot_minutes = start_time + (i * interval)
            new_hour = (slot_minutes // 60) % 24
            new_minute = slot_minutes % 60

            user_shop.automation_hour = new_hour
            user_shop.automation_minute = new_minute

        return len(shops_with_automation)
    
    @classmethod
    def set_current_shop(cls, access_key_id, shop_id):
        """Set the current shop for a user"""
        # Clear all current flags for this user
        cls.query.filter_by(access_key_id=access_key_id).update(
            {'is_current': False}
        )
        
        # Set the specified shop as current
        user_shop = cls.query.filter_by(
            access_key_id=access_key_id,
            shop_id=shop_id
        ).first()
        
        if user_shop:
            user_shop.is_current = True
            return user_shop
        return None