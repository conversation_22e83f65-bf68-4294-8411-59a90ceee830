# Git and version control
.git
.gitignore

# Environment files
.env
.env.*
!.env.docker

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite

# Celery
celerybeat-schedule*

# Temporary files
instance/
*.tmp
*.temp

# Docker files (don't copy into container)
Dockerfile
docker-compose*.yml
.dockerignore

# Documentation (not needed in container)
*.md
!README.md