#!/usr/bin/env python3
"""
Check Automation Schedules

This script displays the current automation schedules for all shops
and identifies any potential conflicts or issues.
"""

import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.access_control import UserShop
from app.models.ai_config import AIConfig
from celerybeat_schedule import get_dynamic_schedule
import time
from datetime import datetime, timedelta

def check_schedules():
    """Check and display current automation schedules"""
    print(f"\n{'='*80}")
    print("AUTOMATION SCHEDULE STATUS")
    print(f"Current time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*80}\n")
    
    app = create_app()
    
    with app.app_context():
        # Get all user shops
        all_shops = UserShop.query.order_by(UserShop.shop_id).all()
        enabled_shops = UserShop.query.filter_by(automation_enabled=True).order_by(
            UserShop.automation_hour, 
            UserShop.automation_minute
        ).all()
        
        print(f"Total shops: {len(all_shops)}")
        print(f"Automation enabled: {len(enabled_shops)}")
        
        # Check which shops have automation configurations
        shops_with_config = []
        shops_with_enabled_features = []
        
        for shop in enabled_shops:
            config = AIConfig.query.filter_by(
                shop_id=shop.shop_id,
                module_type='seo_automation'
            ).first()
            
            if config and config.config_data:
                shops_with_config.append(shop)

                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])

                if has_enabled_automation:
                    shops_with_enabled_features.append(shop)
        
        print(f"With automation config: {len(shops_with_config)}")
        print(f"With enabled features: {len(shops_with_enabled_features)}")
        
        if not enabled_shops:
            print("\nNo shops have automation enabled.")
            return
        
        print(f"\n{'='*80}")
        print("DETAILED SCHEDULE")
        print(f"{'='*80}")
        print(f"{'Shop ID':<8} {'Time (UTC)':<10} {'Enabled':<8} {'Features':<30} {'Next Run':<20}")
        print("-" * 80)
        
        now = datetime.utcnow()
        conflicts = []
        prev_minutes = None
        
        for shop in enabled_shops:
            # Get automation config
            config = AIConfig.query.filter_by(
                shop_id=shop.shop_id,
                module_type='seo_automation'
            ).first()
            
            features = []
            if config and config.config_data:
                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                if config_data.get('seo_enabled', False):
                    features.append('SEO')
                if config_data.get('collection_enabled', False):
                    features.append('Collections')
                if config_data.get('translation_enabled', False):
                    features.append('Translation')
            
            features_str = ', '.join(features) if features else 'None'
            
            # Calculate next run time
            schedule_time = datetime(
                now.year, now.month, now.day,
                shop.automation_hour, shop.automation_minute
            )
            
            if schedule_time <= now:
                schedule_time += timedelta(days=1)
            
            time_until = schedule_time - now
            hours_until = int(time_until.total_seconds() // 3600)
            minutes_until = int((time_until.total_seconds() % 3600) // 60)
            next_run = f"in {hours_until}h {minutes_until}m"
            
            # Check for conflicts
            current_minutes = shop.automation_hour * 60 + shop.automation_minute
            if prev_minutes is not None:
                interval = current_minutes - prev_minutes
                if interval < 20:
                    conflicts.append(f"Shop {shop.shop_id} (interval: {interval} min)")
            
            time_str = f"{shop.automation_hour:02d}:{shop.automation_minute:02d}"
            enabled_str = "Yes" if features else "No"
            
            print(f"{shop.shop_id:<8} {time_str:<10} {enabled_str:<8} {features_str:<30} {next_run:<20}")
            
            prev_minutes = current_minutes
        
        # Check for conflicts
        print(f"\n{'='*80}")
        print("CONFLICT ANALYSIS")
        print(f"{'='*80}")
        
        if conflicts:
            print(f"⚠️  Found {len(conflicts)} scheduling conflicts (< 20 min intervals):")
            for conflict in conflicts:
                print(f"   {conflict}")
            print("\nRun 'python redistribute_shop_schedules.py' to fix conflicts.")
        else:
            print(f"✓ All {len(enabled_shops)} shops have proper 20+ minute intervals")
        
        # Show Celery Beat schedule
        print(f"\n{'='*80}")
        print("CELERY BEAT SCHEDULE")
        print(f"{'='*80}")
        
        try:
            schedule = get_dynamic_schedule()
            automation_tasks = {k: v for k, v in schedule.items() if 'automation' in k}
            
            print(f"Total scheduled tasks: {len(schedule)}")
            print(f"Automation tasks: {len(automation_tasks)}")
            
            if automation_tasks:
                print("\nAutomation task schedule:")
                for task_name, task_config in sorted(automation_tasks.items()):
                    if 'args' in task_config and task_config['args']:
                        shop_id = task_config['args'][0]
                        schedule_obj = task_config['schedule']
                        if hasattr(schedule_obj, 'hour') and hasattr(schedule_obj, 'minute'):
                            time_str = f"{schedule_obj.hour[0]:02d}:{schedule_obj.minute[0]:02d}"
                            print(f"  {task_name}: Shop {shop_id} at {time_str} UTC")
            
        except Exception as e:
            print(f"Error loading Celery Beat schedule: {e}")
        
        # Show system tasks
        print(f"\n{'='*80}")
        print("SYSTEM TASKS")
        print(f"{'='*80}")
        print("cleanup-old-jobs: 01:00 UTC daily")
        print("fetch-search-console-metrics: 03:00 UTC daily")
        print("Shop automations: Starting 04:00 UTC with 20-minute intervals")

def show_next_runs():
    """Show when each shop will run next"""
    print(f"\n{'='*60}")
    print("NEXT AUTOMATION RUNS")
    print(f"{'='*60}")
    
    app = create_app()
    
    with app.app_context():
        enabled_shops = UserShop.query.filter_by(automation_enabled=True).order_by(
            UserShop.automation_hour, 
            UserShop.automation_minute
        ).all()
        
        now = datetime.utcnow()
        upcoming_runs = []
        
        for shop in enabled_shops:
            # Check if shop has enabled features
            config = AIConfig.query.filter_by(
                shop_id=shop.shop_id,
                module_type='seo_automation'
            ).first()
            
            if config and config.config_data:
                # Handle both dict and string config_data
                config_data = config.config_data
                if isinstance(config_data, str):
                    import json
                    try:
                        config_data = json.loads(config_data)
                    except json.JSONDecodeError:
                        config_data = {}

                has_enabled_automation = any([
                    config_data.get('seo_enabled', False),
                    config_data.get('collection_enabled', False),
                    config_data.get('translation_enabled', False)
                ])

                if has_enabled_automation:
                    # Calculate next run time
                    schedule_time = datetime(
                        now.year, now.month, now.day,
                        shop.automation_hour, shop.automation_minute
                    )
                    
                    if schedule_time <= now:
                        schedule_time += timedelta(days=1)
                    
                    upcoming_runs.append((schedule_time, shop))
        
        # Sort by next run time
        upcoming_runs.sort(key=lambda x: x[0])
        
        print(f"Next {min(10, len(upcoming_runs))} automation runs:")
        print("-" * 50)
        
        for i, (run_time, shop) in enumerate(upcoming_runs[:10]):
            time_until = run_time - now
            hours_until = int(time_until.total_seconds() // 3600)
            minutes_until = int((time_until.total_seconds() % 3600) // 60)
            
            run_time_str = run_time.strftime('%Y-%m-%d %H:%M UTC')
            print(f"{i+1:2d}. Shop {shop.shop_id:3d}: {run_time_str} (in {hours_until}h {minutes_until}m)")

if __name__ == '__main__':
    check_schedules()
    show_next_runs()
    print(f"\n{'='*80}\n")
