#!/usr/bin/env python3
import os
import sys
import subprocess
import time
import signal
from threading import Thread
from app import create_app, db
from app.models import Shop, AIConfig

# Parse command line arguments
START_ALL = '--all' in sys.argv or '-a' in sys.argv
START_SERVICES = '--services' in sys.argv or '-s' in sys.argv
HELP = '--help' in sys.argv or '-h' in sys.argv

if HELP:
    print("""
Shopify AI Control - Run Script

Usage: python run.py [options]

Options:
  --all, -a       Start all services (Redis, Celery, Flask)
  --services, -s  Start background services only (Redis, Celery)
  --help, -h      Show this help message

Without options, only the Flask app will start.
""")
    sys.exit(0)

app = create_app()
processes = []

def start_redis():
    """Check Upstash Redis connection"""
    print("Checking Upstash Redis connection...")
    try:
        import redis
        from config import Config
        
        # Test connection to Upstash Redis
        redis_url = Config.REDIS_URL
        if '?ssl_cert_reqs=' not in redis_url:
            redis_url += '?ssl_cert_reqs=CERT_NONE'
        
        r = redis.from_url(
            redis_url,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            ssl_ca_certs=None
        )
        r.ping()
        print("✓ Upstash Redis connection successful")
        return True
    except Exception as e:
        print(f"✗ Failed to connect to Upstash Redis: {e}")
        return False

def start_celery_services():
    """Start Celery worker and beat"""
    print("\nStarting Celery services...")
    
    # Start worker
    if os.name == 'nt':  # Windows
        worker_cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'celery_app.celery_app',
            'worker',
            '--loglevel=info',
            '--pool=threads',
            '--concurrency=4'
        ]
    else:
        worker_cmd = [
            'celery',
            '-A', 'celery_app.celery_app',
            'worker',
            '--loglevel=info',
            '--concurrency=4'
        ]
    
    try:
        worker = subprocess.Popen(worker_cmd)
        processes.append(worker)
        print("✓ Celery worker started")
    except Exception as e:
        print(f"✗ Failed to start Celery worker: {e}")
        return False
    
    # Start beat scheduler
    beat_cmd = [
        'celery',
        '-A', 'celery_app.celery_app',
        'beat',
        '--loglevel=info'
    ]
    
    if os.name == 'nt':  # Windows
        beat_cmd = [sys.executable, '-m'] + beat_cmd
    
    try:
        beat = subprocess.Popen(beat_cmd)
        processes.append(beat)
        print("✓ Celery beat started")
    except Exception as e:
        print(f"Warning: Celery beat not started: {e}")
    
    return True

def signal_handler(sig, frame):
    """Handle shutdown signal"""
    print("\n\nShutting down services...")
    for p in processes:
        try:
            p.terminate()
        except:
            pass
    sys.exit(0)

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'Shop': Shop, 'AIConfig': AIConfig}

if __name__ == '__main__':
    # Setup signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    # Initialize database tables (simplified approach)
    print("Initializing database tables...")
    try:
        with app.app_context():
            db.create_all()
            print("[OK] Database tables created successfully")
    except Exception as e:
        print(f"[WARNING] Failed to create database tables: {e}")
        print("App will continue - tables may already exist")
    
    # Start services if requested
    if START_ALL or START_SERVICES:
        print("\n" + "="*50)
        print("Starting background services...")
        print("="*50)
        
        # Check Redis connection
        if not start_redis():
            print("Failed to connect to Upstash Redis. Check your configuration.")
            sys.exit(1)
        
        # Start Celery
        if not start_celery_services():
            print("Failed to start Celery. Exiting...")
            sys.exit(1)
        
        print("\n✓ All background services started!")
        
        if START_SERVICES:
            print("\nServices are running. Press Ctrl+C to stop.")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                signal_handler(None, None)
    
    # Start Flask app (unless only services were requested)
    if not START_SERVICES:
        print("\n" + "="*50)
        print("Starting Flask application...")
        print("="*50)
        print("\nAccess the application at: http://localhost:5000")
        print("Press Ctrl+C to stop all services\n")
        
        try:
            app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        except KeyboardInterrupt:
            signal_handler(None, None)