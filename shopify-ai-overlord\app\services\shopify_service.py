import requests
import json

class ShopifyService:
    def __init__(self, shop_domain, access_token):
        self.shop_domain = shop_domain
        self.access_token = access_token
        self.api_version = '2024-10'
        self.headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
        self.graphql_headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, method, endpoint, data=None, raw_response=False):
        """Make request to Shopify API"""
        url = f"https://{self.shop_domain}/admin/api/{self.api_version}/{endpoint}"
        
        response = requests.request(
            method=method,
            url=url,
            headers=self.headers,
            json=data
        )
        
        response.raise_for_status()
        
        if raw_response:
            return response
        
        return response.json() if response.content else None
    
    def _make_graphql_request(self, query, variables=None):
        """Make GraphQL request to Shopify API"""
        url = f"https://{self.shop_domain}/admin/api/{self.api_version}/graphql.json"
        
        payload = {
            "query": query,
            "variables": variables or {}
        }
        
        response = requests.post(
            url=url,
            headers=self.graphql_headers,
            json=payload
        )
        
        response.raise_for_status()
        result = response.json()
        
        if 'errors' in result:
            raise Exception(f"GraphQL errors: {result['errors']}")
        
        return result['data']
    
    def get_shop_details(self):
        """Get shop information"""
        return self._make_request('GET', 'shop.json')['shop']
    
    def get_access_scopes(self):
        """Get current access scopes for the app"""
        try:
            query = """
            {
                app {
                    installation {
                        accessScopes {
                            handle
                        }
                    }
                }
            }
            """
            data = self._make_graphql_request(query)
            scopes = data.get('app', {}).get('installation', {}).get('accessScopes', [])
            return [scope['handle'] for scope in scopes]
        except Exception as e:
            print(f"Error getting access scopes: {e}")
            return []
    
    def get_products(self, limit=50, published_status='published'):
        """Get products from shop with pagination support
        
        Args:
            limit: Maximum number of products to return (0 for all)
            published_status: Filter by published status ('published', 'unpublished', 'any')
                            'published' = available on online store
                            'unpublished' = not available on online store
                            'any' = all products (default Shopify behavior)
        """
        all_products = []
        page_info = None
        pages_fetched = 0
        
        # Shopify REST API has a max limit of 250 per request
        per_page = min(limit, 250) if limit > 0 else 250
        
        while True:
            if page_info:
                # Use page_info for pagination
                response = self._make_request('GET', f'products.json?limit={per_page}&published_status={published_status}&page_info={page_info}', raw_response=True)
            else:
                # First request
                response = self._make_request('GET', f'products.json?limit={per_page}&published_status={published_status}', raw_response=True)
            
            products = response.json()['products']
            all_products.extend(products)
            pages_fetched += 1
            
            # Check if we've reached the desired limit
            if limit > 0 and len(all_products) >= limit:
                return all_products[:limit]
            
            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                # Extract page_info from Link header
                import re
                match = re.search(r'page_info=([^&>]+)', link_header)
                if match:
                    page_info = match.group(1)
                else:
                    break
            else:
                break
        
        # Products fetched successfully
        return all_products
    
    def get_orders(self, limit=50):
        """Get orders from shop"""
        return self._make_request('GET', f'orders.json?limit={limit}&status=any')['orders']
    
    def get_customers(self, limit=50):
        """Get customers from shop"""
        return self._make_request('GET', f'customers.json?limit={limit}')['customers']
    
    def get_collections(self, limit=250):
        """Get all collections (smart and custom) from shop"""
        try:
            # Get custom collections with pagination
            custom_collections = self._get_paginated_resource('custom_collections', limit)
            
            # Get smart collections with pagination
            smart_collections = self._get_paginated_resource('smart_collections', limit)
            
            return custom_collections + smart_collections
        except Exception as e:
            print(f"Error getting collections: {e}")
            return []
    
    def get_all_collection_handles(self):
        """Get all collection handles to check for duplicates"""
        try:
            all_handles = set()
            
            # Get all custom collections
            custom_collections = self._get_paginated_resource('custom_collections', 0)  # 0 = get all
            for collection in custom_collections:
                if 'handle' in collection:
                    all_handles.add(collection['handle'])
            
            # Get all smart collections
            smart_collections = self._get_paginated_resource('smart_collections', 0)  # 0 = get all
            for collection in smart_collections:
                if 'handle' in collection:
                    all_handles.add(collection['handle'])
            
            return all_handles
        except Exception as e:
            print(f"Error getting collection handles: {e}")
            return set()
    
    def _get_paginated_resource(self, resource_name, limit):
        """Generic method to get paginated resources"""
        all_items = []
        page_info = None
        
        # Shopify REST API has a max limit of 250 per request
        per_page = min(limit, 250) if limit > 0 else 250
        
        while True:
            if page_info:
                response = self._make_request('GET', f'{resource_name}.json?limit={per_page}&page_info={page_info}', raw_response=True)
            else:
                response = self._make_request('GET', f'{resource_name}.json?limit={per_page}', raw_response=True)
            
            items = response.json()[resource_name]
            all_items.extend(items)
            
            # Check if we've reached the desired limit
            if limit > 0 and len(all_items) >= limit:
                return all_items[:limit]
            
            # Check for next page
            link_header = response.headers.get('Link', '')
            if 'rel="next"' in link_header:
                import re
                match = re.search(r'page_info=([^&>]+)', link_header)
                if match:
                    page_info = match.group(1)
                else:
                    break
            else:
                break
        
        return all_items
    
    def get_product(self, product_id):
        """Get a single product by ID"""
        try:
            return self._make_request('GET', f'products/{product_id}.json')['product']
        except Exception as e:
            print(f"Error getting product {product_id}: {e}")
            return None
    
    def update_product(self, product_id, data):
        """Update product details"""
        try:
            self._make_request('PUT', f'products/{product_id}.json', {'product': data})
            return True
        except Exception as e:
            print(f"Error updating product: {e}")
            return False
    
    def create_product(self, data):
        """Create new product"""
        return self._make_request('POST', 'products.json', {'product': data})['product']
    
    def update_product_metafield(self, product_id, namespace, key, value, value_type='string'):
        """Update or create product metafield"""
        try:
            metafield_data = {
                'metafield': {
                    'namespace': namespace,
                    'key': key,
                    'value': value,
                    'type': 'single_line_text_field' if value_type == 'string' else value_type
                }
            }
            self._make_request('POST', f'products/{product_id}/metafields.json', metafield_data)
            return True
        except Exception as e:
            print(f"Error updating metafield: {e}")
            return False
    
    def get_collection_by_handle(self, handle):
        """Get collection by handle (URL slug) to check if it exists"""
        try:
            # Try smart collections first
            try:
                response = self._make_request('GET', f'smart_collections.json?handle={handle}')
                if response and 'smart_collections' in response and len(response['smart_collections']) > 0:
                    return response['smart_collections'][0]
            except:
                pass
            
            # Try custom collections
            try:
                response = self._make_request('GET', f'custom_collections.json?handle={handle}')
                if response and 'custom_collections' in response and len(response['custom_collections']) > 0:
                    return response['custom_collections'][0]
            except:
                pass
                
            return None
        except Exception as e:
            print(f"Error getting collection by handle: {e}")
            return None
    
    def create_smart_collection(self, data):
        """Create a smart collection with automatic product rules"""
        try:
            # Check if collection with same handle already exists
            if 'handle' in data:
                existing = self.get_collection_by_handle(data['handle'])
                if existing:
                    print(f"Collection with handle '{data['handle']}' already exists (ID: {existing.get('id')})")
                    return existing  # Return existing collection instead of creating duplicate
            
            response = self._make_request('POST', 'smart_collections.json', {'smart_collection': data})
            if response and 'smart_collection' in response:
                return response['smart_collection']  # Return the created collection object
            return True  # Fallback for backward compatibility
        except Exception as e:
            print(f"Error creating smart collection: {e}")
            return False
    
    def create_custom_collection(self, data):
        """Create a custom collection (manual product selection)"""
        try:
            # Check if collection with same handle already exists
            if 'handle' in data:
                existing = self.get_collection_by_handle(data['handle'])
                if existing:
                    print(f"Collection with handle '{data['handle']}' already exists (ID: {existing.get('id')})")
                    return existing  # Return existing collection instead of creating duplicate
            
            response = self._make_request('POST', 'custom_collections.json', {'custom_collection': data})
            if response and 'custom_collection' in response:
                return response['custom_collection']  # Return the created collection object
            return True  # Fallback for backward compatibility
        except Exception as e:
            print(f"Error creating custom collection: {e}")
            return False
    
    def delete_smart_collection(self, collection_id):
        """Delete a smart collection"""
        try:
            self._make_request('DELETE', f'smart_collections/{collection_id}.json')
            return True
        except Exception as e:
            print(f"Error deleting smart collection: {e}")
            return False
    
    def delete_custom_collection(self, collection_id):
        """Delete a custom collection"""
        try:
            self._make_request('DELETE', f'custom_collections/{collection_id}.json')
            return True
        except Exception as e:
            print(f"Error deleting custom collection: {e}")
            return False
    
    def get_shop_locales(self):
        """Get available shop locales/languages using GraphQL"""
        try:
            query = """
            query getShopLocales {
                shopLocales {
                    locale
                    name
                    primary
                    published
                }
            }
            """
            
            result = self._make_graphql_request(query)
            
            # The _make_graphql_request already returns result['data'], so we can directly access shopLocales
            locales = result.get('shopLocales', []) if result else []
            
            # If no locales found, return at least the primary language
            if not locales:
                return [
                    {
                        'locale': 'en',
                        'name': 'English',
                        'primary': True,
                        'published': True
                    }
                ]
            
            return locales
        except Exception as e:
            print(f"Error getting shop locales: {e}")
            # Fallback to primary language
            return [
                {
                    'locale': 'en',
                    'name': 'English',
                    'primary': True,
                    'published': True
                }
            ]
    
    def create_translation(self, resource_type, resource_id, translations):
        """Create translations for a resource (product, collection, etc)"""
        try:
            # First, try the GraphQL approach
            print("Attempting GraphQL translation API...")
            result = self.create_translation_with_graphql(resource_type, resource_id, translations)
            if result:
                print("GraphQL translation successful!")
                return True
            else:
                print("GraphQL translation returned False, falling back to REST API...")
                raise Exception("GraphQL translation failed")
        except Exception as e:
            print(f"GraphQL translation error: {str(e)}")
            if "ACCESS_DENIED" in str(e) or "read_translations" in str(e) or "translatableResource" in str(e):
                print("Falling back to REST API approach due to missing scopes...")
                
                # Fallback to REST API approach
                locale = translations[0].get('locale') if translations else 'en'
                
                # Build update data based on resource type
                update_data = {}
                for trans in translations:
                    key = trans.get('key')
                    value = trans.get('value')
                    
                    if key == 'title':
                        update_data['title'] = value
                    elif key == 'body_html':
                        update_data['body_html'] = value
                    # Note: meta fields would need to be handled via metafields API
                
                if resource_type.lower() == 'product':
                    success = self.update_product(resource_id, update_data)
                elif resource_type.lower() in ['collection', 'smart_collection', 'custom_collection']:
                    # Collections use different endpoints
                    try:
                        # Determine collection type by trying to fetch it
                        try:
                            # Try smart collection first
                            self._make_request('GET', f'smart_collections/{resource_id}.json')
                            self._make_request('PUT', f'smart_collections/{resource_id}.json', 
                                             {'smart_collection': update_data})
                        except:
                            # If that fails, it's a custom collection
                            self._make_request('PUT', f'custom_collections/{resource_id}.json', 
                                             {'custom_collection': update_data})
                        success = True
                    except Exception as e:
                        print(f"Error updating collection: {e}")
                        success = False
                elif resource_type.lower() == 'page':
                    try:
                        self._make_request('PUT', f'pages/{resource_id}.json', 
                                         {'page': update_data})
                        success = True
                    except Exception as e:
                        print(f"Error updating page: {e}")
                        success = False
                else:
                    success = False
                
                if success:
                    print(f"Translation saved using REST API fallback for locale: {locale}")
                    print("Note: This updates the main content, not locale-specific translations")
                
                return success
            else:
                print(f"Unexpected error creating translation: {e}")
                return False
    
    def create_translation_with_graphql(self, resource_type, resource_id, translations):
        """Create translations using GraphQL - requires read_translations and write_translations scopes"""
        try:
            # Convert resource type to proper GraphQL format
            resource_type_map = {
                'product': 'Product',
                'collection': 'Collection',
                'smart_collection': 'Collection',
                'custom_collection': 'Collection',
                'page': 'OnlineStorePage'
            }
            
            gql_resource_type = resource_type_map.get(resource_type.lower(), resource_type)
            resource_gid = f"gid://shopify/{gql_resource_type}/{resource_id}"
            
            # First, fetch the translatable content to get digests
            fetch_query = """
            query getTranslatableContent($resourceId: ID!) {
                translatableResource(resourceId: $resourceId) {
                    translatableContent {
                        key
                        value
                        digest
                        locale
                    }
                }
            }
            """
            
            print(f"Fetching translatable content for {resource_gid}...")
            fetch_data = self._make_graphql_request(fetch_query, {"resourceId": resource_gid})
            print(f"Fetch response: {fetch_data}")
            translatable_contents = fetch_data.get('translatableResource', {}).get('translatableContent', [])
            
            # Create a map of key to digest
            digest_map = {}
            for content in translatable_contents:
                digest_map[content['key']] = content['digest']
            
            print(f"Available translatable fields: {list(digest_map.keys())}")
            print(f"Requested translation fields: {[t.get('key') for t in translations]}")
            
            # Filter translations to only include fields that have digests
            valid_translations = []
            for translation in translations:
                key = translation.get('key')
                if key in digest_map:
                    translation['translatableContentDigest'] = digest_map[key]
                    valid_translations.append(translation)
                else:
                    print(f"Skipping translation for '{key}' - no digest available")
            
            if not valid_translations:
                print("No valid translations after filtering")
                return False
            
            print(f"Sending {len(valid_translations)} valid translations")
            
            # Now register the translations
            register_query = """
            mutation translationsRegister($resourceId: ID!, $translations: [TranslationInput!]!) {
                translationsRegister(resourceId: $resourceId, translations: $translations) {
                    userErrors {
                        message
                        field
                    }
                    translations {
                        locale
                        key
                        value
                    }
                }
            }
            """
            
            variables = {
                "resourceId": resource_gid,
                "translations": valid_translations
            }
            
            data = self._make_graphql_request(register_query, variables)
            result = data.get('translationsRegister', {})
            
            # Check for errors
            user_errors = result.get('userErrors', [])
            if user_errors:
                error_messages = [f"{err['field']}: {err['message']}" for err in user_errors]
                raise Exception(f"Translation errors: {', '.join(error_messages)}")
            
            return True
        except Exception as e:
            print(f"Error creating translation with GraphQL: {e}")
            return False
    
    def get_online_store_pages(self):
        """Get online store pages for translation"""
        try:
            return self._make_request('GET', 'pages.json')['pages']
        except Exception as e:
            print(f"Error getting pages: {e}")
            return []
    
    def create_url_redirect(self, path, target):
        """Create URL redirect for language-specific paths"""
        try:
            redirect_data = {
                'redirect': {
                    'path': path,
                    'target': target
                }
            }
            self._make_request('POST', 'redirects.json', redirect_data)
            return True
        except Exception as e:
            print(f"Error creating redirect: {e}")
            # Redirects require write_online_store_navigation scope
            print("Note: URL redirects require 'write_online_store_navigation' scope")
            return False
    
    def get_products_count(self, published_status='published'):
        """Get total product count
        
        Args:
            published_status: Filter by published status ('published', 'unpublished', 'any')
        """
        try:
            result = self._make_request('GET', f'products/count.json?published_status={published_status}')
            count = result.get('count', 0)
            # Removed redundant logging
            return count
        except Exception as e:
            print(f"Error getting product count: {e}")
            return 0
    
    def get_products_batch(self, page=1, limit=250, page_info=None, published_status='published'):
        """Get products with pagination info for batch loading
        
        Args:
            page: Page number (1-based)
            limit: Number of products per page (max 250)
            page_info: Shopify page_info token for pagination
            published_status: Filter by published status ('published', 'unpublished', 'any')
        """
        try:
            # Shopify REST API has a max limit of 250 per request
            per_page = min(limit, 250)
            
            # Store page_info tokens in session or cache for better performance
            # For now, we'll use a simple approach with since_id
            if page > 1 and not page_info:
                # Get products to find the last ID from previous pages
                # This is inefficient but works for demo purposes
                # In production, use cursor-based pagination with page_info
                skip = (page - 1) * per_page
                # Use fields parameter to only get IDs for skipped products
                # Limit the skip to 250 max per request
                skip_limited = min(skip, 250)
                temp_response = self._make_request('GET', f'products.json?limit={skip_limited}&published_status={published_status}&fields=id', raw_response=True)
                temp_products = temp_response.json()['products']
                if temp_products:
                    since_id = temp_products[-1]['id']
                    response = self._make_request('GET', f'products.json?limit={per_page}&published_status={published_status}&since_id={since_id}', raw_response=True)
                else:
                    response = self._make_request('GET', f'products.json?limit={per_page}&published_status={published_status}', raw_response=True)
            else:
                response = self._make_request('GET', f'products.json?limit={per_page}&published_status={published_status}', raw_response=True)
            
            products = response.json()['products']
            
            # Check for next page
            link_header = response.headers.get('Link', '')
            has_next = 'rel="next"' in link_header
            
            return products, has_next
        except Exception as e:
            print(f"Error getting products batch: {e}")
            return [], False
    
    def get_collections_count(self):
        """Get total collection count (smart + custom)"""
        try:
            custom_count = self._make_request('GET', 'custom_collections/count.json').get('count', 0)
            smart_count = self._make_request('GET', 'smart_collections/count.json').get('count', 0)
            return custom_count + smart_count
        except Exception as e:
            print(f"Error getting collection count: {e}")
            return 0
    
    def get_collections_batch(self, page=1, limit=250):
        """Get collections with pagination info for batch loading"""
        try:
            # Get custom collections
            custom_response = self._make_request('GET', f'custom_collections.json?limit={limit}', raw_response=True)
            custom_collections = custom_response.json()['custom_collections']
            
            # Get smart collections
            smart_response = self._make_request('GET', f'smart_collections.json?limit={limit}', raw_response=True)
            smart_collections = smart_response.json()['smart_collections']
            
            collections = custom_collections + smart_collections
            
            # Check for next page in either response
            custom_link = custom_response.headers.get('Link', '')
            smart_link = smart_response.headers.get('Link', '')
            has_next = 'rel="next"' in custom_link or 'rel="next"' in smart_link
            
            return collections, has_next
        except Exception as e:
            print(f"Error getting collections batch: {e}")
            return [], False
    
    def get_online_store_pages(self, limit=100):
        """Get online store pages"""
        try:
            return self._get_paginated_resource('pages', limit)
        except Exception as e:
            print(f"Error getting pages: {e}")
            return []
    
    def get_pages_count(self):
        """Get total pages count"""
        try:
            result = self._make_request('GET', 'pages/count.json')
            return result.get('count', 0)
        except Exception as e:
            print(f"Error getting pages count: {e}")
            return 0
    
    def get_pages_batch(self, page=1, limit=50):
        """Get pages with pagination info for batch loading"""
        try:
            # Shopify REST API has a max limit of 250 per request
            per_page = min(limit, 250)
            
            # Similar to products, use since_id for pagination
            if page > 1:
                skip = (page - 1) * per_page
                temp_response = self._make_request('GET', f'pages.json?limit={skip}&fields=id', raw_response=True)
                temp_pages = temp_response.json()['pages']
                if temp_pages:
                    since_id = temp_pages[-1]['id']
                    response = self._make_request('GET', f'pages.json?limit={per_page}&since_id={since_id}', raw_response=True)
                else:
                    response = self._make_request('GET', f'pages.json?limit={per_page}', raw_response=True)
            else:
                response = self._make_request('GET', f'pages.json?limit={per_page}', raw_response=True)
            
            pages = response.json()['pages']
            
            # Check for next page
            link_header = response.headers.get('Link', '')
            has_next = 'rel="next"' in link_header
            
            return pages, has_next
        except Exception as e:
            print(f"Error getting pages batch: {e}")
            return [], False
    
    def update_homepage_seo(self, seo_title, seo_description):
        """Update homepage SEO settings
        
        Note: Homepage SEO is typically managed through theme files or Online Store settings.
        This method provides guidance on how to update these settings.
        """
        try:
            # Homepage SEO settings in Shopify are typically managed through:
            # 1. Theme files (using Liquid templates)
            # 2. Online Store > Preferences in Shopify Admin
            # 
            # The API doesn't provide direct access to update these settings.
            # This is a limitation of the Shopify API.
            
            # Return false to indicate API update is not available
            return False
        except Exception as e:
            print(f"Error with homepage SEO: {e}")
            return False
    
    def get_product_images_with_context(self, product_id):
        """Get product images with surrounding context for alt text optimization"""
        query = """
        query getProductWithImages($id: ID!) {
            product(id: $id) {
                id
                title
                description
                tags
                productType
                vendor
                images(first: 20) {
                    edges {
                        node {
                            id
                            altText
                            url
                            width
                            height
                        }
                    }
                }
                variants(first: 10) {
                    edges {
                        node {
                            id
                            title
                            selectedOptions {
                                name
                                value
                            }
                        }
                    }
                }
            }
        }
        """
        
        try:
            variables = {"id": f"gid://shopify/Product/{product_id}"}
            data = self._make_graphql_request(query, variables)
            
            if not data or not data.get('product'):
                return None
                
            product = data['product']
            images = [edge['node'] for edge in product['images']['edges']]
            variants = [edge['node'] for edge in product['variants']['edges']]
            
            return {
                'product': {
                    'id': product_id,
                    'title': product['title'],
                    'description': product['description'] or '',
                    'tags': product['tags'],
                    'productType': product['productType'] or '',
                    'vendor': product['vendor'] or ''
                },
                'images': images,
                'variants': variants
            }
        except Exception as e:
            print(f"Error fetching product images: {e}")
            return None
    
    def update_product_image_alt_text(self, product_id, image_updates):
        """Update alt text for product images using REST API (more reliable for alt text)"""
        try:
            success_count = 0
            for update in image_updates:
                # Extract numeric image ID from the GID
                image_id = update['id'].split('/')[-1] if '/' in update['id'] else update['id']
                alt_text = update['alt']
                
                # Use REST API to update image alt text
                endpoint = f"products/{product_id}/images/{image_id}.json"
                
                data = {
                    "image": {
                        "id": int(image_id),
                        "alt": alt_text
                    }
                }
                
                print(f"Updating image {image_id} with alt text: '{alt_text}'")
                
                try:
                    response = self._make_request('PUT', endpoint, data)
                    success_count += 1
                    print(f"✓ Successfully updated alt text for image {image_id}")
                except Exception as req_error:
                    print(f"✗ Failed to update image {image_id}: {str(req_error)}")
            
            return success_count > 0
            
        except Exception as e:
            print(f"Error updating image alt text: {e}")
            return False
    
    def get_all_images_for_alt_text_optimization(self, limit=25):
        """Get ALL images across the site that need alt text optimization"""
        all_images = []
        
        # Get product images
        product_images = self.get_products_for_alt_text_optimization(limit=limit)
        for product in product_images:
            all_images.extend([{
                'type': 'product',
                'source_id': product['id'],
                'source_title': product['title'],
                'images_without_alt': product['images_without_alt'],
                'total_images': product['total_images']
            }])
        
        # Get collection images
        collection_images = self.get_collections_for_alt_text_optimization(limit=10)
        for collection in collection_images:
            all_images.extend([{
                'type': 'collection', 
                'source_id': collection['id'],
                'source_title': collection['title'],
                'images_without_alt': collection['images_without_alt'],
                'total_images': collection['total_images']
            }])
        
        # Get page images (from page content)
        page_images = self.get_pages_for_alt_text_optimization(limit=10)
        for page in page_images:
            all_images.extend([{
                'type': 'page',
                'source_id': page['id'], 
                'source_title': page['title'],
                'images_without_alt': page['images_without_alt'],
                'total_images': page['total_images']
            }])
        
        # Get blog post images
        blog_images = self.get_blog_posts_for_alt_text_optimization(limit=10)
        for post in blog_images:
            all_images.extend([{
                'type': 'blog_post',
                'source_id': post['id'],
                'source_title': post['title'], 
                'images_without_alt': post['images_without_alt'],
                'total_images': post['total_images']
            }])
        
        return all_images[:limit]

    def get_products_for_alt_text_optimization(self, limit=25):
        """Get products that need alt text optimization (prioritized)"""
        query = """
        query getProductsForAltText($first: Int!) {
            products(first: $first, sortKey: UPDATED_AT, reverse: true) {
                edges {
                    node {
                        id
                        title
                        images(first: 5) {
                            edges {
                                node {
                                    id
                                    altText
                                    url
                                }
                            }
                        }
                    }
                }
            }
        }
        """
        
        try:
            # Get more products to filter through
            variables = {"first": min(limit * 4, 250)}  # Get 4x to filter for missing alt text
            data = self._make_graphql_request(query, variables)
            
            if not data or not data.get('products'):
                return []
            
            products_needing_alt_text = []
            
            for edge in data['products']['edges']:
                product = edge['node']
                product_id = product['id'].split('/')[-1]  # Extract numeric ID
                
                # Check if product has images without alt text
                images_without_alt = []
                for img_edge in product['images']['edges']:
                    image = img_edge['node']
                    if not image.get('altText') or image['altText'].strip() == '':
                        images_without_alt.append({
                            'id': image['id'],
                            'url': image['url']
                        })
                
                if images_without_alt:
                    products_needing_alt_text.append({
                        'id': product_id,
                        'title': product['title'],
                        'images_without_alt': len(images_without_alt),
                        'total_images': len(product['images']['edges'])
                    })
                
                # Stop when we have enough products
                if len(products_needing_alt_text) >= limit:
                    break
            
            return products_needing_alt_text
            
        except Exception as e:
            print(f"Error fetching products for alt text optimization: {e}")
            return []
    
    def get_collections_for_alt_text_optimization(self, limit=10):
        """Get collections that need alt text optimization"""
        query = """
        query getCollectionsForAltText($first: Int!) {
            collections(first: $first, sortKey: UPDATED_AT, reverse: true) {
                edges {
                    node {
                        id
                        title
                        image {
                            id
                            altText
                            url
                        }
                    }
                }
            }
        }
        """
        
        try:
            variables = {"first": limit * 2}
            data = self._make_graphql_request(query, variables)
            
            if not data or not data.get('collections'):
                return []
            
            collections_needing_alt_text = []
            
            for edge in data['collections']['edges']:
                collection = edge['node']
                collection_id = collection['id'].split('/')[-1]
                
                # Check if collection has image without alt text
                images_without_alt = 0
                total_images = 0
                
                if collection.get('image'):
                    total_images = 1
                    if not collection['image'].get('altText') or collection['image']['altText'].strip() == '':
                        images_without_alt = 1
                
                if images_without_alt > 0:
                    collections_needing_alt_text.append({
                        'id': collection_id,
                        'title': collection['title'],
                        'images_without_alt': images_without_alt,
                        'total_images': total_images
                    })
                
                if len(collections_needing_alt_text) >= limit:
                    break
            
            return collections_needing_alt_text
            
        except Exception as e:
            print(f"Error fetching collections for alt text optimization: {e}")
            return []
    
    def get_pages_for_alt_text_optimization(self, limit=10):
        """Get pages that need alt text optimization (images in content)"""
        query = """
        query getPagesForAltText($first: Int!) {
            pages(first: $first) {
                edges {
                    node {
                        id
                        title
                        bodySummary
                    }
                }
            }
        }
        """
        
        try:
            variables = {"first": limit * 2}
            data = self._make_graphql_request(query, variables)
            
            if not data or not data.get('pages'):
                return []
            
            pages_needing_alt_text = []
            
            for edge in data['pages']['edges']:
                page = edge['node']
                page_id = page['id'].split('/')[-1]
                
                # Simple check for images in content (could be enhanced with HTML parsing)
                body_content = page.get('bodySummary', '')
                images_in_content = body_content.count('<img')
                
                if images_in_content > 0:
                    # Assume some images may need alt text (this is a simplified approach)
                    pages_needing_alt_text.append({
                        'id': page_id,
                        'title': page['title'],
                        'images_without_alt': min(images_in_content, 3),  # Estimate
                        'total_images': images_in_content
                    })
                
                if len(pages_needing_alt_text) >= limit:
                    break
            
            return pages_needing_alt_text
            
        except Exception as e:
            print(f"Error fetching pages for alt text optimization: {e}")
            return []
    
    def get_blog_posts_for_alt_text_optimization(self, limit=10):
        """Get blog posts that need alt text optimization"""
        query = """
        query getBlogsForAltText($first: Int!) {
            blogs(first: 5) {
                edges {
                    node {
                        id
                        articles(first: $first) {
                            edges {
                                node {
                                    id
                                    title
                                    summary
                                    image {
                                        id
                                        altText
                                        url
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        """
        
        try:
            variables = {"first": limit}
            data = self._make_graphql_request(query, variables)
            
            if not data or not data.get('blogs'):
                return []
            
            posts_needing_alt_text = []
            
            for blog_edge in data['blogs']['edges']:
                blog = blog_edge['node']
                
                for article_edge in blog['articles']['edges']:
                    article = article_edge['node']
                    article_id = article['id'].split('/')[-1]
                    
                    images_without_alt = 0
                    total_images = 0
                    
                    # Check featured image
                    if article.get('image'):
                        total_images += 1
                        if not article['image'].get('altText') or article['image']['altText'].strip() == '':
                            images_without_alt += 1
                    
                    # Check images in content summary
                    summary_text = article.get('summary', '')
                    summary_images = summary_text.count('<img')
                    total_images += summary_images
                    images_without_alt += min(summary_images, 1)  # Estimate some need alt text
                    
                    if images_without_alt > 0:
                        posts_needing_alt_text.append({
                            'id': article_id,
                            'title': article['title'],
                            'images_without_alt': images_without_alt,
                            'total_images': total_images
                        })
                    
                    if len(posts_needing_alt_text) >= limit:
                        break
                
                if len(posts_needing_alt_text) >= limit:
                    break
            
            return posts_needing_alt_text
            
        except Exception as e:
            print(f"Error fetching blog posts for alt text optimization: {e}")
            return []