import os
import json
import base64
from typing import List, Dict, Any, Optional
import requests

try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

class OpenAIService:
    def __init__(self, api_key: str = None):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI library not installed. Install with: pip install openai>=1.0.0")
        
        if not api_key:
            api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key is required for technical SEO features")
        
        # Initialize OpenAI client with compatible versions (openai==1.55.3, httpx==0.27.2)
        try:
            self.client = OpenAI(api_key=api_key)
        except Exception as e:
            raise RuntimeError(f"Failed to initialize OpenAI client: {str(e)}")
        
        self.model = "gpt-4o-mini"  # Using GPT-4o-mini for cost-effectiveness
        
    def analyze_technical_seo(self, url: str, screenshot_base64: str = None) -> Dict[str, Any]:
        """Analyze technical SEO using GPT-4o-mini with optional image processing"""
        
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"""
                        Perform a comprehensive technical SEO analysis for: {url}
                        
                        Please analyze the following technical SEO aspects:
                        1. **Page Load Speed**: Identify potential performance bottlenecks
                        2. **Mobile Responsiveness**: Check viewport meta tags and responsive design
                        3. **HTML Structure**: Validate semantic HTML usage and accessibility
                        4. **Meta Tags**: Review title tags, meta descriptions, and Open Graph tags
                        5. **Schema Markup**: Check for structured data implementation
                        6. **Internal Linking**: Analyze navigation and link structure
                        7. **Image Optimization**: Alt tags, file sizes, and lazy loading
                        8. **Core Web Vitals**: LCP, FID, CLS considerations
                        9. **Technical Issues**: Broken links, duplicate content, crawl errors
                        10. **Security**: HTTPS implementation and security headers
                        
                        {"If a screenshot is provided, also analyze:" if screenshot_base64 else ""}
                        {"- Visual layout and design elements" if screenshot_base64 else ""}
                        {"- Above-the-fold content optimization" if screenshot_base64 else ""}
                        {"- CTA placement and visibility" if screenshot_base64 else ""}
                        {"- Mobile vs desktop rendering" if screenshot_base64 else ""}
                        
                        Return your analysis as valid JSON in this exact format:
                        {{
                            "overall_score": 85,
                            "page_speed": {{
                                "score": 75,
                                "issues": ["Large image files", "Unoptimized CSS"],
                                "recommendations": ["Compress images", "Minify CSS/JS"]
                            }},
                            "mobile_optimization": {{
                                "score": 90,
                                "issues": ["Touch targets too small"],
                                "recommendations": ["Increase button sizes"]
                            }},
                            "html_structure": {{
                                "score": 80,
                                "issues": ["Missing H1 tag", "Improper heading hierarchy"],
                                "recommendations": ["Add unique H1", "Fix heading order"]
                            }},
                            "meta_tags": {{
                                "score": 70,
                                "issues": ["Meta description too long", "Missing Open Graph tags"],
                                "recommendations": ["Shorten meta description", "Add OG tags"]
                            }},
                            "schema_markup": {{
                                "score": 60,
                                "issues": ["No structured data found"],
                                "recommendations": ["Implement Product schema", "Add Organization schema"]
                            }},
                            "internal_linking": {{
                                "score": 85,
                                "issues": ["Some orphaned pages"],
                                "recommendations": ["Add breadcrumbs", "Create topic clusters"]
                            }},
                            "image_optimization": {{
                                "score": 65,
                                "issues": ["Missing alt tags", "Large file sizes"],
                                "recommendations": ["Add descriptive alt text", "Use WebP format"]
                            }},
                            "core_web_vitals": {{
                                "score": 75,
                                "issues": ["High LCP", "Layout shifts"],
                                "recommendations": ["Optimize hero image", "Reserve space for ads"]
                            }},
                            "technical_issues": {{
                                "score": 90,
                                "issues": ["Few minor broken links"],
                                "recommendations": ["Fix internal links", "Update sitemap"]
                            }},
                            "security": {{
                                "score": 95,
                                "issues": ["Missing security headers"],
                                "recommendations": ["Add CSP headers", "Implement HSTS"]
                            }},
                            "priority_fixes": [
                                {{
                                    "issue": "Missing H1 tag",
                                    "impact": "High",
                                    "effort": "Low",
                                    "description": "Add a unique H1 tag to improve page structure"
                                }},
                                {{
                                    "issue": "Large image files",
                                    "impact": "High", 
                                    "effort": "Medium",
                                    "description": "Compress and optimize images for faster loading"
                                }}
                            ],
                            "implementation_guide": {{
                                "immediate_fixes": ["Add H1 tag", "Fix meta description"],
                                "short_term": ["Implement schema markup", "Optimize images"],
                                "long_term": ["Improve site architecture", "Enhance mobile UX"]
                            }}
                        }}
                        
                        Make sure to return ONLY the JSON object, no additional text.
                        """
                    }
                ]
            }
        ]
        
        # Add screenshot if provided
        if screenshot_base64:
            messages[0]["content"].append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_base64}",
                    "detail": "high"
                }
            })
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=4096,
                temperature=0.3  # Lower temperature for more consistent technical analysis
            )
            
            response_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                import re
                # Remove markdown code blocks if present
                if '```json' in response_text:
                    json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
                    if json_match:
                        response_text = json_match.group(1)
                
                # Try to find JSON object in the response
                json_match = re.search(r'\{[\s\S]*\}', response_text)
                if json_match:
                    analysis_data = json.loads(json_match.group())
                    return {
                        "success": True,
                        "analysis": analysis_data,
                        "model_used": self.model,
                        "tokens_used": response.usage.total_tokens if hasattr(response, 'usage') else None
                    }
                else:
                    return {
                        "success": True,
                        "raw_analysis": response_text,
                        "model_used": self.model,
                        "parsing_note": "Could not parse as JSON"
                    }
                    
            except json.JSONDecodeError as e:
                return {
                    "success": True,
                    "raw_analysis": response_text,
                    "json_error": str(e),
                    "model_used": self.model
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"OpenAI API error: {str(e)}",
                "error_type": type(e).__name__,
                "model_used": self.model
            }
    
    def analyze_page_screenshot(self, screenshot_base64: str, analysis_type: str = "technical_seo") -> Dict[str, Any]:
        """Analyze a page screenshot for various purposes"""
        
        analysis_prompts = {
            "technical_seo": """
            Analyze this webpage screenshot for technical SEO issues:
            - Above-the-fold content optimization
            - Mobile responsiveness indicators
            - Visual hierarchy and heading structure
            - CTA placement and visibility
            - Loading indicators or broken elements
            - Overall user experience factors
            """,
            "conversion_optimization": """
            Analyze this webpage for conversion optimization:
            - CTA button placement and design
            - Trust signals and social proof
            - Form optimization opportunities
            - Navigation clarity
            - Value proposition visibility
            - Distraction analysis
            """,
            "accessibility": """
            Analyze this webpage for accessibility issues:
            - Color contrast problems
            - Text readability
            - Button and link visibility
            - Image alt text requirements
            - Form field clarity
            - Navigation accessibility
            """
        }
        
        prompt = analysis_prompts.get(analysis_type, analysis_prompts["technical_seo"])
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"{prompt}\n\nProvide specific, actionable recommendations based on what you see in the screenshot."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{screenshot_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=2048,
                temperature=0.3
            )
            
            return {
                "success": True,
                "analysis": response.choices[0].message.content,
                "analysis_type": analysis_type,
                "model_used": self.model,
                "tokens_used": response.usage.total_tokens if hasattr(response, 'usage') else None
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Screenshot analysis error: {str(e)}",
                "model_used": self.model
            }
    
    def generate_technical_seo_recommendations(self, site_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive technical SEO recommendations based on site analysis"""
        
        prompt = f"""
        Based on the following site analysis data, generate a comprehensive technical SEO improvement plan:
        
        Site Data:
        {json.dumps(site_data, indent=2)}
        
        Please provide:
        1. Priority-ranked list of technical improvements
        2. Implementation difficulty assessment (Easy/Medium/Hard)
        3. Expected impact on SEO (High/Medium/Low)
        4. Specific implementation steps
        5. Tools needed for each fix
        6. Timeline estimates
        
        Return as JSON with this structure:
        {{
            "recommendations": [
                {{
                    "title": "Fix Core Web Vitals",
                    "priority": 1,
                    "difficulty": "Medium",
                    "impact": "High",
                    "description": "Detailed description of the issue and fix",
                    "steps": ["Step 1", "Step 2", "Step 3"],
                    "tools_needed": ["Tool 1", "Tool 2"],
                    "estimated_time": "2-4 hours",
                    "category": "Performance"
                }}
            ],
            "quick_wins": ["Easy fixes that can be done immediately"],
            "long_term_strategy": "Overall technical SEO strategy",
            "monitoring_setup": "How to track improvements"
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=3072,
                temperature=0.3
            )
            
            response_text = response.choices[0].message.content
            
            try:
                import re
                json_match = re.search(r'\{[\s\S]*\}', response_text)
                if json_match:
                    recommendations = json.loads(json_match.group())
                    return {
                        "success": True,
                        "recommendations": recommendations,
                        "model_used": self.model
                    }
                else:
                    return {
                        "success": True,
                        "raw_recommendations": response_text,
                        "model_used": self.model
                    }
            except json.JSONDecodeError:
                return {
                    "success": True,
                    "raw_recommendations": response_text,
                    "model_used": self.model
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating recommendations: {str(e)}",
                "model_used": self.model
            }
    
    def check_openai_availability(self) -> Dict[str, Any]:
        """Check if OpenAI service is available and properly configured"""
        try:
            # Make a simple test request
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            
            return {
                "available": True,
                "model": self.model,
                "test_response": response.choices[0].message.content,
                "tokens_used": response.usage.total_tokens if hasattr(response, 'usage') else None
            }
            
        except Exception as e:
            return {
                "available": False,
                "error": str(e),
                "model": self.model
            }
    
    def optimize_image_alt_text(self, image_url: str, product_context: Dict[str, Any], variants: List[Dict] = None) -> Dict[str, Any]:
        """Generate SEO-friendly alt text for product image using both vision and context analysis"""
        
        # Build context prompt
        context_parts = []
        if product_context.get('title'):
            context_parts.append(f"Product: {product_context['title']}")
        if product_context.get('productType'):
            context_parts.append(f"Type: {product_context['productType']}")
        if product_context.get('vendor'):
            context_parts.append(f"Brand: {product_context['vendor']}")
        if product_context.get('tags'):
            context_parts.append(f"Tags: {', '.join(product_context['tags'][:5])}")
        
        # Add variant information if available
        if variants:
            variant_info = []
            for variant in variants[:3]:  # Limit to first 3 variants
                if variant.get('selectedOptions'):
                    options = [f"{opt['name']}: {opt['value']}" for opt in variant['selectedOptions']]
                    variant_info.append(', '.join(options))
            if variant_info:
                context_parts.append(f"Variants: {'; '.join(variant_info)}")
        
        context_text = '\n'.join(context_parts)
        
        prompt = f"""
        Generate a concise, SEO-friendly alt text for this product image. 

        PRODUCT CONTEXT:
        {context_text}

        REQUIREMENTS:
        - Keep under 125 characters (ideal for SEO)
        - Be descriptive but concise
        - Include key product features visible in the image
        - Include brand name if recognizable
        - Focus on what's actually visible, not marketing language
        - Use natural language that helps accessibility
        - Include color, style, or material if clearly visible
        - Don't use phrases like "image of" or "picture of"

        EXAMPLES:
        - "Black leather Chelsea boots with elastic sides"
        - "Red cotton t-shirt with crew neck"
        - "Stainless steel coffee mug with handle"

        Return ONLY the alt text, nothing else.
        """
        
        try:
            # Check if we need to analyze the image or just use context
            should_analyze_image = True
            
            # If we have very detailed context, we might skip image analysis to save costs
            if (len(context_parts) >= 4 and 
                product_context.get('productType') and 
                any(tag for tag in product_context.get('tags', []) if len(tag) > 3)):
                should_analyze_image = False
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
            
            # Add image analysis if needed and if image URL is accessible
            if should_analyze_image and image_url:
                try:
                    # Download and encode image as base64
                    import requests
                    import base64
                    from io import BytesIO
                    from PIL import Image as PILImage
                    
                    response = requests.get(image_url, timeout=10)
                    response.raise_for_status()
                    
                    # Resize image if too large (OpenAI has size limits)
                    img = PILImage.open(BytesIO(response.content))
                    if img.width > 1024 or img.height > 1024:
                        img.thumbnail((1024, 1024), PILImage.Resampling.LANCZOS)
                    
                    # Convert to base64
                    buffer = BytesIO()
                    img.save(buffer, format='JPEG', quality=85)
                    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    
                    # Add image to the message
                    messages[0]["content"].append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_base64}",
                            "detail": "low"  # Use low detail for cost efficiency
                        }
                    })
                    
                except Exception as img_error:
                    print(f"Could not process image for vision analysis: {img_error}")
                    # Continue without image analysis
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=50,  # Alt text should be short
                temperature=0.3  # Lower temperature for consistent results
            )
            
            alt_text = response.choices[0].message.content.strip()
            
            # Clean up the alt text
            alt_text = alt_text.strip('"\'')  # Remove quotes
            alt_text = alt_text[:124]  # Ensure under 125 chars
            
            return {
                "success": True,
                "alt_text": alt_text,
                "model_used": self.model,
                "tokens_used": response.usage.total_tokens if hasattr(response, 'usage') else None,
                "used_vision": should_analyze_image and 'image_url' in str(messages)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating alt text: {str(e)}",
                "model_used": self.model
            }