{% extends "base_auth.html" %}

{% block title %}Collection Status - Shopify AI Control{% endblock %}

{% block extra_css %}
<style>
    /* Container fixes */
    body, html {
        overflow-x: hidden;
        max-width: 100%;
    }
    
    /* Collection card styles */
    .collection-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .collection-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    /* Status badge styles */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        padding: 0.35rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-draft {
        background-color: #f3f4f6;
        color: #374151;
    }
    
    /* Tag display */
    .tag-source {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background-color: #e0e7ff;
        color: #3730a3;
        border-radius: 0.375rem;
        font-weight: 500;
    }
    
    /* Collection info grid */
    .collection-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .info-label {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .info-value {
        font-weight: 600;
        color: #111827;
    }
    
    /* Progress section */
    .progress-section {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .progress-bar-custom {
        height: 2rem;
        background-color: #e5e7eb;
        border-radius: 0.5rem;
        overflow: hidden;
        position: relative;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #10b981;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        transition: width 0.3s ease;
    }
    
    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    /* Tags available section */
    .tags-available {
        background-color: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .tag-item {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.25rem;
        margin: 0.25rem;
        font-size: 0.875rem;
    }
    
    .tag-count {
        background-color: #dbeafe;
        color: #1e40af;
        padding: 0.125rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="mb-2 mb-md-0">
            <i class="fas fa-layer-group me-2"></i>Collection Status
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-primary">
                <i class="fas fa-tachometer-alt me-2"></i>SEO Dashboard
            </a>
            <button class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Info Banner -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Collection Status Monitor</strong> - This page shows collections created from AI-generated tags. 
        To create new collections, use the <a href="{{ url_for('ai_modules.seo_dashboard') }}">SEO Dashboard</a> automation controls.
    </div>

    <!-- Progress Section -->
    <div class="progress-section">
        <h5 class="mb-3">Collection Creation Progress</h5>
        <div class="row mb-3">
            <div class="col-md-3 text-center">
                <h3 class="text-primary mb-0">{{ available_tags_count }}</h3>
                <small class="text-muted">AI-Generated Tags</small>
            </div>
            <div class="col-md-3 text-center">
                <h3 class="text-success mb-0">{{ collections_created }}</h3>
                <small class="text-muted">Collections Created</small>
            </div>
            <div class="col-md-3 text-center">
                <h3 class="text-warning mb-0">{{ tags_pending }}</h3>
                <small class="text-muted">Tags Pending</small>
            </div>
            <div class="col-md-3 text-center">
                <h3 class="text-info mb-0">{{ total_collections }}</h3>
                <small class="text-muted">Total Collections</small>
            </div>
        </div>
        
        {% if available_tags_count > 0 %}
        <div class="progress-bar-custom">
            <div class="progress-fill" style="width: {{ (collections_created / available_tags_count * 100) if available_tags_count > 0 else 0 }}%">
                {{ ((collections_created / available_tags_count * 100) if available_tags_count > 0 else 0)|round|int }}%
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Available Tags Section -->
    {% if pending_tags %}
    <div class="tags-available">
        <h6 class="mb-2">
            <i class="fas fa-tags me-2"></i>AI-Generated Tags Ready for Collection Creation
        </h6>
        <div>
            {% for tag, count in pending_tags[:10] %}
            <span class="tag-item">
                {{ tag }}
                <span class="tag-count">{{ count }} products</span>
            </span>
            {% endfor %}
            {% if pending_tags|length > 10 %}
            <span class="tag-item">
                +{{ pending_tags|length - 10 }} more tags
            </span>
            {% endif %}
        </div>
        <div class="mt-3">
            <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-sm btn-warning">
                <i class="fas fa-magic me-2"></i>Create Collections in Dashboard
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Collections List -->
    <h5 class="mb-3">Created Collections</h5>
    
    {% if created_collections %}
    <div class="row">
        {% for collection in created_collections %}
        <div class="col-lg-6 col-xl-4">
            <div class="collection-card">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">{{ collection.title }}</h6>
                    {% if collection.published %}
                    <span class="status-badge status-active">
                        <i class="fas fa-check-circle"></i> Active
                    </span>
                    {% else %}
                    <span class="status-badge status-draft">
                        <i class="fas fa-circle"></i> Draft
                    </span>
                    {% endif %}
                </div>
                
                <div class="tag-source">
                    <i class="fas fa-tag"></i>
                    Created from: "{{ collection.created_from_tag }}"
                </div>
                
                <div class="collection-info">
                    <div class="info-item">
                        <span class="info-label">Products</span>
                        <span class="info-value">{{ collection.products_count }} items</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Created</span>
                        <span class="info-value">{{ collection.created_at }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Type</span>
                        <span class="info-value">{{ collection.collection_type|title }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Handle</span>
                        <span class="info-value">/collections/{{ collection.handle }}</span>
                    </div>
                </div>
                
                {% if collection.description %}
                <div class="mt-3 text-muted small">
                    {{ collection.description|truncate(150) }}
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <a href="https://{{ shop_domain }}/admin/collections/{{ collection.id }}" 
                       target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>View in Shopify
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <i class="fas fa-layer-group"></i>
        <p>No collections have been created from AI-generated tags yet.</p>
        <p>
            {% if available_tags_count > 0 %}
            You have {{ available_tags_count }} AI-generated tags ready to be turned into collections.
            {% else %}
            First, optimize your products to generate AI tags, then create collections.
            {% endif %}
        </p>
        <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-primary">
            <i class="fas fa-tachometer-alt me-2"></i>Go to SEO Dashboard
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh if collections are being processed
const processingCount = {{ processing_count|default(0) }};
if (processingCount > 0) {
    setTimeout(() => {
        location.reload();
    }, 30000); // Refresh every 30 seconds
}

// Show notification if collections were recently created
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get('created')) {
    const count = urlParams.get('created');
    showNotification(`${count} collection(s) created successfully!`, 'success');
}

function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => alertDiv.remove(), 5000);
}
</script>
{% endblock %}</content>
</invoke>