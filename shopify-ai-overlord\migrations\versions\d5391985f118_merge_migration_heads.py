"""Merge migration heads

Revision ID: d5391985f118
Revises: 6b461de519a4, 8c660d7ca5ec, add_automation_jobs, add_translated_data, add_translation_status, fix_automation_jobs_metadata
Create Date: 2025-05-31 13:29:34.938499

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd5391985f118'
down_revision = ('6b461de519a4', '8c660d7ca5ec', 'add_automation_jobs', 'add_translated_data', 'add_translation_status', 'fix_automation_jobs_metadata')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
