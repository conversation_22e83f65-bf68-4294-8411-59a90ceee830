"""Add translation status tracking

Revision ID: add_translation_status
Revises: c0ba407f5fad
Create Date: 2025-05-29 18:15:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_translation_status'
down_revision = 'c0ba407f5fad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('translation_status',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.String(length=100), nullable=False),
    sa.Column('language', sa.String(length=10), nullable=False),
    sa.Column('is_translated', sa.<PERSON>(), nullable=True),
    sa.Column('translated_at', sa.DateTime(), nullable=True),
    sa.Column('translated_by', sa.String(length=100), nullable=True),
    sa.Column('translated_fields', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('shop_id', 'resource_type', 'resource_id', 'language', name='_shop_resource_language_uc')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('translation_status')
    # ### end Alembic commands ###