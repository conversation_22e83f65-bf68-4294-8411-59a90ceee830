from datetime import datetime
from app import db
from sqlalchemy import Index, UniqueConstraint


class SearchConsoleMetrics(db.Model):
    """Store Search Console performance metrics for all created/optimized URLs"""
    __tablename__ = 'search_console_metrics'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False)
    
    # URL identification
    url = db.Column(db.String(500), nullable=False)  # The full URL path
    resource_type = db.Column(db.String(50))  # product, collection, page
    resource_id = db.Column(db.String(255))  # Shopify resource ID
    language = db.Column(db.String(10))  # en, fr, de, etc.
    is_translation = db.Column(db.Boolean, default=False)
    
    # Performance metrics
    clicks = db.Column(db.Integer, default=0)
    impressions = db.Column(db.Integer, default=0)
    ctr = db.Column(db.Float, default=0.0)  # Click-through rate
    position = db.Column(db.Float, default=0.0)  # Average position
    
    # Top queries data (stored as JSON)
    top_queries = db.Column(db.JSON, default=list)  # List of {query, clicks, impressions, position}
    
    # Date tracking
    metrics_date = db.Column(db.Date, nullable=False)  # The date these metrics are for
    fetched_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Comparison data
    previous_clicks = db.Column(db.Integer, default=0)
    previous_impressions = db.Column(db.Integer, default=0)
    clicks_change = db.Column(db.Integer, default=0)  # Change from previous period
    impressions_change = db.Column(db.Integer, default=0)
    
    # Relationships
    shop = db.relationship('Shop', backref='search_metrics')
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_shop_url_date', 'shop_id', 'url', 'metrics_date'),
        UniqueConstraint('shop_id', 'url', 'metrics_date', name='unique_shop_url_date'),
    )
    
    def calculate_changes(self, previous_metrics):
        """Calculate changes from previous metrics"""
        if previous_metrics:
            self.previous_clicks = previous_metrics.clicks
            self.previous_impressions = previous_metrics.impressions
            self.clicks_change = self.clicks - previous_metrics.clicks
            self.impressions_change = self.impressions - previous_metrics.impressions
    
    def __repr__(self):
        return f'<SearchConsoleMetrics {self.url} - {self.metrics_date}>'