{% extends "base_auth.html" %}

{% block title %}Multi-Language SEO - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    /* Ensure page doesn't overflow horizontally */
    body {
        overflow-x: hidden;
    }
    
    /* Constrain progress bar to container */
    .progress {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Fix any potential card overflow */
    .card {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Ensure modals don't cause overflow */
    .modal-dialog {
        max-width: 95vw;
    }
    
    /* Ensure tables don't cause overflow */
    .table-responsive {
        max-width: 100%;
        overflow-x: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-globe me-2"></i>Multi-Language SEO & Translation</h1>
    <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/languages" 
       target="_blank" class="btn btn-primary">
        <i class="fas fa-external-link-alt me-2"></i>Manage Languages in Shopify
    </a>
</div>

<!-- Setup Instructions -->
<div class="alert alert-info mb-4">
    <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>How to Use Multi-Language Translation</h5>
    <ol class="mb-2">
        <li><strong>Enable Languages:</strong> Go to <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/languages" target="_blank" class="alert-link">Shopify Admin → Settings → Languages</a></li>
        <li><strong>Add to Markets:</strong> Assign languages to your markets in <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/markets" target="_blank" class="alert-link">Settings → Markets</a></li>
        <li><strong>Translate Content:</strong> Return here and select your target language from the dropdown to translate</li>
    </ol>
    <small>Languages shown are automatically detected from your Shopify store settings.</small>
</div>

<!-- Product Visibility Notice -->
<div class="alert alert-warning mb-4">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Product Visibility:</strong> Only products that are <strong>available on your Online Store</strong> will be translated. 
    Products not visible on the online store channel won't appear in the translation list.
</div>

<!-- Translation Stats -->
<!-- Permission check removed - custom apps have all permissions granted when created -->
<div class="alert alert-info mb-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Translation Mode:</strong> Make sure your custom app has the following Admin API scopes:
    <code>read_translations</code>, <code>write_translations</code>, and <code>write_online_store_navigation</code> (for URL redirects)
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-box fa-2x text-primary mb-2"></i>
                <h3 class="mb-0">{{ translatable_count.products }}</h3>
                <small class="text-muted">Products to Translate</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                <h3 class="mb-0">{{ translatable_count.collections }}</h3>
                <small class="text-muted">Collections to Translate</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-tasks fa-2x text-info mb-2"></i>
                <h3 class="mb-0">{{ translatable_count.total }}</h3>
                <small class="text-muted">Total Items</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-globe fa-2x text-warning mb-2"></i>
                <h3 class="mb-0 translation-progress">Ready</h3>
                <small class="text-muted">Translation Status</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Translation Control -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-language me-2"></i>Translation Control</h5>
            </div>
            <div class="card-body">
                {% if shop_locales|length <= 1 %}
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>No additional languages enabled!</strong> 
                    <br>Please enable languages in your Shopify admin first:
                    <ol class="mb-0 mt-2">
                        <li>Go to <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/languages" target="_blank" class="alert-link">Settings → Languages</a></li>
                        <li>Click "Add language" and select your desired languages</li>
                        <li>Return here to translate your content</li>
                    </ol>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Target Language</label>
                        <select class="form-select" id="target-language-code" {% if shop_locales|length <= 1 %}disabled{% endif %}>
                            <option value="">Select a language</option>
                            {% for locale in shop_locales %}
                                {% if not locale.primary %}
                                <option value="{{ locale.locale }}">{{ locale.name }} ({{ locale.locale }})</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        <small class="form-text">Only showing languages enabled in your Shopify store</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Translation Mode</label>
                        <select class="form-select" id="translation-mode">
                            <option value="selective">Selective (Choose items)</option>
                            <option value="all-products">All Products</option>
                            <option value="all-collections">All Collections</option>
                            <option value="all-pages">All Pages</option>
                            <option value="everything">Everything</option>
                        </select>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">SEO Keywords (Optional)</label>
                    <input type="text" class="form-control" id="seo-keywords" 
                           placeholder="Important keywords to maintain in translations">
                    <small class="form-text">Comma-separated keywords that should be preserved</small>
                </div>
                
                <button class="btn btn-primary" id="start-translation-btn">
                    <i class="fas fa-language me-2"></i>Start Translation
                </button>
            </div>
        </div>

        <!-- Translation Queue -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Translation Queue</h5>
                    <span class="badge bg-light text-dark"><span id="queue-count">0</span> items</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush" id="translation-queue" style="max-height: 400px; overflow-y: auto;">
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>Select a target language and translation mode to begin</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-success" id="process-queue-btn" disabled>
                    <i class="fas fa-play me-2"></i>Process Queue
                </button>
                <button class="btn btn-secondary ms-2" id="clear-queue-btn" disabled>
                    <i class="fas fa-times me-2"></i>Clear Queue
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- AI Translation Assistant -->
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">AI Translation Assistant</h5>
                <p class="small text-muted">Powered by Claude Sonnet 4</p>
                
                <div class="alert alert-success small">
                    <i class="fas fa-check-circle me-2"></i>
                    Claude provides:
                    <ul class="mb-0 mt-2">
                        <li>Culturally adapted translations</li>
                        <li>SEO-optimized content</li>
                        <li>Native market expressions</li>
                        <li>Proper product naming</li>
                        <li>Localized keywords</li>
                    </ul>
                </div>
                
                <h6 class="mt-4">Hreflang Setup</h6>
                <div class="mb-3">
                    <label class="form-label">URL Structure</label>
                    <select class="form-select" id="url-structure">
                        <option value="subdirectory">Subdirectory (/fr, /es)</option>
                        <option value="subdomain">Subdomain (fr.example.com)</option>
                        <option value="domain">Separate Domains</option>
                    </select>
                </div>
                <button class="btn btn-outline-primary w-100" id="generate-hreflang-btn">
                    <i class="fas fa-code me-2"></i>Generate Hreflang Tags
                </button>
            </div>
        </div>
        
        <!-- Language Reference -->
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Enabled Languages</h6>
                {% if shop_locales|length > 0 %}
                <div class="small">
                    {% for locale in shop_locales %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <code>{{ locale.locale }}</code> - {{ locale.name }}
                        </div>
                        <div>
                            {% if locale.primary %}
                            <span class="badge bg-primary">Primary</span>
                            {% else %}
                            <span class="badge bg-success">Available</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted small mb-0">No languages enabled yet.</p>
                {% endif %}
                
                <hr>
                
                <h6 class="mt-3">Need More Languages?</h6>
                <p class="small text-muted mb-2">Add languages in Shopify Admin:</p>
                <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/languages" 
                   target="_blank" class="btn btn-sm btn-outline-primary w-100">
                    <i class="fas fa-plus me-1"></i>Add Languages
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Item Selection Modal -->
<div class="modal fade" id="itemSelectionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Items to Translate</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs mb-3" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#products-tab">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#collections-tab">Collections</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#pages-tab">Pages</a>
                    </li>
                </ul>
                
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="products-tab">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="product-search" placeholder="Search products...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="select-all-products">
                                        </th>
                                        <th>Product</th>
                                    </tr>
                                </thead>
                                <tbody id="product-list">
                                    {% for product in products[:50] %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" 
                                                   data-id="{{ product.id }}" 
                                                   data-title="{{ product.title }}"
                                                   data-description="{{ product.body_html|default('', true) }}"
                                                   data-handle="{{ product.handle|default('', true) }}">
                                        </td>
                                        <td>
                                            <strong>{{ product.title }}</strong>
                                            {% if product.vendor %}
                                            <br><small class="text-muted">{{ product.vendor }}</small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="collections-tab">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="collection-search" placeholder="Search collections...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="select-all-collections">
                                        </th>
                                        <th>Collection</th>
                                    </tr>
                                </thead>
                                <tbody id="collection-list">
                                    {% for collection in collections %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input collection-checkbox" 
                                                   data-id="{{ collection.id }}" 
                                                   data-title="{{ collection.title }}"
                                                   data-description="{{ collection.body_html|default('', true) }}"
                                                   data-handle="{{ collection.handle|default('', true) }}">
                                        </td>
                                        <td>
                                            <strong>{{ collection.title }}</strong>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="pages-tab">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="page-search" placeholder="Search pages...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="select-all-pages">
                                        </th>
                                        <th>Page</th>
                                    </tr>
                                </thead>
                                <tbody id="page-list">
                                    {% for page in pages %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input page-checkbox" 
                                                   data-id="{{ page.id }}" 
                                                   data-title="{{ page.title }}"
                                                   data-description="{{ page.body_html|default('', true) }}"
                                                   data-handle="{{ page.handle|default('', true) }}">
                                        </td>
                                        <td>
                                            <strong>{{ page.title }}</strong>
                                            {% if page.author %}
                                            <br><small class="text-muted">Author: {{ page.author }}</small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span class="text-muted me-auto"><span id="selected-count">0</span> items selected</span>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="add-to-queue-btn">Add to Queue</button>
            </div>
        </div>
    </div>
</div>

<!-- Translation Modal -->
<div class="modal fade" id="translationModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Translation Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="translation-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Translating content with Claude...</p>
                </div>
                <div id="translation-results" style="display: none;">
                    <!-- Results will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Skip</button>
                <button type="button" class="btn btn-primary" id="apply-translation" style="display: none;">Apply Translation</button>
            </div>
        </div>
    </div>
</div>

<!-- Hreflang Modal -->
<div class="modal fade" id="hreflangModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hreflang Implementation Guide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="hreflang-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Generating hreflang strategy...</p>
                </div>
                <div id="hreflang-results" style="display: none;">
                    <!-- Results will be inserted here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let translationQueue = [];
let currentTranslation = null;
let itemSelectionModal = null;
let translationModal = null;
let hreflangModal = null;

// Async loading variables
let isLoadingProducts = false;
let isLoadingCollections = false;
let isLoadingPages = false;
let loadedProductsCount = {{ products|length }};
let loadedCollectionsCount = {{ collections|length }};
let loadedPagesCount = {{ pages|length }};
let totalProductsCount = {{ translatable_count.products }};
let totalCollectionsCount = {{ translatable_count.collections }};
let totalPagesCount = {{ translatable_count.pages }};
let allProducts = [];
let allCollections = [];
let allPages = [];

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    itemSelectionModal = new bootstrap.Modal(document.getElementById('itemSelectionModal'));
    translationModal = new bootstrap.Modal(document.getElementById('translationModal'));
    hreflangModal = new bootstrap.Modal(document.getElementById('hreflangModal'));
    
    // Store initial data
    {% for product in products %}
    allProducts.push({
        id: '{{ product.id }}',
        title: {{ product.title|tojson|safe }},
        body_html: {{ product.body_html|tojson|safe if product.body_html else '""'|safe }},
        handle: {{ product.handle|tojson|safe if product.handle else '""'|safe }},
        vendor: {{ product.vendor|tojson|safe if product.vendor else '""'|safe }}
    });
    {% endfor %}
    
    {% for collection in collections %}
    allCollections.push({
        id: '{{ collection.id }}',
        title: {{ collection.title|tojson|safe }},
        body_html: {{ collection.body_html|tojson|safe if collection.body_html else '""'|safe }},
        handle: {{ collection.handle|tojson|safe if collection.handle else '""'|safe }}
    });
    {% endfor %}
    
    {% for page in pages %}
    allPages.push({
        id: '{{ page.id }}',
        title: {{ page.title|tojson|safe }},
        body_html: {{ page.body_html|tojson|safe if page.body_html else '""'|safe }},
        handle: {{ page.handle|tojson|safe if page.handle else '""'|safe }},
        author: {{ page.author|tojson|safe if page.author else '""'|safe }}
    });
    {% endfor %}
    
    // Add tab change event listeners
    const productTab = document.querySelector('a[href="#products-tab"]');
    const collectionTab = document.querySelector('a[href="#collections-tab"]');
    const pageTab = document.querySelector('a[href="#pages-tab"]');
    
    if (productTab) {
        productTab.addEventListener('shown.bs.tab', function() {
            if (loadedProductsCount < totalProductsCount && !isLoadingProducts) {
                loadMoreProducts();
            }
        });
    }
    
    if (collectionTab) {
        collectionTab.addEventListener('shown.bs.tab', function() {
            if (loadedCollectionsCount < totalCollectionsCount && !isLoadingCollections) {
                loadMoreCollections();
            }
        });
    }
    
    if (pageTab) {
        pageTab.addEventListener('shown.bs.tab', function() {
            if (loadedPagesCount < totalPagesCount && !isLoadingPages) {
                loadMorePages();
            }
        });
    }
});

// Function to load more products
async function loadMoreProducts() {
    if (isLoadingProducts || loadedProductsCount >= totalProductsCount) return;
    
    isLoadingProducts = true;
    let page = Math.floor(loadedProductsCount / 250) + 1;
    
    try {
        while (loadedProductsCount < totalProductsCount) {
            const response = await fetch(`/ai/api/products/batch?page=${page}&limit=250`);
            const data = await response.json();
            
            if (data.success && data.products) {
                data.products.forEach(product => {
                    if (!allProducts.find(p => p.id === product.id)) {
                        allProducts.push({
                            id: product.id,
                            title: product.title,
                            body_html: product.body_html || '',
                            handle: product.handle || '',
                            vendor: product.vendor || ''
                        });
                        loadedProductsCount++;
                    }
                });
                
                if (!data.has_next) break;
                page++;
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                break;
            }
        }
        
        // Update product list in modal if it's open
        updateProductListInModal();
    } catch (error) {
        console.error('Error loading products:', error);
    } finally {
        isLoadingProducts = false;
    }
}

// Function to load more collections
async function loadMoreCollections() {
    if (isLoadingCollections || loadedCollectionsCount >= totalCollectionsCount) return;
    
    isLoadingCollections = true;
    let page = Math.floor(loadedCollectionsCount / 250) + 1;
    
    try {
        while (loadedCollectionsCount < totalCollectionsCount) {
            const response = await fetch(`/ai/api/collections/batch?page=${page}&limit=250`);
            const data = await response.json();
            
            if (data.success && data.collections) {
                data.collections.forEach(collection => {
                    if (!allCollections.find(c => c.id === collection.id)) {
                        allCollections.push({
                            id: collection.id,
                            title: collection.title,
                            body_html: collection.body_html || '',
                            handle: collection.handle || ''
                        });
                        loadedCollectionsCount++;
                    }
                });
                
                if (!data.has_next) break;
                page++;
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                break;
            }
        }
        
        // Update collection list in modal if it's open
        updateCollectionListInModal();
    } catch (error) {
        console.error('Error loading collections:', error);
    } finally {
        isLoadingCollections = false;
    }
}

// Function to load more pages
async function loadMorePages() {
    if (isLoadingPages || loadedPagesCount >= totalPagesCount) return;
    
    isLoadingPages = true;
    let page = Math.floor(loadedPagesCount / 50) + 1;
    
    try {
        while (loadedPagesCount < totalPagesCount) {
            const response = await fetch(`/ai/api/pages/batch?page=${page}&limit=50`);
            const data = await response.json();
            
            if (data.success && data.pages) {
                data.pages.forEach(pageItem => {
                    if (!allPages.find(p => p.id === pageItem.id)) {
                        allPages.push({
                            id: pageItem.id,
                            title: pageItem.title,
                            body_html: pageItem.body_html || '',
                            handle: pageItem.handle || '',
                            author: pageItem.author || ''
                        });
                        loadedPagesCount++;
                    }
                });
                
                if (!data.has_next) break;
                page++;
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                break;
            }
        }
        
        // Update page list in modal if it's open
        updatePageListInModal();
    } catch (error) {
        console.error('Error loading pages:', error);
    } finally {
        isLoadingPages = false;
    }
}

// Update product list in modal
function updateProductListInModal() {
    const tbody = document.getElementById('product-list');
    if (!tbody) return;
    
    // Clear existing content
    tbody.innerHTML = '';
    
    // Add all products
    allProducts.forEach(product => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input product-checkbox" 
                       data-id="${product.id}" 
                       data-title="${product.title.replace(/"/g, '&quot;')}"
                       data-description="${(product.body_html || '').replace(/"/g, '&quot;')}"
                       data-handle="${(product.handle || '').replace(/"/g, '&quot;')}">
            </td>
            <td>
                <strong>${product.title}</strong>
                ${product.vendor ? `<br><small class="text-muted">${product.vendor}</small>` : ''}
            </td>
        `;
        tbody.appendChild(tr);
    });
    
    // Re-attach event listeners
    document.querySelectorAll('.product-checkbox').forEach(cb => {
        cb.addEventListener('change', updateSelectedCount);
    });
}

// Update collection list in modal
function updateCollectionListInModal() {
    const tbody = document.getElementById('collection-list');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    allCollections.forEach(collection => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input collection-checkbox" 
                       data-id="${collection.id}" 
                       data-title="${collection.title.replace(/"/g, '&quot;')}"
                       data-description="${(collection.body_html || '').replace(/"/g, '&quot;')}"
                       data-handle="${(collection.handle || '').replace(/"/g, '&quot;')}">
            </td>
            <td>
                <strong>${collection.title}</strong>
            </td>
        `;
        tbody.appendChild(tr);
    });
    
    document.querySelectorAll('.collection-checkbox').forEach(cb => {
        cb.addEventListener('change', updateSelectedCount);
    });
}

// Update page list in modal
function updatePageListInModal() {
    const tbody = document.getElementById('page-list');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    allPages.forEach(pageItem => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input page-checkbox" 
                       data-id="${pageItem.id}" 
                       data-title="${pageItem.title.replace(/"/g, '&quot;')}"
                       data-description="${(pageItem.body_html || '').replace(/"/g, '&quot;')}"
                       data-handle="${(pageItem.handle || '').replace(/"/g, '&quot;')}">
            </td>
            <td>
                <strong>${pageItem.title}</strong>
                ${pageItem.author ? `<br><small class="text-muted">Author: ${pageItem.author}</small>` : ''}
            </td>
        `;
        tbody.appendChild(tr);
    });
    
    document.querySelectorAll('.page-checkbox').forEach(cb => {
        cb.addEventListener('change', updateSelectedCount);
    });
}

// Start translation button
document.getElementById('start-translation-btn').addEventListener('click', async function() {
    const targetLanguage = document.getElementById('target-language-code').value.trim();
    const translationMode = document.getElementById('translation-mode').value;
    
    if (!targetLanguage) {
        showNotification('Please select a target language', 'warning');
        return;
    }
    
    // Store target language
    window.targetLanguage = targetLanguage;
    
    if (translationMode === 'selective') {
        // Show item selection modal
        itemSelectionModal.show();
        
        // Don't load data in background - only load when user navigates tabs
    } else {
        // Build queue based on mode
        if (translationMode === 'everything') {
            // For "everything" mode, use bulk translation task
            await submitBulkTranslation(targetLanguage);
        } else {
            // For other modes, build queue normally
            await buildQueueByMode(translationMode, targetLanguage);
        }
    }
});

// Submit bulk translation task for "everything" mode
async function submitBulkTranslation(targetLanguage) {
    const btn = document.getElementById('start-translation-btn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting Bulk Translation...';
    
    try {
        const response = await fetch('/ai/api/tasks/translate-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target_languages: [targetLanguage],
                resource_types: ['product', 'collection', 'page'],
                seo_keywords: document.getElementById('seo-keywords').value
            })
        });
        
        const data = await response.json();
        
        if (data.success && data.task_id) {
            showNotification('Bulk translation task started! Check progress in the dashboard.', 'success');
            
            // Update translation status
            document.querySelector('.translation-progress').textContent = 'Processing...';
            
            // Redirect to dashboard after a moment
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
        } else {
            showNotification(data.error || 'Failed to start bulk translation', 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-language me-2"></i>Start Translation';
        }
    } catch (error) {
        showNotification('Error: ' + error.message, 'error');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-language me-2"></i>Start Translation';
    }
}

// Build queue by mode - only load the data we need
async function buildQueueByMode(mode, targetLanguage) {
    translationQueue = [];
    
    // Show a simple loading indicator
    showNotification('Loading items for translation...', 'info');
    
    try {
        if (mode === 'all-products' || mode === 'everything') {
            // Load all products in batches
            let page = 1;
            let hasMore = true;
            
            while (hasMore) {
                const response = await fetch(`/ai/api/products/batch?page=${page}&limit=250`);
                const data = await response.json();
                
                if (data.success && data.products) {
                    data.products.forEach(product => {
                        translationQueue.push({
                            type: 'product',
                            id: product.id,
                            title: product.title,
                            description: product.body_html || '',
                            handle: product.handle || '',
                            targetLocale: targetLanguage
                        });
                    });
                    
                    hasMore = data.has_next;
                    page++;
                } else {
                    hasMore = false;
                }
            }
        }
        
        if (mode === 'all-collections' || mode === 'everything') {
            // Load all collections in batches
            let page = 1;
            let hasMore = true;
            
            while (hasMore) {
                const response = await fetch(`/ai/api/collections/batch?page=${page}&limit=250`);
                const data = await response.json();
                
                if (data.success && data.collections) {
                    data.collections.forEach(collection => {
                        translationQueue.push({
                            type: 'collection',
                            id: collection.id,
                            title: collection.title,
                            description: collection.body_html || '',
                            handle: collection.handle || '',
                            targetLocale: targetLanguage
                        });
                    });
                    
                    hasMore = data.has_next;
                    page++;
                } else {
                    hasMore = false;
                }
            }
        }
        
        if (mode === 'all-pages' || mode === 'everything') {
            // Load all pages in batches
            let page = 1;
            let hasMore = true;
            
            while (hasMore) {
                const response = await fetch(`/ai/api/pages/batch?page=${page}&limit=50`);
                const data = await response.json();
                
                if (data.success && data.pages) {
                    data.pages.forEach(page => {
                        translationQueue.push({
                            type: 'page',
                            id: page.id,
                            title: page.title,
                            description: page.body_html || '',
                            handle: page.handle || '',
                            targetLocale: targetLanguage
                        });
                    });
                    
                    hasMore = data.has_next;
                    page++;
                } else {
                    hasMore = false;
                }
            }
        }
        
        updateQueueDisplay();
        showNotification(`Added ${translationQueue.length} items to translation queue`, 'success');
        
    } catch (error) {
        console.error('Error loading items:', error);
        showNotification('Error loading items. Please try again.', 'error');
    }
}

// Loading modal functions removed - no longer needed

// Search functionality
document.getElementById('product-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('#product-list tr').forEach(row => {
        const title = row.querySelector('strong').textContent.toLowerCase();
        row.style.display = title.includes(searchTerm) ? '' : 'none';
    });
});

document.getElementById('collection-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('#collection-list tr').forEach(row => {
        const title = row.querySelector('strong').textContent.toLowerCase();
        row.style.display = title.includes(searchTerm) ? '' : 'none';
    });
});

document.getElementById('page-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('#page-list tr').forEach(row => {
        const title = row.querySelector('strong').textContent.toLowerCase();
        row.style.display = title.includes(searchTerm) ? '' : 'none';
    });
});

// Select all checkboxes
document.getElementById('select-all-products').addEventListener('change', function() {
    document.querySelectorAll('.product-checkbox').forEach(cb => {
        if (cb.closest('tr').style.display !== 'none') {
            cb.checked = this.checked;
        }
    });
    updateSelectedCount();
});

document.getElementById('select-all-collections').addEventListener('change', function() {
    document.querySelectorAll('.collection-checkbox').forEach(cb => {
        if (cb.closest('tr').style.display !== 'none') {
            cb.checked = this.checked;
        }
    });
    updateSelectedCount();
});

document.getElementById('select-all-pages').addEventListener('change', function() {
    document.querySelectorAll('.page-checkbox').forEach(cb => {
        if (cb.closest('tr').style.display !== 'none') {
            cb.checked = this.checked;
        }
    });
    updateSelectedCount();
});

// Update selected count
document.querySelectorAll('.product-checkbox, .collection-checkbox, .page-checkbox').forEach(cb => {
    cb.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const count = document.querySelectorAll('.product-checkbox:checked, .collection-checkbox:checked, .page-checkbox:checked').length;
    document.getElementById('selected-count').textContent = count;
}

// Add to queue button
document.getElementById('add-to-queue-btn').addEventListener('click', function() {
    const targetLanguage = window.targetLanguage;
    
    // Add selected products
    document.querySelectorAll('.product-checkbox:checked').forEach(cb => {
        translationQueue.push({
            type: 'product',
            id: cb.dataset.id,
            title: cb.dataset.title,
            description: cb.dataset.description || '',
            handle: cb.dataset.handle || '',
            targetLocale: targetLanguage
        });
    });
    
    // Add selected collections
    document.querySelectorAll('.collection-checkbox:checked').forEach(cb => {
        translationQueue.push({
            type: 'collection',
            id: cb.dataset.id,
            title: cb.dataset.title,
            description: cb.dataset.description || '',
            handle: cb.dataset.handle || '',
            targetLocale: targetLanguage
        });
    });
    
    // Add selected pages
    document.querySelectorAll('.page-checkbox:checked').forEach(cb => {
        translationQueue.push({
            type: 'page',
            id: cb.dataset.id,
            title: cb.dataset.title,
            description: cb.dataset.description || '',
            handle: cb.dataset.handle || '',
            targetLocale: targetLanguage
        });
    });
    
    itemSelectionModal.hide();
    updateQueueDisplay();
    showNotification(`Added ${document.querySelectorAll('.product-checkbox:checked, .collection-checkbox:checked, .page-checkbox:checked').length} items to queue`, 'success');
});

// Update queue display
function updateQueueDisplay() {
    const queueElement = document.getElementById('translation-queue');
    const queueCount = document.getElementById('queue-count');
    
    queueCount.textContent = translationQueue.length;
    
    if (translationQueue.length === 0) {
        queueElement.innerHTML = `
            <div class="p-4 text-center text-muted">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p>Select a target language and translation mode to begin</p>
            </div>
        `;
        document.getElementById('process-queue-btn').disabled = true;
        document.getElementById('clear-queue-btn').disabled = true;
    } else {
        queueElement.innerHTML = '';
        translationQueue.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'list-group-item';
            itemElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-${item.type === 'product' ? 'box' : item.type === 'collection' ? 'layer-group' : 'file-alt'} me-2"></i>
                        <strong>${item.title}</strong>
                        <span class="badge bg-secondary ms-2">${item.type}</span>
                    </div>
                    <div>
                        <span class="badge bg-info me-2">${item.targetLocale.toUpperCase()}</span>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromQueue(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            queueElement.appendChild(itemElement);
        });
        document.getElementById('process-queue-btn').disabled = false;
        document.getElementById('clear-queue-btn').disabled = false;
    }
}

// Remove from queue
function removeFromQueue(index) {
    translationQueue.splice(index, 1);
    updateQueueDisplay();
}

// Clear queue
document.getElementById('clear-queue-btn').addEventListener('click', function() {
    if (confirm('Clear all items from the translation queue?')) {
        translationQueue = [];
        updateQueueDisplay();
    }
});

// Process queue - now uses Celery tasks
document.getElementById('process-queue-btn').addEventListener('click', function() {
    if (translationQueue.length === 0) return;
    
    const btn = this;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting Translation Tasks...';
    
    // Process all translations using Celery
    processTranslationQueue();
});

// Process translation queue using Celery tasks
async function processTranslationQueue() {
    if (translationQueue.length === 0) {
        showNotification('All translation tasks submitted!', 'success');
        updateQueueDisplay();
        document.getElementById('process-queue-btn').disabled = false;
        document.getElementById('process-queue-btn').innerHTML = '<i class="fas fa-play me-2"></i>Process Queue';
        return;
    }
    
    // Submit each translation as a Celery task
    const tasks = [];
    const totalItems = translationQueue.length;
    
    while (translationQueue.length > 0) {
        const item = translationQueue.shift();
        
        try {
            const response = await fetch('/ai/api/tasks/translate-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    resource_type: item.type,
                    resource_id: item.id,
                    content: {
                        title: item.title,
                        description: item.description
                    },
                    target_language: item.targetLocale.toUpperCase(),
                    seo_keywords: document.getElementById('seo-keywords').value
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.task_id) {
                tasks.push({
                    task_id: data.task_id,
                    item: item
                });
                showNotification(`Translation task started for "${item.title}"`, 'info');
            } else {
                showNotification(`Failed to start translation for "${item.title}": ${data.error}`, 'error');
            }
            
            // Small delay between submissions
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            showNotification(`Error submitting translation for "${item.title}": ${error.message}`, 'error');
        }
    }
    
    updateQueueDisplay();
    
    // Show notification about tasks
    if (tasks.length > 0) {
        showNotification(`${tasks.length} translation tasks submitted! Check progress in the dashboard.`, 'success');
        
        // Optionally redirect to dashboard after a moment
        setTimeout(() => {
            if (confirm('Translation tasks are running in the background. Would you like to go to the dashboard to track progress?')) {
                window.location.href = '/dashboard';
            }
        }, 2000);
    }
    
    // Re-enable button
    document.getElementById('process-queue-btn').disabled = false;
    document.getElementById('process-queue-btn').innerHTML = '<i class="fas fa-play me-2"></i>Process Queue';
}

// Legacy function for individual translation (kept for modal display)
async function processNextTranslation() {
    if (translationQueue.length === 0) {
        showNotification('All translations completed!', 'success');
        updateQueueDisplay();
        return;
    }
    
    currentTranslation = translationQueue.shift();
    updateQueueDisplay();
    
    translationModal.show();
    document.getElementById('translation-loading').style.display = 'block';
    document.getElementById('translation-results').style.display = 'none';
    document.getElementById('apply-translation').style.display = 'none';
    
    try {
        const response = await fetch('/ai/api/ai/translate-content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content: {
                    title: currentTranslation.title,
                    description: currentTranslation.description
                },
                source_language: 'EN',
                target_language: currentTranslation.targetLocale.toUpperCase(),
                content_type: currentTranslation.type,
                seo_keywords: document.getElementById('seo-keywords').value
            })
        });
        
        const data = await response.json();
        
        if (data.success && data.parsed_data) {
            displayTranslationResults(data.parsed_data);
        } else {
            throw new Error(data.error || 'Translation failed');
        }
    } catch (error) {
        document.getElementById('translation-loading').style.display = 'none';
        document.getElementById('translation-results').style.display = 'block';
        document.getElementById('translation-results').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Error: ${error.message}
            </div>
        `;
    }
}

// Display translation results
function displayTranslationResults(data) {
    document.getElementById('translation-loading').style.display = 'none';
    document.getElementById('translation-results').style.display = 'block';
    document.getElementById('apply-translation').style.display = 'inline-block';
    
    const trans = data.translations;
    
    document.getElementById('translation-results').innerHTML = `
        <h6 class="mb-3">Translation for: ${currentTranslation.title}</h6>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <strong>Original (EN)</strong>
                    </div>
                    <div class="card-body">
                        <p><strong>Title:</strong> ${currentTranslation.title}</p>
                        <p><strong>Description:</strong> ${currentTranslation.description || 'No description'}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <strong>Translation (${currentTranslation.targetLocale.toUpperCase()})</strong>
                    </div>
                    <div class="card-body">
                        <p><strong>Title:</strong> ${trans.title}</p>
                        <p><strong>Description:</strong> ${trans.description || trans.body_html || 'No description'}</p>
                        ${trans.meta_description ? `<p><strong>Meta Description:</strong> ${trans.meta_description}</p>` : ''}
                        ${trans.handle ? `<p><strong>URL Handle:</strong> ${trans.handle}</p>` : ''}
                        ${trans.tags ? `<p><strong>Tags:</strong> ${trans.tags.join(', ')}</p>` : ''}
                    </div>
                </div>
            </div>
        </div>
        
        ${data.cultural_notes ? `
        <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Cultural Notes:</strong> ${data.cultural_notes}
        </div>
        ` : ''}
        
        ${data.seo_analysis ? `
        <div class="alert alert-success mt-3">
            <i class="fas fa-search me-2"></i>
            <strong>SEO Analysis:</strong> ${data.seo_analysis}
        </div>
        ` : ''}
    `;
    
    // Store for applying
    currentTranslation.translations = trans;
}

// Apply translation
document.getElementById('apply-translation').addEventListener('click', async function() {
    if (!currentTranslation || !currentTranslation.translations) {
        showNotification('No translation data available', 'error');
        return;
    }
    
    try {
        // Disable button during save
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
        
        const response = await fetch('/ai/api/ai/save-translation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                resource_type: currentTranslation.type,
                resource_id: currentTranslation.id,
                locale: currentTranslation.targetLocale,
                translations: currentTranslation.translations,
                original_handle: currentTranslation.handle || currentTranslation.title.toLowerCase().replace(/[^a-z0-9]+/g, '-')
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification(`Translation saved successfully for ${currentTranslation.targetLocale.toUpperCase()}!`, 'success');
            translationModal.hide();
            
            // Continue with next item
            setTimeout(() => processNextTranslation(), 1000);
        } else {
            throw new Error(data.error || 'Failed to save translation');
        }
    } catch (error) {
        showNotification(`Error saving translation: ${error.message}`, 'error');
        console.error('Translation save error:', error);
    } finally {
        // Re-enable button
        this.disabled = false;
        this.innerHTML = '<i class="fas fa-check me-2"></i>Apply Translation';
    }
});

// Generate hreflang
document.getElementById('generate-hreflang-btn').addEventListener('click', async function() {
    // Get all enabled languages from the template data
    const enabledLanguages = {{ shop_locales|tojson|safe }};
    
    if (enabledLanguages.length <= 1) {
        showNotification('Please enable at least one additional language in Shopify first', 'warning');
        return;
    }
    
    hreflangModal.show();
    document.getElementById('hreflang-loading').style.display = 'block';
    document.getElementById('hreflang-results').style.display = 'none';
    
    const urlStructure = document.getElementById('url-structure').value;
    
    try {
        const response = await fetch('/ai/api/ai/generate-hreflang', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                enabled_languages: enabledLanguages,
                url_structure: urlStructure
            })
        });
        
        const data = await response.json();
        
        if (data.success && data.parsed_data) {
            displayHreflangStrategy(data.parsed_data);
        } else {
            throw new Error(data.error || 'Failed to generate strategy');
        }
    } catch (error) {
        document.getElementById('hreflang-loading').style.display = 'none';
        document.getElementById('hreflang-results').style.display = 'block';
        document.getElementById('hreflang-results').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Error: ${error.message}
            </div>
        `;
    }
});

// Display hreflang strategy
function displayHreflangStrategy(strategy) {
    document.getElementById('hreflang-loading').style.display = 'none';
    document.getElementById('hreflang-results').style.display = 'block';
    
    let html = `
        <h6>Recommended URL Structure: ${strategy.url_structure.recommendation}</h6>
        <p>${strategy.url_structure.reasoning}</p>
        
        <h6 class="mt-4">URL Examples:</h6>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Page Type</th>
                    <th>URLs</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    for (const [pageType, urls] of Object.entries(strategy.url_structure.examples)) {
        html += `
            <tr>
                <td>${pageType}</td>
                <td>${urls.join('<br>')}</td>
            </tr>
        `;
    }
    
    html += `
            </tbody>
        </table>
        
        <h6 class="mt-4">Hreflang Tags:</h6>
        <div class="bg-light p-3 rounded">
            <pre><code>${Object.values(strategy.hreflang_tags).flat().join('\n')}</code></pre>
        </div>
        
        <h6 class="mt-4">Implementation Checklist:</h6>
        <ol>
    `;
    
    strategy.implementation_checklist.forEach(step => {
        html += `<li>${step}</li>`;
    });
    
    html += `
        </ol>
        
        <h6 class="mt-4">Common Pitfalls to Avoid:</h6>
        <ul class="text-danger">
    `;
    
    strategy.common_pitfalls.forEach(pitfall => {
        html += `<li>${pitfall}</li>`;
    });
    
    html += '</ul>';
    
    document.getElementById('hreflang-results').innerHTML = html;
}

// Show notification
function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => alertDiv.remove(), 5000);
}

// checkScopes function removed - custom apps have all permissions
</script>
{% endblock %}