#!/usr/bin/env python3
"""
Start Celery Beat Scheduler

This script properly starts the Celery Beat scheduler with the correct
configuration and Flask app context.
"""

import os
import sys
import subprocess

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from celerybeat_schedule import get_beat_schedule
import time

def start_celery_beat():
    """Start Celery Beat scheduler using the celery command"""
    print(f"\n{'='*60}")
    print("STARTING CELERY BEAT SCHEDULER")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"{'='*60}\n")
    
    # Test Flask app context and schedule loading
    print("1. Testing Flask app context...")
    try:
        app = create_app()
        with app.app_context():
            schedule = get_beat_schedule()
            automation_tasks = {k: v for k, v in schedule.items() if 'automation' in k}
            print(f"   ✓ Flask app context working")
            print(f"   ✓ Schedule loaded: {len(schedule)} total tasks")
            print(f"   ✓ Automation tasks: {len(automation_tasks)}")
            
            if automation_tasks:
                print("   Automation schedule:")
                for task_name, task_config in automation_tasks.items():
                    if 'args' in task_config and task_config['args']:
                        shop_id = task_config['args'][0]
                        schedule_obj = task_config['schedule']
                        if hasattr(schedule_obj, 'hour') and hasattr(schedule_obj, 'minute'):
                            time_str = f"{schedule_obj.hour[0]:02d}:{schedule_obj.minute[0]:02d}"
                            print(f"     {task_name}: Shop {shop_id} at {time_str} UTC")
    except Exception as e:
        print(f"   ✗ Error testing Flask context: {e}")
        return False
    
    # Set environment variables
    print("\n2. Setting environment variables...")
    os.environ['CELERY_APP'] = 'celery_app:celery_app'
    print("   ✓ CELERY_APP set to celery_app:celery_app")
    
    # Start Celery Beat
    print("\n3. Starting Celery Beat scheduler...")
    print("   Command: celery -A celery_app:celery_app beat --loglevel=info")
    print("   Press Ctrl+C to stop the scheduler")
    print(f"\n{'='*60}")
    
    try:
        # Use subprocess to start celery beat
        cmd = [
            'celery', 
            '-A', 'celery_app:celery_app', 
            'beat', 
            '--loglevel=info',
            '--schedule=/tmp/celerybeat-schedule'  # Use temp file for schedule
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print(f"\n{'='*60}")
        print("Celery Beat scheduler stopped by user")
        print(f"{'='*60}\n")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n{'='*60}")
        print(f"Error starting Celery Beat: {e}")
        print(f"{'='*60}\n")
        return False
    except FileNotFoundError:
        print(f"\n{'='*60}")
        print("Error: 'celery' command not found")
        print("Make sure Celery is installed: pip install celery")
        print(f"{'='*60}\n")
        return False

def show_instructions():
    """Show instructions for manual startup"""
    print(f"\n{'='*60}")
    print("MANUAL CELERY BEAT STARTUP INSTRUCTIONS")
    print(f"{'='*60}")
    
    print("\nIf the automatic startup fails, you can start manually:")
    print("\n1. In one terminal, start Celery workers:")
    print("   celery -A celery_app:celery_app worker --loglevel=info")
    
    print("\n2. In another terminal, start Celery Beat:")
    print("   celery -A celery_app:celery_app beat --loglevel=info")
    
    print("\n3. Monitor the system:")
    print("   python check_automation_schedules.py")
    
    print("\n4. Clear queues if needed:")
    print("   python clear_task_queue.py")
    
    print(f"\n{'='*60}\n")

if __name__ == '__main__':
    success = start_celery_beat()
    
    if not success:
        show_instructions()
