{% extends "base_auth.html" %}

{% block title %}Homepage SEO - Shopify AI Control{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3">Homepage SEO Optimizer</h1>
            <p class="text-muted">Analyze and optimize your store's homepage for better search engine visibility and conversions</p>
        </div>
    </div>

    <!-- Analysis Form -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-4">Analyze Your Homepage</h5>
                    
                    <form id="homepageAnalysisForm">
                        <div class="mb-3">
                            <label for="homepageUrl" class="form-label">Homepage URL</label>
                            <input type="url" class="form-control" id="homepageUrl" 
                                   placeholder="https://your-store.myshopify.com" 
                                   value="https://{{ shop_domain }}" required>
                            <small class="form-text text-muted">Enter your store's homepage URL to analyze</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="targetKeywords" class="form-label">Target Keywords (Optional)</label>
                            <input type="text" class="form-control" id="targetKeywords" 
                                   placeholder="e.g., organic skincare, natural beauty products">
                            <small class="form-text text-muted">Enter keywords you want to rank for (comma-separated)</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="competitorUrls" class="form-label">Competitor URLs (Optional)</label>
                            <textarea class="form-control" id="competitorUrls" rows="3" 
                                      placeholder="https://competitor1.com&#10;https://competitor2.com"></textarea>
                            <small class="form-text text-muted">Enter competitor homepages to analyze (one per line)</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="analyzeBtn">
                            <i class="fas fa-search me-2"></i>Analyze Homepage
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">What this tool does:</h6>
                    <ul class="small mb-0">
                        <li>Fetches your homepage content using JINA AI</li>
                        <li>Analyzes current SEO performance</li>
                        <li>Suggests optimized title and meta description</li>
                        <li>Recommends collection features</li>
                        <li>Provides content block suggestions</li>
                        <li>Offers competitive insights</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="resultsSection" style="display: none;">
        <!-- Loading State -->
        <div id="loadingState" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Analyzing...</span>
            </div>
            <p class="mt-3 text-muted">Analyzing your homepage with AI...</p>
        </div>

        <!-- Results Content -->
        <div id="resultsContent" style="display: none;">
            <!-- SEO Score Card -->
            <div class="row mb-4">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title mb-4">SEO Analysis Results</h5>
                            
                            <!-- Current vs Optimized -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6>Current Homepage Analysis</h6>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>Title:</strong> <span id="currentTitle">-</span></p>
                                        <p class="mb-2"><strong>Meta Description:</strong> <span id="currentMetaDesc">-</span></p>
                                        <p class="mb-0"><strong>Content Length:</strong> <span id="contentLength">-</span> words</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Optimized Recommendations</h6>
                                    <div class="bg-success bg-opacity-10 p-3 rounded">
                                        <p class="mb-2"><strong>Title:</strong> <span id="optimizedTitle">-</span></p>
                                        <small class="text-muted d-block mb-2"><i class="fas fa-info-circle"></i> Your store name will be automatically added by Shopify</small>
                                        <p class="mb-2"><strong>Meta Description:</strong> <span id="optimizedMetaDesc">-</span></p>
                                        <button class="btn btn-sm btn-success" id="applyHomepageSeoBtn" onclick="applyHomepageSeo()" style="display: none;">
                                            <i class="fas fa-check me-2"></i>Apply Changes
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Collection Recommendations -->
                            <div class="mb-4">
                                <h6>Recommended Collection Features</h6>
                                <div id="collectionRecommendations" class="row g-3">
                                    <!-- Collection cards will be inserted here -->
                                </div>
                            </div>

                            <!-- Content Block Suggestions -->
                            <div class="mb-4">
                                <h6>Content Block Suggestions</h6>
                                <div id="contentSuggestions" class="list-group">
                                    <!-- Content suggestions will be inserted here -->
                                </div>
                            </div>

                            <!-- SEO Improvements -->
                            <div class="mb-4">
                                <h6>General SEO Improvements</h6>
                                <ul id="seoImprovements" class="list-unstyled">
                                    <!-- SEO improvements will be inserted here -->
                                </ul>
                            </div>

                            <!-- Note about other optimizations -->
                            <div class="alert alert-info mt-4">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> Collection recommendations and content suggestions are provided as guidance for improving your homepage. 
                                The "Apply Changes" button will guide you to update your homepage title and meta description in Shopify admin.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="alert alert-danger" style="display: none;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span id="errorMessage"></span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .user-select-all {
        user-select: all;
        cursor: text;
        display: block;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        font-family: monospace;
        font-size: 0.875rem;
        word-break: break-all;
    }
    .user-select-all:hover {
        background-color: #e9ecef;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('homepageAnalysisForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const homepageUrl = document.getElementById('homepageUrl').value;
    const targetKeywords = document.getElementById('targetKeywords').value;
    const competitorUrls = document.getElementById('competitorUrls').value;
    
    // Show results section and loading state
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('resultsContent').style.display = 'none';
    document.getElementById('errorState').style.display = 'none';
    
    // Disable analyze button
    const analyzeBtn = document.getElementById('analyzeBtn');
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Analyzing...';
    
    try {
        const response = await fetch('/ai/api/ai/analyze-homepage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                homepage_url: homepageUrl,
                target_keywords: targetKeywords,
                competitor_urls: competitorUrls.split('\n').filter(url => url.trim())
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayResults(result.data);
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('resultsContent').style.display = 'block';
        } else {
            throw new Error(result.error || 'Analysis failed');
        }
    } catch (error) {
        document.getElementById('errorMessage').textContent = error.message;
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('errorState').style.display = 'block';
    } finally {
        // Re-enable analyze button
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-search me-2"></i>Analyze Homepage';
    }
});

function displayResults(data) {
    // Display current analysis
    document.getElementById('currentTitle').textContent = data.current_title || 'Not found';
    document.getElementById('currentMetaDesc').textContent = data.current_meta_description || 'Not found';
    document.getElementById('contentLength').textContent = data.content_length || '0';
    
    // Display optimized recommendations
    document.getElementById('optimizedTitle').textContent = data.optimized_title || 'No recommendation';
    document.getElementById('optimizedMetaDesc').textContent = data.optimized_meta_description || 'No recommendation';
    
    // Display collection recommendations
    const collectionsContainer = document.getElementById('collectionRecommendations');
    collectionsContainer.innerHTML = '';
    if (data.collection_recommendations && data.collection_recommendations.length > 0) {
        data.collection_recommendations.forEach(collection => {
            const col = document.createElement('div');
            col.className = 'col-md-4';
            col.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">${collection.title}</h6>
                        <p class="card-text small">${collection.description}</p>
                        <span class="badge bg-primary">${collection.priority}</span>
                    </div>
                </div>
            `;
            collectionsContainer.appendChild(col);
        });
    }
    
    // Display content suggestions
    const contentContainer = document.getElementById('contentSuggestions');
    contentContainer.innerHTML = '';
    if (data.content_suggestions && data.content_suggestions.length > 0) {
        data.content_suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'list-group-item';
            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${suggestion.type}</h6>
                    <small class="text-muted">${suggestion.priority}</small>
                </div>
                <p class="mb-1">${suggestion.description}</p>
                <small class="text-muted">${suggestion.benefit}</small>
            `;
            contentContainer.appendChild(item);
        });
    }
    
    // Display SEO improvements
    const improvementsContainer = document.getElementById('seoImprovements');
    improvementsContainer.innerHTML = '';
    if (data.seo_improvements && data.seo_improvements.length > 0) {
        data.seo_improvements.forEach(improvement => {
            const li = document.createElement('li');
            li.className = 'mb-2';
            li.innerHTML = `<i class="fas fa-check-circle text-success me-2"></i>${improvement}`;
            improvementsContainer.appendChild(li);
        });
    }
    
    // Store results for apply function
    window.homepageOptimizations = data;
    
    // Show apply button if we have optimized title and description
    if (data.optimized_title && data.optimized_meta_description) {
        document.getElementById('applyHomepageSeoBtn').style.display = 'inline-block';
    }
}

async function applyHomepageSeo() {
    if (!window.homepageOptimizations) {
        alert('No optimizations to apply');
        return;
    }
    
    const applyBtn = document.getElementById('applyHomepageSeoBtn');
    applyBtn.disabled = true;
    applyBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Applying...';
    
    try {
        const response = await fetch('/ai/api/ai/update-homepage-seo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                seo_title: window.homepageOptimizations.optimized_title,
                seo_description: window.homepageOptimizations.optimized_meta_description
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show mt-3';
            notification.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Homepage SEO settings updated successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.getElementById('resultsContent').prepend(notification);
            
            // Update the current values display
            document.getElementById('currentTitle').textContent = window.homepageOptimizations.optimized_title;
            document.getElementById('currentMetaDesc').textContent = window.homepageOptimizations.optimized_meta_description;
            
            // Hide the apply button
            applyBtn.style.display = 'none';
        } else {
            // Show guidance on manual update
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show mt-3';
            
            let instructionsHtml = '';
            if (result.instructions) {
                instructionsHtml = '<ol class="mb-2">';
                result.instructions.forEach(instruction => {
                    instructionsHtml += `<li>${instruction}</li>`;
                });
                instructionsHtml += '</ol>';
            }
            
            let optimizedValuesHtml = '';
            if (result.optimized_title || result.optimized_description) {
                optimizedValuesHtml = `
                    <div class="bg-light p-3 rounded mt-3">
                        <h6 class="mb-2">Copy these optimized values:</h6>
                        ${result.optimized_title ? `<p class="mb-1"><strong>Title:</strong><br><code class="user-select-all">${result.optimized_title}</code></p>` : ''}
                        ${result.optimized_description ? `<p class="mb-0"><strong>Meta Description:</strong><br><code class="user-select-all">${result.optimized_description}</code></p>` : ''}
                    </div>
                `;
            }
            
            notification.innerHTML = `
                <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Manual Update Required</h5>
                <p>${result.guidance || 'Homepage SEO settings need to be updated manually.'}</p>
                ${instructionsHtml}
                ${result.admin_url ? `<a href="${result.admin_url}" target="_blank" class="btn btn-primary btn-sm"><i class="fas fa-external-link-alt me-2"></i>Open Shopify Admin</a>` : ''}
                ${optimizedValuesHtml}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.getElementById('resultsContent').prepend(notification);
            
            // Hide the apply button since manual update is required
            applyBtn.style.display = 'none';
        }
    } catch (error) {
        alert('Error applying SEO settings: ' + error.message);
    } finally {
        applyBtn.disabled = false;
        applyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Apply Changes';
    }
}
</script>
{% endblock %}