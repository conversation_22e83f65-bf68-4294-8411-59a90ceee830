#!/usr/bin/env python
"""Reset daily quota by clearing today's automation jobs"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.automation_job import AutomationJob
from datetime import datetime, timedelta

app = create_app()

with app.app_context():
    # Get today's date range
    today = datetime.utcnow().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = today_start + timedelta(days=1)
    
    # Delete today's automation jobs for shop 2
    shop_id = 2
    
    deleted = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.created_at >= today_start,
        AutomationJob.created_at < today_end
    ).delete()
    
    db.session.commit()
    
    print(f"Deleted {deleted} automation jobs for shop {shop_id} from today")
    print("Daily quota has been reset. The automation can now run again.")
    print("\nTo restart Celery Beat with the new schedule:")
    print("1. Stop the current Celery Beat process (Ctrl+C)")
    print("2. Delete celerybeat-schedule files: rm celerybeat-schedule*")
    print("3. Start Celery Beat again: celery -A celery_app beat --loglevel=warning")