{% extends "base_auth.html" %}

{% block title %}Product SEO - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    /* CRITICAL: Fix container and wrapper issues */
    * {
        box-sizing: border-box;
    }
    
    /* Reset any parent container constraints */
    body, html {
        overflow-x: hidden;
        max-width: 100%;
    }
    
    /* Fix common Bootstrap container issues */
    .container, 
    .container-fluid,
    .container-lg,
    .container-xl,
    .container-xxl {
        max-width: 100%;
        overflow-x: hidden;
    }
    
    /* Fix any wrapper divs from parent template */
    #wrapper,
    .wrapper,
    .page-wrapper,
    .content-wrapper,
    .main-wrapper,
    main,
    .main-content {
        overflow-x: hidden !important;
        max-width: 100% !important;
    }
    
    /* Fix Bootstrap row negative margins */
    .row {
        margin-left: 0;
        margin-right: 0;
    }
    
    /* Progress bar constraints */
    .progress {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Card constraints - CRITICAL */
    .card {
        max-width: 100%;
        overflow: hidden;
        margin-bottom: 1rem;
    }
    
    /* CRITICAL: Force card body to contain table */
    .card-body.p-0 {
        overflow: hidden;
        max-width: 100%;
    }
    
    /* CRITICAL TABLE FIX */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-width: 100%;
        margin: 0;
    }
    
    /* CRITICAL: Contain the table */
    .table {
        margin-bottom: 0;
        width: 100%;
        max-width: 100%;
        table-layout: auto;
    }
    
    /* Table cells must respect container */
    .table th,
    .table td {
        word-wrap: break-word;
        word-break: break-word;
        overflow-wrap: break-word;
        vertical-align: middle;
        white-space: normal;
        padding: 0.5rem;
        max-width: 300px; /* Prevent any single cell from being too wide */
    }
    
    /* Product column specific */
    .table td:first-child {
        max-width: 250px;
    }
    
    .product-info {
        max-width: 100%;
        min-width: 0;
        overflow: hidden;
    }
    
    .product-title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.3;
        max-height: 2.6em;
        word-break: break-word;
    }
    
    /* Tags container */
    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Compact badges */
    .badge {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100px;
    }
    
    /* Compact buttons */
    .btn-sm {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        white-space: nowrap;
    }
    
    /* Modal constraints */
    .modal-dialog {
        max-width: min(95vw, 800px);
        margin: 1.75rem auto;
    }
    
    /* Responsive adjustments */
    @media (max-width: 991px) {
        .table {
            font-size: 0.875rem;
        }
        
        /* Hide less important columns on mobile */
        .table th:nth-child(3),
        .table td:nth-child(3) {
            display: none;
        }
        
        /* Smaller images on mobile */
        .product-info img,
        .product-info .bg-light {
            width: 40px !important;
            height: 40px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
    <h1 class="mb-2 mb-md-0"><i class="fas fa-box me-2"></i>Product SEO Optimization</h1>
    <button class="btn btn-primary" id="bulk-optimize-btn">
        <i class="fas fa-magic me-2"></i><span class="d-none d-sm-inline">Bulk Optimize All Products</span>
    </button>
</div>

<!-- Product Visibility Notice -->
<div class="alert alert-info mb-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Note:</strong> Only products that are <strong>available on your Online Store</strong> are shown here. 
    If you don't see a product you expect, please check:
    <ul class="mb-0 mt-2">
        <li>The product's status is "Active" in Shopify Admin</li>
        <li>The product is available on the "Online Store" sales channel</li>
        <li>Go to Shopify Admin → Products → Edit Product → Visibility → Select "Online Store"</li>
    </ul>
</div>

<!-- Optimization Stats -->
<div class="row mb-4">
    <div class="col-6 col-md-3 mb-3 mb-md-0">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-box fa-2x text-primary mb-2"></i>
                <h3 class="mb-0" id="total-products-count">{{ total_count if total_count is defined else products|length }}</h3>
                <small class="text-muted">Total Products</small>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3 mb-3 mb-md-0">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h3 class="mb-0 optimized-count">{{ optimized_count if optimized_count is defined else 0 }}</h3>
                <small class="text-muted">Optimized</small>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-edit fa-2x text-warning mb-2"></i>
                <h3 class="mb-0 ready-count">{{ ready_count if ready_count is defined else 0 }}</h3>
                <small class="text-muted">Ready to Apply</small>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3">
        <div class="card border-secondary">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-secondary mb-2"></i>
                <h3 class="mb-0 pending-count">{{ pending_count if pending_count is defined else total_count }}</h3>
                <small class="text-muted">Not Optimized</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Your Products</h5>
                    <span class="badge bg-light text-dark">{{ products|length }} Products</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th class="d-none d-md-table-cell">Status</th>
                                <th>SEO Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr id="product-row-{{ product.id }}" class="align-middle">
                                <td>
                                    <div class="d-flex align-items-start product-info">
                                        <div class="flex-shrink-0">
                                            {% if product.images %}
                                            <img src="{{ product.images[0].src }}" alt="{{ product.title }}" 
                                                 class="rounded me-2 border" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1" style="min-width: 0; overflow: hidden;">
                                            <strong class="d-block product-title small">{{ product.title }}</strong>
                                            <small class="text-muted d-block text-truncate" style="font-size: 0.75rem;">{{ product.vendor or 'No vendor' }}</small>
                                            {% if product.tags %}
                                            <div class="tags-container mt-1 d-none d-lg-flex">
                                                {% for tag in product.tags.split(',')[:2] %}
                                                <span class="badge bg-light text-dark">{{ tag.strip() }}</span>
                                                {% endfor %}
                                                {% if product.tags.split(',')|length > 2 %}
                                                <span class="text-muted" style="font-size: 0.65rem;">+{{ product.tags.split(',')|length - 2 }}</span>
                                                {% endif %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="text-nowrap">
                                    {% if product.variants %}
                                    <strong class="small">${{ "%.2f"|format(product.variants[0].price|float) }}</strong>
                                    {% else %}
                                    <span class="text-muted small">-</span>
                                    {% endif %}
                                </td>
                                <td class="d-none d-md-table-cell">
                                    {% if product.status == 'active' %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ product.status|title }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if optimization_statuses and optimization_statuses[product.id|string] %}
                                    <span class="badge bg-success seo-status" id="seo-status-{{ product.id }}">
                                        <i class="fas fa-check-circle me-1"></i><span class="d-none d-lg-inline">Optimized</span><span class="d-lg-none">Done</span>
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary seo-status" id="seo-status-{{ product.id }}">
                                        <i class="fas fa-clock me-1"></i><span class="d-none d-lg-inline">Not Optimized</span><span class="d-lg-none">Not Opt.</span>
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary optimize-btn" 
                                            data-product-id="{{ product.id }}"
                                            data-product-title="{{ product.title }}"
                                            data-product-description="{{ product.body_html }}"
                                            data-product-tags="{{ product.tags }}">
                                        <i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if not products %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No products found. Add products to your Shopify store first.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">AI SEO Assistant</h5>
                <p class="small text-muted">Powered by Claude 3.5 Sonnet</p>
                
                <div class="mb-3">
                    <label class="form-label">Target Keywords</label>
                    <input type="text" class="form-control" id="target-keywords" 
                           placeholder="e.g., organic cotton, sustainable fashion">
                    <small class="form-text">Separate multiple keywords with commas</small>
                </div>
                
                <div class="alert alert-info small">
                    <i class="fas fa-info-circle me-2"></i>
                    Claude will automatically search Google for top-ranking similar products and analyze their SEO strategies.
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">SEO Tips</h6>
                <ul class="small mb-0">
                    <li>Keep titles under 60 characters</li>
                    <li>Meta descriptions: 150-160 characters</li>
                    <li>Include primary keyword in title</li>
                    <li>Use structured data for products</li>
                    <li>Optimize images with alt text</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Modal -->
<div class="modal fade" id="optimizationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SEO Optimization Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="optimization-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Claude is analyzing and optimizing your product...</p>
                </div>
                <div id="optimization-results" style="display: none;">
                    <!-- Results will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="apply-optimization">Apply Changes</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('=== Product SEO JavaScript loaded ===');
// Global variables for async loading
let allProducts = [];
let totalProductCount = {{ total_count if total_count is defined else products|length }};
let loadedProductCount = {{ products|length }};
let isLoadingMore = false;
console.log('Total products:', totalProductCount, 'Loaded:', loadedProductCount);

// Function to update optimization stats
function updateOptimizationStats() {
    console.log('Updating optimization stats...');
    let optimizedCount = 0;
    let readyCount = 0;
    let pendingCount = 0;
    
    document.querySelectorAll('.seo-status').forEach(badge => {
        if (badge.textContent.includes('Optimized') && !badge.textContent.includes('Not Optimized')) {
            optimizedCount++;
        } else if (badge.textContent.includes('Optimization Ready')) {
            readyCount++;
        } else {
            pendingCount++;
        }
    });
    
    console.log('Count results - Optimized:', optimizedCount, 'Ready:', readyCount, 'Pending:', pendingCount);
    
    // Don't update the counts from JavaScript - use the server-provided values
    // The server already calculated these correctly
    // document.querySelector('.optimized-count').textContent = optimizedCount;
    // document.querySelector('.ready-count').textContent = readyCount;
    // document.querySelector('.pending-count').textContent = pendingCount;
}

// FIXED: Function to add product row to table
function addProductRow(product, isOptimized = false) {
    const tbody = document.querySelector('table tbody');
    const tr = document.createElement('tr');
    tr.id = `product-row-${product.id}`;
    tr.className = 'align-middle';
    
    // Escape and truncate product title to prevent overflow
    const escapeHtml = (str) => {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    };
    
    const truncateText = (text, maxLength) => {
        if (text && text.length > maxLength) {
            return text.substring(0, maxLength) + '...';
        }
        return text || '';
    };
    
    let imageHtml = '';
    if (product.images && product.images.length > 0) {
        imageHtml = `<img src="${product.images[0].src}" alt="${escapeHtml(product.title)}" 
                     class="rounded me-2 border" style="width: 50px; height: 50px; object-fit: cover;">`;
    } else {
        imageHtml = `<div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                     style="width: 50px; height: 50px;">
                    <i class="fas fa-image text-muted"></i>
                </div>`;
    }
    
    let priceHtml = '';
    if (product.variants && product.variants.length > 0) {
        priceHtml = `<strong class="small">$${parseFloat(product.variants[0].price).toFixed(2)}</strong>`;
    } else {
        priceHtml = '<span class="text-muted small">-</span>';
    }
    
    let tagsHtml = '';
    if (product.tags) {
        const tags = product.tags.split(',');
        const displayTags = tags.slice(0, 2).map(tag => 
            `<span class="badge bg-light text-dark">${escapeHtml(truncateText(tag.trim(), 15))}</span>`
        ).join('');
        const moreTags = tags.length > 2 ? 
            `<span class="text-muted" style="font-size: 0.65rem;">+${tags.length - 2}</span>` : '';
        tagsHtml = `<div class="tags-container mt-1 d-none d-lg-flex">${displayTags}${moreTags}</div>`;
    }
    
    // Create cells separately to ensure proper constraints
    const productCell = document.createElement('td');
    productCell.innerHTML = `
        <div class="d-flex align-items-start product-info">
            <div class="flex-shrink-0">
                ${imageHtml}
            </div>
            <div class="flex-grow-1" style="min-width: 0; overflow: hidden;">
                <strong class="d-block product-title small">${escapeHtml(product.title)}</strong>
                <small class="text-muted d-block text-truncate" style="font-size: 0.75rem;">${escapeHtml(product.vendor || 'No vendor')}</small>
                ${tagsHtml}
            </div>
        </div>
    `;
    
    const priceCell = document.createElement('td');
    priceCell.className = 'text-nowrap';
    priceCell.innerHTML = priceHtml;
    
    const statusCell = document.createElement('td');
    statusCell.className = 'd-none d-md-table-cell';
    statusCell.innerHTML = product.status === 'active' ? 
        '<span class="badge bg-success">Active</span>' : 
        `<span class="badge bg-secondary">${escapeHtml(product.status.charAt(0).toUpperCase() + product.status.slice(1))}</span>`;
    
    const seoStatusCell = document.createElement('td');
    if (isOptimized) {
        seoStatusCell.innerHTML = `
            <span class="badge bg-success seo-status" id="seo-status-${product.id}">
                <i class="fas fa-check-circle me-1"></i><span class="d-none d-lg-inline">Optimized</span><span class="d-lg-none">Done</span>
            </span>
        `;
    } else {
        seoStatusCell.innerHTML = `
            <span class="badge bg-secondary seo-status" id="seo-status-${product.id}">
                <i class="fas fa-clock me-1"></i><span class="d-none d-lg-inline">Not Optimized</span><span class="d-lg-none">Not Opt.</span>
            </span>
        `;
    }
    
    const actionsCell = document.createElement('td');
    actionsCell.innerHTML = `
        <button class="btn btn-sm btn-primary optimize-btn" 
                data-product-id="${product.id}"
                data-product-title="${escapeHtml(product.title || '')}"
                data-product-description="${escapeHtml(product.body_html || '')}"
                data-product-tags="${escapeHtml(product.tags || '')}">
            <i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>
        </button>
    `;
    
    // Append cells to row
    tr.appendChild(productCell);
    tr.appendChild(priceCell);
    tr.appendChild(statusCell);
    tr.appendChild(seoStatusCell);
    tr.appendChild(actionsCell);
    
    tbody.appendChild(tr);
    
    // Add event listener to the new optimize button
    const optimizeBtn = actionsCell.querySelector('.optimize-btn');
    optimizeBtn.addEventListener('click', handleOptimizeClick);
}

// Function to load more products
async function loadMoreProducts() {
    if (isLoadingMore || loadedProductCount >= totalProductCount) {
        return;
    }
    
    isLoadingMore = true;
    
    try {
        // Get total count first if we don't have it
        if (totalProductCount === loadedProductCount) {
            const countResponse = await fetch('/ai/api/products/count');
            const countData = await countResponse.json();
            if (countData.success) {
                totalProductCount = countData.count;
                document.getElementById('total-products-count').textContent = totalProductCount;
                document.querySelector('.pending-count').textContent = totalProductCount;
            }
        }
        
        let page = 1;
        let hasMore = true;
        
        while (hasMore && loadedProductCount < totalProductCount) {
            const response = await fetch(`/ai/api/products/batch?page=${page}&limit=250`);
            const data = await response.json();
            
            if (data.success && data.products) {
                // Skip products we already have
                const newProducts = data.products.filter(p => 
                    !document.getElementById(`product-row-${p.id}`)
                );
                
                newProducts.forEach(product => {
                    const isOptimized = data.optimization_statuses && data.optimization_statuses[product.id.toString()];
                    addProductRow(product, isOptimized);
                    loadedProductCount++;
                });
                
                hasMore = data.has_next;
                page++;
                
                // Small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                hasMore = false;
            }
        }
        
        // Update stats after loading
        updateOptimizationStats();
        
    } catch (error) {
        console.error('Error loading products:', error);
    } finally {
        isLoadingMore = false;
    }
}

// Handle optimize button click - now uses Celery tasks
async function handleOptimizeClick(event) {
    console.log('Optimize button clicked');
    
    const btn = event.currentTarget;
    const productId = btn.dataset.productId;
    const productTitle = btn.dataset.productTitle;
    const productDescription = btn.dataset.productDescription;
    const productTags = btn.dataset.productTags;
    
    console.log('Product details:', { productId, productTitle, productDescription });
    
    // Disable button and show processing state
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span class="d-none d-xl-inline ms-1">Processing</span>';
    
    // Update status badge to show processing
    const statusBadge = document.getElementById(`seo-status-${productId}`);
    if (statusBadge) {
        statusBadge.className = 'badge bg-primary seo-status';
        statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i><span class="d-none d-lg-inline">Processing</span><span class="d-lg-none">...</span>';
    }
    
    try {
        console.log('Submitting optimization task to Celery...');
        const response = await fetch('/ai/api/tasks/optimize-product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId,
                product_data: {
                    title: productTitle,
                    description: productDescription,
                    tags: productTags
                },
                keywords: document.getElementById('target-keywords').value
            })
        });
        
        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', data);
        
        if (data.success && data.task_id) {
            // Show notification
            showNotification(`Optimization task started for "${productTitle}". Check progress in the dashboard.`, 'info');
            
            // Poll for task status
            pollTaskStatus(data.task_id, productId);
            
        } else {
            // Reset button and badge
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>';
            
            if (statusBadge) {
                statusBadge.className = 'badge bg-secondary seo-status';
                statusBadge.innerHTML = '<i class="fas fa-clock me-1"></i><span class="d-none d-lg-inline">Not Optimized</span><span class="d-lg-none">Not Opt.</span>';
            }
            
            showNotification(data.error || 'Failed to start optimization task', 'error');
        }
    } catch (error) {
        console.error('Error starting optimization:', error);
        
        // Reset button and badge
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>';
        
        if (statusBadge) {
            statusBadge.className = 'badge bg-secondary seo-status';
            statusBadge.innerHTML = '<i class="fas fa-clock me-1"></i><span class="d-none d-lg-inline">Not Optimized</span><span class="d-lg-none">Not Opt.</span>';
        }
        
        showNotification('Error: ' + error.message, 'error');
    }
}

// Poll for task status
function pollTaskStatus(taskId, productId) {
    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/ai/api/tasks/status/${taskId}`);
            const data = await response.json();
            
            if (data.state === 'SUCCESS') {
                clearInterval(pollInterval);
                
                // Update UI to show success
                const statusBadge = document.getElementById(`seo-status-${productId}`);
                if (statusBadge) {
                    statusBadge.className = 'badge bg-success seo-status';
                    statusBadge.innerHTML = '<i class="fas fa-check-circle me-1"></i><span class="d-none d-lg-inline">Optimized</span><span class="d-lg-none">Done</span>';
                }
                
                // Re-enable button
                const btn = document.querySelector(`button[data-product-id="${productId}"]`);
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>';
                }
                
                // Update stats
                updateOptimizationStats();
                
                showNotification('Product optimization completed successfully!', 'success');
                
            } else if (data.state === 'FAILURE') {
                clearInterval(pollInterval);
                
                // Update UI to show failure
                const statusBadge = document.getElementById(`seo-status-${productId}`);
                if (statusBadge) {
                    statusBadge.className = 'badge bg-danger seo-status';
                    statusBadge.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i><span class="d-none d-lg-inline">Failed</span><span class="d-lg-none">Error</span>';
                }
                
                // Re-enable button
                const btn = document.querySelector(`button[data-product-id="${productId}"]`);
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i><span class="d-none d-xl-inline ms-1">Optimize</span>';
                }
                
                showNotification('Optimization failed: ' + (data.info || 'Unknown error'), 'error');
            }
            // If PENDING or PROGRESS, keep polling
            
        } catch (error) {
            console.error('Error polling task status:', error);
        }
    }, 2000); // Poll every 2 seconds
    
    // Stop polling after 5 minutes
    setTimeout(() => clearInterval(pollInterval), 300000);
}

// Show notification helper
function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => alertDiv.remove(), 5000);
}

// Initialize stats on page load and start async loading
window.addEventListener('load', function() {
    updateOptimizationStats();
    
    // Add event listeners to existing optimize buttons
    document.querySelectorAll('.optimize-btn').forEach(btn => {
        btn.addEventListener('click', handleOptimizeClick);
    });
    
    // FIXED: Delay loading to see if something else is interfering
    // and force a re-check of the table width after loading
    if (totalProductCount > loadedProductCount) {
        setTimeout(() => {
            loadMoreProducts().then(() => {
                // Force table to recalculate after all products are loaded
                const table = document.querySelector('.table');
                const tableResponsive = document.querySelector('.table-responsive');
                if (table && tableResponsive) {
                    table.style.width = '100%';
                    tableResponsive.style.maxWidth = '100%';
                    tableResponsive.style.overflow = 'auto';
                }
            });
        }, 1000);
    }
});

// Apply optimization button
document.getElementById('apply-optimization').addEventListener('click', async function() {
    if (!window.currentOptimization || !window.currentOptimization.data) {
        alert('No optimization data available to apply.');
        return;
    }
    
    const opt = window.currentOptimization.data;
    const productId = window.currentOptimization.productId;
    
    if (confirm(`This will update the product with:\n\nTitle: ${opt.optimized_title}\nDescription: ${opt.product_description}\nTags: ${opt.tags.join(', ')}\n\nContinue?`)) {
        try {
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('optimizationModal'));
            modal.hide();
            
            // Call Shopify API to update the product
            const response = await fetch('/ai/api/ai/update-product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    title: opt.optimized_title,
                    body_html: opt.product_description,
                    tags: opt.tags.join(', '),
                    metafields: {
                        meta_description: opt.meta_description,
                        h1_tag: opt.h1_tag
                    }
                })
            });
            
            if (response.ok) {
                // Update the SEO status badge in the table
                const statusBadge = document.getElementById(`seo-status-${productId}`);
                if (statusBadge) {
                    statusBadge.className = 'badge bg-success seo-status';
                    statusBadge.innerHTML = '<i class="fas fa-check-circle me-1"></i>Optimized';
                }
                
                // Update stats
                updateOptimizationStats();
                
                // Show success notification
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3';
                alertDiv.style.zIndex = '9999';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    Product SEO updated successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);
                
                // Auto-dismiss after 3 seconds
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
            } else {
                const error = await response.json();
                alert('Failed to update product: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            alert('Error updating product: ' + error.message);
        }
    }
});

// Bulk optimize - now uses Celery task
document.getElementById('bulk-optimize-btn').addEventListener('click', async function() {
    if (confirm('This will optimize all unoptimized products in the background. The process will run asynchronously and you can track progress in the dashboard. Continue?')) {
        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting...';
        
        try {
            const response = await fetch('/ai/api/tasks/optimize-all-products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    keywords: document.getElementById('target-keywords').value
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.task_id) {
                showNotification('Bulk optimization started! Check progress in the dashboard.', 'success');
                
                // Redirect to dashboard after a moment
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
            } else {
                showNotification(data.error || 'Failed to start bulk optimization', 'error');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic me-2"></i><span class="d-none d-sm-inline">Bulk Optimize All Products</span>';
            }
        } catch (error) {
            showNotification('Error: ' + error.message, 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-magic me-2"></i><span class="d-none d-sm-inline">Bulk Optimize All Products</span>';
        }
    }
});
</script>
{% endblock %}