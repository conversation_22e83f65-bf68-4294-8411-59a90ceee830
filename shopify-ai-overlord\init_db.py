#!/usr/bin/env python3
"""Initialize database and run migrations"""
import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate, upgrade
from config import Config

# Add project directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def init_database():
    """Initialize database and run migrations"""
    # Create Flask app
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db = SQLAlchemy(app)
    migrate = Migrate(app, db)
    
    with app.app_context():
        # Import models to ensure they're registered
        from app.models import (
            Shop, AIConfig, ChangeLog, OptimizationStatus, 
            HomepageSEO, TranslationStatus, AccessKey, UserSettings, AutomationJob, GoogleOAuth
        )
        
        # Create database directory if using SQLite
        database_uri = app.config['SQLALCHEMY_DATABASE_URI']
        if database_uri.startswith('sqlite:///'):
            db_path = database_uri.replace('sqlite:///', '')
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)
        
        # Run migrations
        print("Running database migrations...")
        try:
            # Upgrade to all heads to handle multiple migration branches
            upgrade(revision='heads')
            print("[OK] Database migrations completed successfully")
        except Exception as e:
            print(f"[ERROR] Error running migrations: {e}")
            # If migrations fail, try creating tables directly
            print("Attempting to create tables directly...")
            db.create_all()
            print("[OK] Tables created successfully")
        
        # Verify tables exist
        inspector = db.inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"\nDatabase tables: {', '.join(tables)}")
        
        return True

if __name__ == '__main__':
    if init_database():
        print("\n[OK] Database initialization complete!")
    else:
        print("\n[ERROR] Database initialization failed!")
        sys.exit(1)