version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:6543/postgres
      - REDIS_URL=rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE
      - SHOPIFY_APP_URL=http://localhost:5000
      - GOOGLE_OAUTH_REDIRECT_URI=http://localhost:5000/api/auth/callback/google
      - GOOGLE_CLIENT_ID=988316679046-ae2puc7vretbc2raedqpvju4ufmu2qpf.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=dev-secret-key-docker-local
      - ADMIN_USERNAME=${ADMIN_USERNAME:-localadmin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-LocalPass123!}
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/app/__pycache__
    command: python run.py
    depends_on:
      - worker
    restart: unless-stopped

  worker:
    build: .
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:6543/postgres
      - REDIS_URL=rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=dev-secret-key-docker-local
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/app/__pycache__
    command: celery -A celery_app.celery_app worker --loglevel=info --concurrency=1
    restart: unless-stopped

# Commented out for local development - uncomment for production testing
  # beat:
  #   build: .
  #   environment:
  #     - FLASK_ENV=development
  #     - DATABASE_URL=postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:6543/postgres
  #     - REDIS_URL=rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE
  #     - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
  #     - OPENAI_API_KEY=${OPENAI_API_KEY}
  #     - SECRET_KEY=dev-secret-key-docker-local
  #   volumes:
  #     - .:/app
  #     - /app/__pycache__
  #     - /app/app/__pycache__
  #   command: celery -A celery_app.celery_app beat --loglevel=info
  #   restart: unless-stopped
  #   depends_on:
  #     - worker