"""Add GoogleOAuth model for Search Console integration

Revision ID: 00aedd0947b6
Revises: 4c049229af9c
Create Date: 2025-05-31 14:16:43.389724

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '00aedd0947b6'
down_revision = '4c049229af9c'
branch_labels = None
depends_on = None


def upgrade():
    # Create google_oauth table
    op.create_table('google_oauth',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('shop_id', sa.Integer(), nullable=False),
        sa.Column('access_token', sa.Text(), nullable=False),
        sa.Column('refresh_token', sa.Text(), nullable=True),
        sa.Column('token_type', sa.String(length=50), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('google_email', sa.String(length=255), nullable=True),
        sa.Column('google_id', sa.String(length=255), nullable=True),
        sa.Column('scopes', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create index for quick shop lookups
    op.create_index('idx_shop_google', 'google_oauth', ['shop_id'], unique=False)


def downgrade():
    # Drop index and table
    op.drop_index('idx_shop_google', table_name='google_oauth')
    op.drop_table('google_oauth')
