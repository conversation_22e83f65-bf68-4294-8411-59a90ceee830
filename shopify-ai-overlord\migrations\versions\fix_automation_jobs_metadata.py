"""Fix automation_jobs metadata column

Revision ID: fix_automation_jobs_metadata
Revises: merged_heads
Create Date: 2025-05-29 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_automation_jobs_metadata'
down_revision = 'merged_heads'
branch_labels = None
depends_on = None


def upgrade():
    # Check if the table exists and if the column is missing
    try:
        # Add job_metadata column if it doesn't exist
        op.add_column('automation_jobs', sa.Column('job_metadata', sa.Text(), nullable=True))
    except:
        # Column might already exist
        pass


def downgrade():
    # Remove the column
    try:
        op.drop_column('automation_jobs', 'job_metadata')
    except:
        pass