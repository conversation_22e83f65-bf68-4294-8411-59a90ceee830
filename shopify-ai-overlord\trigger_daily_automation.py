#!/usr/bin/env python3
"""
Manually trigger daily automation tasks for testing
Run this to immediately queue daily automation without waiting for scheduled time
"""

import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.tasks import daily_automation_task
from app.models.ai_config import AIConfig
import time

print(f"\n{'='*60}")
print("MANUAL TRIGGER: Daily Automation Tasks")
print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print(f"{'='*60}\n")

app = create_app()

with app.app_context():
    # Get all shops with automation enabled
    configs = AIConfig.query.filter_by(module_type='seo_automation').all()
    
    print(f"Found {len(configs)} shops with seo_automation configuration")
    
    shops_queued = 0
    for config in configs:
        print(f"\nShop ID {config.shop_id} config data: {config.config_data}")
        if config.config_data and any([
            config.config_data.get('seo_enabled'),
            config.config_data.get('collection_enabled'),
            config.config_data.get('translation_enabled')
        ]):
            print(f"Queuing daily automation for shop ID: {config.shop_id}")

            # Queue the task with 20 minute delays between shops (matching the scheduled intervals)
            countdown_seconds = shops_queued * 1200  # 20 minutes * shop number
            result = daily_automation_task.apply_async(
                args=[config.shop_id],
                countdown=countdown_seconds
            )

            print(f"  Task ID: {result.id}")
            print(f"  Will start in: {countdown_seconds/60:.1f} minutes")
            print(f"  Status: QUEUED")
            print("-" * 40)

            shops_queued += 1
    
    if shops_queued == 0:
        print("No shops have automation enabled.")
        print("\nTo enable automation:")
        print("1. Go to http://127.0.0.1:5000/ai/seo/dashboard")
        print("2. Click Settings")
        print("3. Enable SEO, Collections, or Translation automation")
    else:
        print(f"\n✓ Queued daily automation for {shops_queued} shop(s)")
        print(f"Tasks will begin executing in 5 seconds...")
        print("\nCheck your Celery worker terminal to see the tasks execute!")

print(f"\n{'='*60}\n")