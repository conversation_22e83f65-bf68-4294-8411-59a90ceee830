"""
Daily Quota Service
Calculates and manages daily optimization limits based on store size
"""
import math
from datetime import datetime, timedelta
from flask import current_app
from app.models import Shop, AutomationJob
from app import db
from sqlalchemy import func


def calculate_daily_limits(sku_count: int,
                         target_days: int = None,
                         min_prod: int = None,
                         max_prod: int = None,
                         colls_per_prod: float = None,
                         max_colls: int = None):
    """
    Calculate daily product and collection limits based on SKU count.
    
    Args:
        sku_count: Total number of products in the store
        target_days: Days to complete full catalog (default from config)
        min_prod: Minimum products per day (default from config)
        max_prod: Maximum products per day (default from config)
        colls_per_prod: Average collections per product (default from config)
        max_colls: Maximum collections per day (default from config)
    
    Returns:
        tuple: (products_per_day, collections_per_day)
    """
    # Use config defaults if not provided
    if target_days is None:
        target_days = current_app.config['TARGET_COMPLETION_DAYS']
    if min_prod is None:
        min_prod = current_app.config['MIN_PRODUCTS_PER_DAY']
    if max_prod is None:
        max_prod = current_app.config['MAX_PRODUCTS_PER_DAY']
    if colls_per_prod is None:
        colls_per_prod = current_app.config['COLLS_PER_PRODUCT']
    if max_colls is None:
        max_colls = current_app.config['MAX_COLLECTIONS_PER_DAY']
    
    # Calculate products per day
    products = math.ceil(sku_count / target_days)
    products = max(products, min_prod)
    products = min(products, max_prod)
    
    # Calculate collections per day
    collections = math.ceil(products * colls_per_prod)
    collections = min(collections, max_colls)
    collections = max(1, collections)
    
    return products, collections


def get_today_usage(shop_id: int):
    """
    Get today's usage statistics for products and collections.
    
    Args:
        shop_id: The shop ID
    
    Returns:
        dict: Usage statistics including completed, failed, and remaining quotas
    """
    today = datetime.utcnow().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = today_start + timedelta(days=1)
    
    # Get today's completed jobs
    product_jobs = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['product_seo', 'bulk_product_seo']),
        AutomationJob.created_at >= today_start,
        AutomationJob.created_at < today_end
    ).all()
    
    collection_jobs = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['create_collection', 'bulk_create_collections']),
        AutomationJob.created_at >= today_start,
        AutomationJob.created_at < today_end
    ).all()
    
    # Count completed and failed
    products_completed = sum(1 for job in product_jobs if job.status == 'completed')
    products_failed = sum(1 for job in product_jobs if job.status == 'failed')
    products_processing = sum(1 for job in product_jobs if job.status in ['pending', 'processing'])
    
    collections_completed = sum(1 for job in collection_jobs if job.status == 'completed')
    collections_failed = sum(1 for job in collection_jobs if job.status == 'failed')
    collections_processing = sum(1 for job in collection_jobs if job.status in ['pending', 'processing'])
    
    # Extract actual counts from job results
    products_optimized = 0
    collections_created = 0
    
    for job in product_jobs:
        if job.status == 'completed' and job.result_data:
            try:
                result = job.get_result()
                products_optimized += result.get('optimized', 0)
            except:
                products_optimized += 1
    
    for job in collection_jobs:
        if job.status == 'completed' and job.result_data:
            try:
                result = job.get_result()
                collections_created += result.get('created', 0)
            except:
                collections_created += 1
    
    return {
        'products': {
            'completed': products_completed,
            'optimized': products_optimized,
            'failed': products_failed,
            'processing': products_processing,
            'total': len(product_jobs)
        },
        'collections': {
            'completed': collections_completed,
            'created': collections_created,
            'failed': collections_failed,
            'processing': collections_processing,
            'total': len(collection_jobs)
        }
    }


def get_yesterday_deficit(shop_id: int):
    """
    Calculate yesterday's failed/incomplete jobs that need to be added to today's queue.
    
    Args:
        shop_id: The shop ID
    
    Returns:
        dict: Deficit counts for products and collections
    """
    yesterday = datetime.utcnow().date() - timedelta(days=1)
    yesterday_start = datetime.combine(yesterday, datetime.min.time())
    yesterday_end = yesterday_start + timedelta(days=1)
    
    # Get yesterday's failed jobs
    failed_products = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['product_seo', 'bulk_product_seo']),
        AutomationJob.status == 'failed',
        AutomationJob.created_at >= yesterday_start,
        AutomationJob.created_at < yesterday_end
    ).count()
    
    failed_collections = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['create_collection', 'bulk_create_collections']),
        AutomationJob.status == 'failed',
        AutomationJob.created_at >= yesterday_start,
        AutomationJob.created_at < yesterday_end
    ).count()
    
    return {
        'products': failed_products,
        'collections': failed_collections
    }


def calculate_quota_with_deficit(shop_id: int, total_products: int):
    """
    Calculate today's quota including yesterday's deficit.
    
    Args:
        shop_id: The shop ID
        total_products: Total product count in store
    
    Returns:
        dict: Complete quota information including limits, usage, and remaining
    """
    # Get base daily limits
    products_limit, collections_limit = calculate_daily_limits(total_products)
    
    # Get today's usage
    usage = get_today_usage(shop_id)
    
    # Get yesterday's deficit
    deficit = get_yesterday_deficit(shop_id)
    
    # Calculate effective limits (base + deficit, but capped at max)
    effective_product_limit = min(
        products_limit + deficit['products'],
        current_app.config['MAX_PRODUCTS_PER_DAY']
    )
    effective_collection_limit = min(
        collections_limit + deficit['collections'],
        current_app.config['MAX_COLLECTIONS_PER_DAY']
    )
    
    # Calculate remaining quota
    products_remaining = max(0, effective_product_limit - usage['products']['optimized'])
    collections_remaining = max(0, effective_collection_limit - usage['collections']['created'])
    
    # Calculate ETA
    products_left = total_products - _get_total_optimized(shop_id)
    days_remaining = math.ceil(products_left / products_limit) if products_limit > 0 else 0
    
    return {
        'base_limits': {
            'products': products_limit,
            'collections': collections_limit
        },
        'deficit': deficit,
        'effective_limits': {
            'products': effective_product_limit,
            'collections': effective_collection_limit
        },
        'usage': usage,
        'remaining': {
            'products': products_remaining,
            'collections': collections_remaining
        },
        'progress': {
            'products_percentage': (usage['products']['optimized'] / effective_product_limit * 100) if effective_product_limit > 0 else 0,
            'collections_percentage': (usage['collections']['created'] / effective_collection_limit * 100) if effective_collection_limit > 0 else 0
        },
        'eta_days': days_remaining,
        'completion_date': (datetime.utcnow() + timedelta(days=days_remaining)).strftime('%Y-%m-%d')
    }


def _get_total_optimized(shop_id: int):
    """Get total number of products optimized for a shop."""
    completed_jobs = AutomationJob.query.filter(
        AutomationJob.shop_id == shop_id,
        AutomationJob.task_type.in_(['product_seo', 'bulk_product_seo']),
        AutomationJob.status == 'completed'
    ).all()
    
    total_optimized = 0
    for job in completed_jobs:
        if job.result_data:
            try:
                result = job.get_result()
                total_optimized += result.get('optimized', 0)
            except:
                total_optimized += 1
    
    return total_optimized


def check_quota_available(shop_id: int, resource_type: str, count: int = 1):
    """
    Check if quota is available for the requested operation.
    
    Args:
        shop_id: The shop ID
        resource_type: 'products' or 'collections'
        count: Number of items to process
    
    Returns:
        tuple: (is_available: bool, remaining: int, message: str)
    """
    shop = Shop.query.get(shop_id)
    if not shop:
        return False, 0, "Shop not found"
    
    # Get total products from Shopify
    from app.services.shopify_service import ShopifyService
    shop_domain = shop.shopify_shop_domain or shop.shop_domain
    if shop_domain and shop_domain != 'None' and shop_domain != 'Not configured':
        shopify_service = ShopifyService(shop_domain, shop.shopify_access_token)
        try:
            total_products = shopify_service.get_products_count(published_status='published')
        except:
            total_products = 0
    else:
        total_products = 0
    
    # Get quota information
    quota_info = calculate_quota_with_deficit(shop_id, total_products)
    
    if resource_type == 'products':
        remaining = quota_info['remaining']['products']
        limit = quota_info['effective_limits']['products']
    elif resource_type == 'collections':
        remaining = quota_info['remaining']['collections']
        limit = quota_info['effective_limits']['collections']
    else:
        return False, 0, "Invalid resource type"
    
    if remaining >= count:
        return True, remaining - count, f"Quota available: {remaining} remaining"
    else:
        return False, remaining, f"Quota exceeded: {remaining} remaining out of {limit} daily limit"