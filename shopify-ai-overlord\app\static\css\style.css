/* Sidebar styles */
.sidebar {
    width: 280px;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    padding-top: 20px;
}

main {
    margin-left: 280px;
}

.sidebar .nav-link {
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Module toggle switches */
.form-check-input.module-toggle {
    width: 3em;
    height: 1.5em;
    cursor: pointer;
}

/* Card hover effects */
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    main {
        margin-left: 0;
    }
}