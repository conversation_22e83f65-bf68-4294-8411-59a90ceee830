{% extends "base.html" %}

{% block title %}Settings - Shopify AI Control{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-cog text-primary"></i> Settings
            </h1>
            <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key"></i> API Keys Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.settings') }}">
                    
                    <div class="mb-4">
                        <h6 class="text-muted mb-3">AI Configuration</h6>
                        <div class="mb-3">
                            <label for="anthropic_api_key" class="form-label">Anthropic API Key</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-robot"></i></span>
                                <input type="password" class="form-control" id="anthropic_api_key" name="anthropic_api_key" 
                                       value="{{ settings.anthropic_api_key or '' }}" placeholder="sk-ant-...">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleVisibility('anthropic_api_key')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Used for Claude AI-powered content generation</small>
                        </div>
                        <div class="mb-3">
                            <label for="openai_api_key" class="form-label">OpenAI API Key</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-magic"></i></span>
                                <input type="password" class="form-control" id="openai_api_key" name="openai_api_key" 
                                       value="{{ settings.openai_api_key or '' }}" placeholder="sk-proj-...">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleVisibility('openai_api_key')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Used for technical SEO features like alt text optimization (GPT-4o-mini)</small>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-muted mb-3">Shopify Configuration</h6>
                        <div class="mb-3">
                            <label for="shopify_shop_domain" class="form-label">Shopify Shop Domain</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-store"></i></span>
                                <input type="text" class="form-control" id="shopify_shop_domain" name="shopify_shop_domain" 
                                       value="{{ settings.shopify_shop_domain or '' }}" placeholder="your-store.myshopify.com">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="store_actual_live_url" class="form-label">Store Live URL</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                <input type="text" class="form-control" id="store_actual_live_url" name="store_actual_live_url" 
                                       value="{{ settings.store_actual_live_url or '' }}" placeholder="https://www.yourdomain.com">
                            </div>
                            <small class="text-muted">Your store's actual domain (used for Search Console). Include https://</small>
                        </div>
                        <div class="mb-3">
                            <label for="shopify_access_token" class="form-label">Shopify Access Token</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" class="form-control" id="shopify_access_token" name="shopify_access_token" 
                                       value="{{ settings.shopify_access_token or '' }}" placeholder="shpat_...">
                                <button class="btn btn-outline-secondary" type="button" onclick="toggleVisibility('shopify_access_token')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Admin API access token with read/write permissions</small>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Help
                </h5>
            </div>
            <div class="card-body">
                <h6>Where to find your API keys:</h6>
                <ul class="small">
                    <li><strong>Anthropic:</strong> <a href="https://console.anthropic.com/account/keys" target="_blank">Anthropic Console</a></li>
                    <li><strong>Shopify:</strong> Your Shopify Admin → Apps → Manage private apps</li>
                </ul>
                <div class="alert alert-warning small mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Keep your API keys secure and never share them.
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle"></i> Account Info
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2"><strong>Email:</strong> {{ session.get('user_email', 'Not set') }}</p>
                <p class="mb-0"><strong>Access Key:</strong> <code>{{ session.get('access_key_id', '')[:8] }}...</code></p>
            </div>
        </div>
    </div>
</div>

<script>
function toggleVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}