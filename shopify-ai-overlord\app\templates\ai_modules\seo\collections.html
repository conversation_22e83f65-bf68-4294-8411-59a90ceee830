{% extends "base_auth.html" %}

{% block title %}Collection Maker - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    /* Ensure page doesn't overflow horizontally */
    body {
        overflow-x: hidden;
    }
    
    /* Constrain progress bar to container */
    .progress {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Fix any potential card overflow */
    .card {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Ensure modals don't cause overflow */
    .modal-dialog {
        max-width: 95vw;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-layer-group me-2"></i>AI Collection Maker</h1>
    <div>
        <button class="btn btn-success me-2" id="create-all-collections">
            <i class="fas fa-magic me-2"></i>Create All Collections
        </button>
        <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-outline-primary">
            <i class="fas fa-box me-2"></i>Optimize Products First
        </a>
    </div>
</div>

<!-- Product Visibility Notice -->
<div class="alert alert-info mb-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Note:</strong> Collections are created based on tags from products that are <strong>available on your Online Store</strong>. 
    Tags from products not visible on the online store channel are not included.
</div>


<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                <h3 class="mb-0" id="unique-tags-count">{{ tag_data|length }}</h3>
                <small class="text-muted">Unique Tags</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-box fa-2x text-info mb-2"></i>
                <h3 class="mb-0" id="total-products-count">{{ total_products }}</h3>
                <small class="text-muted">Total Products</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h3 class="mb-0 collections-created">0</h3>
                <small class="text-muted">Collections Created</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-layer-group fa-2x text-warning mb-2"></i>
                <h3 class="mb-0" id="existing-collections-count">{{ total_collections if total_collections is defined else existing_collections|length }}</h3>
                <small class="text-muted">Existing Collections</small>
            </div>
        </div>
    </div>
</div>

{% if no_new_tags %}
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>No AI-generated tags found!</strong> 
    <br>The Collection Maker only uses tags that were generated by the AI Product Optimizer to ensure quality and relevance.
    <br>Total tags in your products: {{ all_tags_count }} (but these are existing tags, not AI-generated)
    <br><br>
    <a href="{{ url_for('ai_modules.seo_products') }}" class="btn btn-primary">
        <i class="fas fa-box me-2"></i>Go to Product Optimizer
    </a>
</div>
{% elif not tag_data %}
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>No tags found!</strong> Please optimize your products first to generate tags that can be used for collections.
    <a href="{{ url_for('ai_modules.seo_products') }}" class="alert-link">Go to Product Optimizer</a>
</div>
{% else %}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>AI-Generated Tags (From Product Optimizer)</h5>
                    <span class="badge bg-light text-dark">{{ tag_data|length }} Tags</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                    <table class="table table-hover mb-0">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="select-all-tags" class="form-check-input">
                                </th>
                                <th width="35%">Tag Name</th>
                                <th width="15%">Products</th>
                                <th width="20%">Status</th>
                                <th width="25%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tag, count in tag_data %}
                            <tr id="tag-row-{{ loop.index }}" class="align-middle">
                                <td>
                                    <input type="checkbox" class="tag-checkbox form-check-input" 
                                           data-tag="{{ tag }}" data-count="{{ count }}">
                                </td>
                                <td>
                                    <strong>{{ tag }}</strong>
                                    <br>
                                    <small class="text-muted">Handle: {{ tag|lower|replace(' ', '-')|replace('_', '-') }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ count }} products</span>
                                </td>
                                <td>
                                    {% if tag|lower|replace(' ', '-')|replace('_', '-') in existing_handles %}
                                    <span class="badge bg-success collection-status">
                                        <i class="fas fa-check-circle me-1"></i>Collection Exists
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary collection-status" id="status-{{ loop.index }}">
                                        <i class="fas fa-clock me-1"></i>Not Created
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if tag|lower|replace(' ', '-')|replace('_', '-') not in existing_handles %}
                                    <button class="btn btn-sm btn-primary create-collection-btn" 
                                            data-tag="{{ tag }}" 
                                            data-count="{{ count }}"
                                            data-index="{{ loop.index }}">
                                        <i class="fas fa-plus-circle me-1"></i>Create Collection
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-outline-secondary" disabled>
                                        <i class="fas fa-check me-1"></i>Already Exists
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" id="create-selected-collections">
                    <i class="fas fa-layer-group me-2"></i>Create Selected Collections
                </button>
                <span class="text-muted ms-3">
                    <span id="selected-count">0</span> tags selected
                </span>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">AI Collection Creator</h5>
                <p class="small text-muted">Powered by Claude Sonnet 4</p>
                
                <div class="alert alert-info small">
                    <i class="fas fa-info-circle me-2"></i>
                    Claude will search for similar collections online and create SEO-optimized:
                    <ul class="mb-0 mt-2">
                        <li>Collection titles</li>
                        <li>Descriptions</li>
                        <li>Meta descriptions</li>
                        <li>Smart collection rules</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Collection Type</label>
                    <select class="form-select" id="collection-type">
                        <option value="smart">Smart Collections (Auto-update)</option>
                        <option value="custom">Custom Collections (Manual)</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">SEO Focus Keywords</label>
                    <input type="text" class="form-control" id="seo-keywords" 
                           placeholder="e.g., sustainable, organic, premium">
                    <small class="form-text">Optional: Add focus keywords for all collections</small>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Collection Best Practices</h6>
                <ul class="small mb-0">
                    <li>Use descriptive collection names</li>
                    <li>Keep titles under 60 characters</li>
                    <li>Write unique descriptions for each</li>
                    <li>Include relevant keywords naturally</li>
                    <li>Use smart collections for dynamic updates</li>
                </ul>
            </div>
        </div>
    </div>
</div>

{% endif %}

<!-- Collection Creation Modal -->
<div class="modal fade" id="collectionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Collection Creation Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="collection-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Creating SEO-optimized collection with Claude...</p>
                </div>
                <div id="collection-results" style="display: none;">
                    <!-- Results will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="apply-collection" style="display: none;">Create Collection</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Store current collection data
let currentCollectionData = null;
let collectionModal = null;

// Global variables for async loading
let totalProductCount = {{ total_products }};
let loadedProductCount = 250; // We always start with first 250 products
let totalCollectionCount = {{ total_collections if total_collections is defined else existing_collections|length }};
let isLoadingMore = false;
let allNewGeneratedTags = new Set();
let tagCounts = {};
let allTagCounts = {};

// Initialize new generated tags from server data
{% for tag, count in tag_data %}
allNewGeneratedTags.add('{{ tag }}');
tagCounts['{{ tag }}'] = {{ count }};
{% endfor %}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    collectionModal = new bootstrap.Modal(document.getElementById('collectionModal'));
    updateSelectedCount();
    
    // Start loading more products if needed
    if (totalProductCount > loadedProductCount) {
        setTimeout(() => {
            loadMoreProductsForTags();
        }, 1000);
    }
});

// Select all checkbox
const selectAllCheckbox = document.getElementById('select-all-tags');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.tag-checkbox');
        checkboxes.forEach(cb => {
            if (!cb.closest('tr').querySelector('.collection-status').textContent.includes('Exists')) {
                cb.checked = this.checked;
            }
        });
        updateSelectedCount();
    });
}

// Update selected count
document.querySelectorAll('.tag-checkbox').forEach(cb => {
    cb.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const count = document.querySelectorAll('.tag-checkbox:checked').length;
    document.getElementById('selected-count').textContent = count;
}

// Function to load more products and analyze tags
async function loadMoreProductsForTags() {
    if (isLoadingMore || loadedProductCount >= totalProductCount) {
        return;
    }
    
    isLoadingMore = true;
    // Progress bar removed
    
    try {
        let page = 2; // Start from page 2 since we already have page 1
        let hasMore = true;
        
        while (hasMore && loadedProductCount < totalProductCount) {
            // Progress text removed
            
            const response = await fetch(`/ai/api/products/batch?page=${page}&limit=250`);
            const data = await response.json();
            
            if (data.success && data.products) {
                // Analyze tags from new products
                data.products.forEach(product => {
                    if (product.tags) {
                        const tags = product.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                        tags.forEach(tag => {
                            allTagCounts[tag] = (allTagCounts[tag] || 0) + 1;
                            // Only count if it's a newly generated tag
                            if (allNewGeneratedTags.has(tag)) {
                                tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                            }
                        });
                    }
                    loadedProductCount++;
                });
                
                // Progress bar removed
                
                hasMore = data.has_next;
                page++;
                
                // Small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                hasMore = false;
            }
        }
        
        // Update the tag counts in the UI
        updateTagCountsInUI();
        
    } catch (error) {
        console.error('Error loading products for tag analysis:', error);
        console.error('Error loading products for tag analysis:', error);
    } finally {
        isLoadingMore = false;
    }
}

// Function to update tag counts in the UI after async loading
function updateTagCountsInUI() {
    // Update each tag row with new counts
    document.querySelectorAll('.tag-checkbox').forEach(checkbox => {
        const tag = checkbox.dataset.tag;
        if (tagCounts[tag]) {
            const row = checkbox.closest('tr');
            const countBadge = row.querySelector('.badge.bg-info');
            if (countBadge) {
                countBadge.textContent = `${tagCounts[tag]} products`;
            }
            // Update the data attribute as well
            checkbox.dataset.count = tagCounts[tag];
        }
    });
    
    // Update the unique tags count
    document.getElementById('unique-tags-count').textContent = Object.keys(tagCounts).length;
}

// Create individual collection
document.querySelectorAll('.create-collection-btn').forEach(btn => {
    btn.addEventListener('click', async function() {
        const tag = this.dataset.tag;
        const count = this.dataset.count;
        const index = this.dataset.index;
        
        await createCollection(tag, count, index);
    });
});

// Create selected collections
const createSelectedBtn = document.getElementById('create-selected-collections');
if (createSelectedBtn) {
    createSelectedBtn.addEventListener('click', async function() {
        const selected = document.querySelectorAll('.tag-checkbox:checked');
        if (selected.length === 0) {
            alert('Please select at least one tag to create collections.');
            return;
        }
        
        if (!confirm(`Create ${selected.length} collections?`)) {
            return;
        }
        
        // Process each selected tag
        for (const checkbox of selected) {
            const tag = checkbox.dataset.tag;
            const count = checkbox.dataset.count;
            const row = checkbox.closest('tr');
            const index = row.id.split('-')[2];
            
            await createCollection(tag, count, index, false);
            
            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        alert(`Successfully processed ${selected.length} collections!`);
    });
}

// Create all collections - now uses bulk Celery task
const createAllBtn = document.getElementById('create-all-collections');
if (createAllBtn) {
    createAllBtn.addEventListener('click', async function() {
        const allButtons = document.querySelectorAll('.create-collection-btn:not([disabled])');
        if (allButtons.length === 0) {
            alert('No new collections to create!');
            return;
        }
        
        if (!confirm(`Create ${allButtons.length} collections in the background? You can track progress in the dashboard.`)) {
            return;
        }
        
        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting...';
        
        // Collect all tags data
        const tagsData = [];
        allButtons.forEach(btn => {
            tagsData.push({
                tag: btn.dataset.tag,
                count: parseInt(btn.dataset.count)
            });
        });
        
        try {
            const collectionType = document.getElementById('collection-type').value;
            const seoKeywords = document.getElementById('seo-keywords').value;
            
            const response = await fetch('/ai/api/tasks/create-all-collections', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tags_data: tagsData,
                    collection_type: collectionType,
                    seo_keywords: seoKeywords
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.task_id) {
                showNotification('Bulk collection creation started! Check progress in the dashboard.', 'success');
                
                // Redirect to dashboard after a moment
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
            } else {
                showNotification(data.error || 'Failed to start bulk collection creation', 'error');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic me-2"></i>Create All Collections';
            }
        } catch (error) {
            showNotification('Error: ' + error.message, 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-magic me-2"></i>Create All Collections';
        }
    });
}

// Create collection function - now uses Celery tasks
async function createCollection(tag, productCount, index, showModal = true) {
    const collectionTypeEl = document.getElementById('collection-type');
    const seoKeywordsEl = document.getElementById('seo-keywords');
    const collectionType = collectionTypeEl ? collectionTypeEl.value : 'smart';
    const seoKeywords = seoKeywordsEl ? seoKeywordsEl.value : '';
    
    // Update status in table
    const statusBadge = document.getElementById(`status-${index}`);
    if (statusBadge) {
        statusBadge.className = 'badge bg-primary collection-status';
        statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    }
    
    // Update button
    const btn = document.querySelector(`[data-index="${index}"]`);
    if (btn) {
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing';
    }
    
    try {
        // Submit collection creation task to Celery
        const response = await fetch('/ai/api/tasks/create-collection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                tag: tag,
                product_count: productCount,
                collection_type: collectionType,
                seo_keywords: seoKeywords
            })
        });
        
        const data = await response.json();
        
        if (data.success && data.task_id) {
            showNotification(`Collection creation task started for "${tag}". Check progress in the dashboard.`, 'info');
            
            // Poll for task status
            pollCollectionTaskStatus(data.task_id, tag, index);
            
        } else {
            throw new Error(data.error || 'Failed to start collection creation task');
        }
    } catch (error) {
        // Update status badge
        if (statusBadge) {
            statusBadge.className = 'badge bg-danger collection-status';
            statusBadge.innerHTML = '<i class="fas fa-times-circle me-1"></i>Error';
        }
        
        // Re-enable button
        if (btn) {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-plus-circle me-1"></i>Create Collection';
        }
        
        showNotification(`Error: ${error.message}`, 'danger');
    }
}

// Poll for collection task status
function pollCollectionTaskStatus(taskId, tag, index) {
    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/ai/api/tasks/status/${taskId}`);
            const data = await response.json();
            
            if (data.state === 'SUCCESS') {
                clearInterval(pollInterval);
                
                // Update UI to show success
                const statusBadge = document.getElementById(`status-${index}`);
                if (statusBadge) {
                    statusBadge.className = 'badge bg-success collection-status';
                    statusBadge.innerHTML = '<i class="fas fa-check-circle me-1"></i>Created';
                }
                
                // Update button
                const btn = document.querySelector(`[data-index="${index}"]`);
                if (btn) {
                    btn.disabled = true;
                    btn.className = 'btn btn-sm btn-outline-secondary';
                    btn.innerHTML = '<i class="fas fa-check me-1"></i>Created';
                }
                
                // Update stats
                updateCollectionStats();
                
                showNotification(`Collection "${tag}" created successfully!`, 'success');
                
            } else if (data.state === 'FAILURE') {
                clearInterval(pollInterval);
                
                // Update UI to show failure
                const statusBadge = document.getElementById(`status-${index}`);
                if (statusBadge) {
                    statusBadge.className = 'badge bg-danger collection-status';
                    statusBadge.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i>Failed';
                }
                
                // Re-enable button
                const btn = document.querySelector(`[data-index="${index}"]`);
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-plus-circle me-1"></i>Create Collection';
                }
                
                showNotification('Collection creation failed: ' + (data.info || 'Unknown error'), 'error');
            }
            // If PENDING or PROGRESS, keep polling
            
        } catch (error) {
            console.error('Error polling task status:', error);
        }
    }, 2000); // Poll every 2 seconds
    
    // Stop polling after 5 minutes
    setTimeout(() => clearInterval(pollInterval), 300000);
}

// Apply collection creation - removed since we're using background tasks

function updateCollectionStats() {
    const createdCount = document.querySelectorAll('.collection-status:not(.bg-secondary):not(.bg-danger)').length;
    document.querySelector('.collections-created').textContent = createdCount;
}

function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => alertDiv.remove(), 3000);
}

// Initialize stats
updateCollectionStats();
</script>
{% endblock %}