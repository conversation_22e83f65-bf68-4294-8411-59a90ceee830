{% extends "base_auth.html" %}

{% block title %}Translation Status - Shopify AI Control{% endblock %}

{% block extra_css %}
<style>
    /* Container fixes */
    body, html {
        overflow-x: hidden;
        max-width: 100%;
    }
    
    /* Language card styles */
    .language-card {
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.2s ease;
        background-color: white;
    }
    
    .language-card.active {
        border-color: #3b82f6;
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
    }
    
    .language-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    /* Language header */
    .language-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .language-info {
        display: flex;
        align-items: center;
    }
    
    .language-details h5 {
        margin: 0;
        font-weight: 600;
    }
    
    .language-code {
        color: #6b7280;
        font-size: 0.875rem;
    }
    
    /* Status badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        padding: 0.35rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }
    
    .status-enabled {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-disabled {
        background-color: #f3f4f6;
        color: #374151;
    }
    
    /* Progress section */
    .progress-section {
        background-color: #f9fafb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .progress-bar-custom {
        height: 0.75rem;
        background-color: #e5e7eb;
        border-radius: 0.375rem;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #3b82f6;
        transition: width 0.3s ease;
    }
    
    /* Translation stats */
    .translation-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #111827;
    }
    
    .stat-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }
    
    /* Resource type tabs */
    .resource-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .resource-tab {
        padding: 0.75rem 1.5rem;
        background-color: transparent;
        border: none;
        color: #6b7280;
        font-weight: 500;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
    }
    
    .resource-tab:hover {
        color: #3b82f6;
    }
    
    .resource-tab.active {
        color: #3b82f6;
    }
    
    .resource-tab.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #3b82f6;
    }
    
    /* Translation item */
    .translation-item {
        padding: 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        background-color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .translation-item.translated {
        background-color: #f0fdf4;
        border-color: #86efac;
    }
    
    .item-info {
        flex-grow: 1;
    }
    
    .item-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .item-meta {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .translation-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    
    .status-icon {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .status-icon.success {
        background-color: #10b981;
        color: white;
    }
    
    .status-icon.pending {
        background-color: #6b7280;
        color: white;
    }
    
    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    /* Overall progress card */
    .overall-progress {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .overall-progress h4 {
        margin-bottom: 1.5rem;
    }
    
    .progress-ring {
        display: inline-block;
        position: relative;
    }
    
    .progress-ring-circle {
        transition: stroke-dashoffset 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }
    
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="mb-2 mb-md-0">
            <i class="fas fa-globe me-2"></i>Translation Status
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-primary">
                <i class="fas fa-tachometer-alt me-2"></i>SEO Dashboard
            </a>
            <button class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Info Banner -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Translation Status Monitor</strong> - This page shows the translation status of your content. 
        To run translations, use the <a href="{{ url_for('ai_modules.seo_dashboard') }}">SEO Dashboard</a> automation controls.
    </div>

    <!-- Overall Progress Card -->
    <div class="overall-progress">
        <h4><i class="fas fa-chart-line me-2"></i>Overall Translation Progress</h4>
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="row">
                    <div class="col-6 col-md-3 text-center mb-3">
                        <div class="stat-value">{{ total_resources }}</div>
                        <div class="stat-label">Total Items</div>
                    </div>
                    <div class="col-6 col-md-3 text-center mb-3">
                        <div class="stat-value">{{ total_translated }}</div>
                        <div class="stat-label">Translated</div>
                    </div>
                    <div class="col-6 col-md-3 text-center mb-3">
                        <div class="stat-value">{{ languages_active }}</div>
                        <div class="stat-label">Languages</div>
                    </div>
                    <div class="col-6 col-md-3 text-center mb-3">
                        <div class="stat-value">{{ ((total_translated / (total_resources * languages_active) * 100) if (total_resources > 0 and languages_active > 0) else 0)|round|int }}%</div>
                        <div class="stat-label">Complete</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <svg class="progress-ring" width="120" height="120">
                    <circle class="progress-ring-circle" stroke="rgba(255,255,255,0.3)" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"></circle>
                    <circle class="progress-ring-circle" stroke="white" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                            style="stroke-dasharray: {{ 52 * 2 * 3.14159 }} {{ 52 * 2 * 3.14159 }};
                                   stroke-dashoffset: {{ 52 * 2 * 3.14159 * (1 - (total_translated / (total_resources * languages_active) if (total_resources > 0 and languages_active > 0) else 0)) }}">
                    </circle>
                </svg>
                <div class="mt-2">
                    <strong>{{ total_translated }}</strong> / {{ total_resources * languages_active if languages_active > 0 else 0 }}
                </div>
            </div>
        </div>
    </div>

    <!-- Languages Section -->
    <h5 class="mb-3">Translation Status by Language</h5>
    
    {% if shop_locales %}
    <div class="row">
        {% for locale in shop_locales %}
        <div class="col-lg-6">
            <div class="language-card {{ 'active' if locale.published else '' }}">
                <div class="language-header">
                    <div class="language-info">
                        <div class="language-details">
                            <h5>{{ locale.name }}</h5>
                            <span class="language-code">{{ locale.locale }}</span>
                        </div>
                    </div>
                    <span class="status-badge {{ 'status-enabled' if locale.published else 'status-disabled' }}">
                        <i class="fas fa-{{ 'check' if locale.published else 'times' }}-circle"></i>
                        {{ 'Enabled' if locale.published else 'Disabled' }}
                    </span>
                </div>
                
                {% set lang_stats = translation_stats.get(locale.locale, {}) %}
                <div class="progress-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Translation Progress</span>
                        <span class="fw-bold">{{ lang_stats.get('percentage', 0) }}%</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {{ lang_stats.get('percentage', 0) }}%"></div>
                    </div>
                    <div class="text-muted small mt-1">
                        {{ lang_stats.get('translated', 0) }} of {{ total_resources }} items translated
                    </div>
                </div>
                
                <div class="translation-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ lang_stats.get('products', 0) }}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ lang_stats.get('collections', 0) }}</div>
                        <div class="stat-label">Collections</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ lang_stats.get('pages', 0) }}</div>
                        <div class="stat-label">Pages</div>
                    </div>
                </div>
                
                {% if lang_stats.get('recent_translations') %}
                <div class="mt-3">
                    <h6 class="text-muted mb-2">Recent Translations</h6>
                    {% for item in lang_stats.get('recent_translations', [])[:3] %}
                    <div class="translation-item translated">
                        <div class="item-info">
                            <div class="item-title">{{ item.title }}</div>
                            <div class="item-meta">
                                <i class="fas fa-{{ item.type_icon }} me-1"></i>{{ item.type|title }}
                                • Translated {{ item.translated_at }}
                            </div>
                        </div>
                        <div class="translation-status">
                            <div class="status-icon success">
                                <i class="fas fa-check fa-xs"></i>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <i class="fas fa-language"></i>
        <p>No additional languages are enabled in your Shopify store.</p>
        <p>To enable translations, first add languages in your Shopify admin.</p>
        <a href="https://admin.shopify.com/store/{{ shop_domain.split('.')[0] }}/settings/languages" 
           target="_blank" class="btn btn-primary">
            <i class="fas fa-external-link-alt me-2"></i>Configure Languages in Shopify
        </a>
    </div>
    {% endif %}
    
    <!-- Resource Type Breakdown -->
    {% if total_resources > 0 %}
    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title mb-3">Translation Details by Resource Type</h5>
            
            <div class="resource-tabs">
                <button class="resource-tab active" data-type="all">
                    All Resources ({{ total_resources }})
                </button>
                <button class="resource-tab" data-type="products">
                    <i class="fas fa-box me-1"></i>Products ({{ resource_counts.products }})
                </button>
                <button class="resource-tab" data-type="collections">
                    <i class="fas fa-layer-group me-1"></i>Collections ({{ resource_counts.collections }})
                </button>
                <button class="resource-tab" data-type="pages">
                    <i class="fas fa-file-alt me-1"></i>Pages ({{ resource_counts.pages }})
                </button>
            </div>
            
            <div id="resource-content">
                <!-- Content will be loaded dynamically -->
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Resource tab functionality
document.querySelectorAll('.resource-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Update active state
        document.querySelectorAll('.resource-tab').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        
        // Load content based on type
        const type = this.dataset.type;
        loadResourceContent(type);
    });
});

async function loadResourceContent(type) {
    const contentDiv = document.getElementById('resource-content');
    contentDiv.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    
    try {
        const response = await fetch(`/ai/api/translation-details?type=${type}`);
        const data = await response.json();
        
        if (data.success) {
            let html = '<div class="table-responsive"><table class="table table-hover">';
            html += '<thead><tr><th>Item</th><th>Type</th>';
            
            // Add language columns
            const languages = {{ shop_locales|tojson }};
            languages.forEach(lang => {
                if (!lang.primary) {
                    html += `<th class="text-center">${lang.locale}</th>`;
                }
            });
            
            html += '<th>Actions</th></tr></thead><tbody>';
            
            if (data.items && data.items.length > 0) {
                data.items.forEach(item => {
                    html += '<tr>';
                    html += `<td>${item.title}</td>`;
                    html += `<td><span class="badge bg-secondary">${item.type}</span></td>`;
                    
                    // Language status cells
                    languages.forEach(lang => {
                        if (!lang.primary) {
                            const isTranslated = item.translations && item.translations[lang.locale];
                            html += '<td class="text-center">';
                            if (isTranslated) {
                                html += '<i class="fas fa-check-circle text-success"></i>';
                            } else {
                                html += '<i class="fas fa-times-circle text-muted"></i>';
                            }
                            html += '</td>';
                        }
                    });
                    
                    html += '<td>';
                    html += `<a href="/admin/${item.type}s/${item.id}" target="_blank" class="btn btn-sm btn-outline-primary">`;
                    html += '<i class="fas fa-external-link-alt"></i></a>';
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="100%" class="text-center text-muted py-4">No items found</td></tr>';
            }
            
            html += '</tbody></table></div>';
            contentDiv.innerHTML = html;
        } else {
            contentDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${data.error || 'Unable to load translation details'}
                </div>
            `;
        }
    } catch (error) {
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Error loading translation details: ${error.message}
            </div>
        `;
    }
}

// Auto-refresh if translations are being processed
const processingCount = {{ processing_count|default(0) }};
if (processingCount > 0) {
    setTimeout(() => {
        location.reload();
    }, 30000); // Refresh every 30 seconds
}

// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Load initial content
loadResourceContent('all');
</script>
{% endblock %}</content>
</invoke>