{% extends "base_auth.html" %}

{% block title %}Technical SEO Dashboard - Shopify AI Control{% endblock %}

{% block styles %}
<style>
    /* Dashboard Layout */
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    /* Enhanced Stats Cards */
    .stats-card {
        border: none;
        border-radius: 16px;
        background: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }
    
    .stats-card.primary::before { background: linear-gradient(90deg, #4299E1, #3182CE); }
    .stats-card.success::before { background: linear-gradient(90deg, #48BB78, #38A169); }
    .stats-card.warning::before { background: linear-gradient(90deg, #ED8936, #DD6B20); }
    .stats-card.info::before { background: linear-gradient(90deg, #4FD1C7, #319795); }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 16px;
    }
    
    .stat-icon.primary { background: linear-gradient(135deg, #EBF8FF, #BEE3F8); color: #3182CE; }
    .stat-icon.success { background: linear-gradient(135deg, #F0FFF4, #C6F6D5); color: #38A169; }
    .stat-icon.warning { background: linear-gradient(135deg, #FFFAF0, #FED7AA); color: #DD6B20; }
    .stat-icon.info { background: linear-gradient(135deg, #F0FDFA, #ABEBC6); color: #319795; }
    
    /* Feature Cards */
    .feature-card {
        border: none;
        border-radius: 20px;
        padding: 32px;
        margin-bottom: 24px;
        transition: all 0.4s ease;
        background: white;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
    }
    
    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
    }
    
    .feature-card.available::before {
        background: linear-gradient(90deg, #48BB78, #38A169);
    }
    
    .feature-card.unavailable::before {
        background: linear-gradient(90deg, #F56565, #E53E3E);
    }
    
    .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 35px rgba(0,0,0,0.12);
    }
    
    /* Status Indicators */
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 12px;
        position: relative;
    }
    
    .status-available {
        background-color: #48BB78;
        animation: pulse-green 2s infinite;
        box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.4);
    }
    
    .status-unavailable {
        background-color: #F56565;
    }
    
    .status-processing {
        background-color: #4299E1;
        animation: pulse-blue 1.5s infinite;
    }
    
    @keyframes pulse-green {
        0% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(72, 187, 120, 0); }
        100% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0); }
    }
    
    @keyframes pulse-blue {
        0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); }
        100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
    }
    
    /* Progress Components */
    .progress-circle {
        position: relative;
        display: inline-block;
        width: 120px;
        height: 120px;
    }
    
    .progress-circle canvas {
        transform: rotate(-90deg);
    }
    
    .progress-circle .percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        font-weight: 700;
        color: #2D3748;
    }
    
    .enhanced-progress {
        height: 12px;
        background: #EDF2F7;
        border-radius: 6px;
        overflow: hidden;
        position: relative;
    }
    
    .enhanced-progress .progress-bar {
        height: 100%;
        border-radius: 6px;
        background: linear-gradient(90deg, #48BB78, #38A169);
        transition: width 0.6s ease;
        position: relative;
        overflow: hidden;
    }
    
    .enhanced-progress .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-image: linear-gradient(
            -45deg,
            rgba(255, 255, 255, .2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, .2) 50%,
            rgba(255, 255, 255, .2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 20px 20px;
        animation: move 1s linear infinite;
    }
    
    @keyframes move {
        0% { background-position: 0 0; }
        100% { background-position: 20px 20px; }
    }
    
    /* Enhanced Tables */
    .enhanced-table {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        border: none;
    }
    
    .enhanced-table thead th {
        background: linear-gradient(135deg, #F7FAFC, #EDF2F7);
        border: none;
        padding: 20px 16px;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #4A5568;
    }
    
    .enhanced-table tbody tr {
        border: none;
        transition: all 0.2s ease;
    }
    
    .enhanced-table tbody tr:hover {
        background-color: #F7FAFC;
        transform: scale(1.01);
    }
    
    .enhanced-table tbody td {
        padding: 20px 16px;
        border: none;
        vertical-align: middle;
    }
    
    /* Buttons */
    .btn-primary-gradient {
        background: linear-gradient(135deg, #4299E1, #3182CE);
        border: none;
        padding: 14px 32px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .btn-primary-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }
    
    .btn-primary-gradient:hover::before {
        left: 100%;
    }
    
    .btn-primary-gradient:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
        color: white;
    }
    
    .btn-primary-gradient:disabled {
        background: #A0AEC0;
        transform: none;
        box-shadow: none;
    }
    
    /* Badge Enhancements */
    .badge-enhanced {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .badge-success-enhanced { 
        background: linear-gradient(135deg, #48BB78, #38A169);
        color: white;
    }
    
    .badge-warning-enhanced { 
        background: linear-gradient(135deg, #ED8936, #DD6B20);
        color: white;
    }
    
    .badge-danger-enhanced { 
        background: linear-gradient(135deg, #F56565, #E53E3E);
        color: white;
    }
    
    .badge-info-enhanced { 
        background: linear-gradient(135deg, #4299E1, #3182CE);
        color: white;
    }
    
    .badge-processing {
        background: linear-gradient(135deg, #4299E1, #3182CE);
        color: white;
        animation: pulse-badge 2s infinite;
    }
    
    @keyframes pulse-badge {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    /* Content Sections */
    .section-card {
        background: white;
        border-radius: 20px;
        padding: 32px;
        margin-bottom: 32px;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        border: 1px solid #E2E8F0;
    }
    
    .section-header {
        border-bottom: 2px solid #EDF2F7;
        padding-bottom: 16px;
        margin-bottom: 24px;
    }
    
    .section-title {
        font-size: 20px;
        font-weight: 700;
        color: #2D3748;
        margin: 0;
    }
    
    .section-subtitle {
        color: #718096;
        margin: 4px 0 0 0;
        font-size: 14px;
    }
    
    /* Collapsible Content */
    .collapsible-trigger {
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 8px;
        padding: 12px;
        margin: -12px;
    }
    
    .collapsible-trigger:hover {
        background-color: #F7FAFC;
    }
    
    .collapsible-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    
    .collapsible-content.show {
        max-height: 500px;
    }
    
    /* Change History Styles */
    .change-item {
        border-left: 4px solid #48BB78;
        background: #F0FFF4;
        padding: 16px;
        border-radius: 0 8px 8px 0;
        margin-bottom: 12px;
        transition: all 0.2s ease;
    }
    
    .change-item:hover {
        background: #E6FFFA;
        transform: translateX(4px);
    }
    
    .change-before {
        background: #FED7D7;
        border-radius: 6px;
        padding: 8px;
        font-family: monospace;
        font-size: 12px;
        margin-bottom: 8px;
    }
    
    .change-after {
        background: #C6F6D5;
        border-radius: 6px;
        padding: 8px;
        font-family: monospace;
        font-size: 12px;
    }
    
    /* Shopify Link Styles */
    .shopify-link {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg, #96BF47, #7AB317);
        color: white !important;
        text-decoration: none !important;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 600;
        transition: all 0.2s ease;
        border: none;
        white-space: nowrap;
    }
    
    .shopify-link:hover {
        color: white !important;
        text-decoration: none !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(150, 191, 71, 0.4);
        background: linear-gradient(135deg, #7AB317, #96BF47);
    }
    
    .shopify-link:visited {
        color: white !important;
    }
    
    .shopify-link i {
        margin-right: 4px;
        font-size: 10px;
    }
    
    /* Loading States */
    .loading-shimmer {
        background: linear-gradient(90deg, #F7FAFC 0%, #EDF2F7 50%, #F7FAFC 100%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }
    
    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0 16px;
        }
        
        .feature-card {
            padding: 20px;
        }
        
        .section-card {
            padding: 20px;
        }
        
        .progress-circle {
            width: 80px;
            height: 80px;
        }
        
        .progress-circle .percentage {
            font-size: 16px;
        }
    }
    
    /* Dark Mode Support */
    @media (prefers-color-scheme: dark) {
        .stats-card,
        .feature-card,
        .section-card {
            background: #2D3748;
            color: #E2E8F0;
        }
        
        .enhanced-table thead th {
            background: linear-gradient(135deg, #4A5568, #2D3748);
            color: #E2E8F0;
        }
        
        .enhanced-table tbody tr:hover {
            background-color: #4A5568;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-3 mb-lg-0">
                    <h1 class="display-6 fw-bold text-dark mb-2">Technical SEO Dashboard</h1>
                    <p class="text-muted mb-0 fs-5">AI-powered image optimization and accessibility improvements</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-light rounded-pill px-3 py-2 mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="text-decoration-none">
                                <i class="fas fa-chart-line me-1"></i>SEO Dashboard
                            </a>
                        </li>
                        <li class="breadcrumb-item active">Technical SEO</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Real-time Status Banner -->
    {% if recent_jobs and recent_jobs[0].status == 'processing' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0 rounded-4 p-4" style="background: linear-gradient(135deg, #EBF8FF, #BEE3F8);">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-processing me-3"></div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 fw-bold">
                            <i class="fas fa-cogs me-2"></i>Optimization in Progress
                        </h6>
                        <p class="mb-0 small">Alt text optimization is currently running. This page will automatically update when complete.</p>
                    </div>
                    <div class="text-end">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Enhanced Statistics Overview -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card primary" onclick="scrollToSection('breakdown')">
                <div class="card-body text-center p-4">
                    <div class="stat-icon primary mx-auto">
                        <i class="fas fa-images"></i>
                    </div>
                    <h2 class="display-6 fw-bold text-dark mb-1">{{ alt_text_stats.total_images }}</h2>
                    <p class="text-muted mb-2 fw-medium">Total Images Found</p>
                    <small class="text-muted">
                        <i class="fas fa-search me-1"></i>{{ alt_text_stats.sources_checked }} sources scanned
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card success" onclick="scrollToSection('optimized')">
                <div class="card-body text-center p-4">
                    <div class="stat-icon success mx-auto">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2 class="display-6 fw-bold text-dark mb-1">{{ alt_text_stats.total_images - alt_text_stats.images_without_alt_text }}</h2>
                    <p class="text-muted mb-2 fw-medium">Optimized Images</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>SEO & Accessibility Ready
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card warning" onclick="scrollToSection('pending')">
                <div class="card-body text-center p-4">
                    <div class="stat-icon warning mx-auto">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2 class="display-6 fw-bold text-dark mb-1">{{ alt_text_stats.images_without_alt_text }}</h2>
                    <p class="text-muted mb-2 fw-medium">Need Optimization</p>
                    <small class="text-warning">
                        <i class="fas fa-clock me-1"></i>Missing Alt Text
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card info" onclick="scrollToSection('progress')">
                <div class="card-body text-center p-4">
                    <div class="stat-icon info mx-auto">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h2 class="display-6 fw-bold text-dark mb-1">{{ alt_text_stats.completion_percentage }}%</h2>
                    <p class="text-muted mb-2 fw-medium">Completion Rate</p>
                    <small class="text-info">
                        <i class="fas fa-target me-1"></i>Overall Progress
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Alt Text Optimization Feature Card -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="feature-card {% if openai_available %}available{% else %}unavailable{% endif %}">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0">
                        <div class="d-flex align-items-center mb-4">
                            <span class="status-indicator {% if openai_available %}status-available{% else %}status-unavailable{% endif %}"></span>
                            <h3 class="mb-0 fw-bold">AI-Powered Alt Text Optimization</h3>
                            {% if not openai_available %}
                                <span class="badge badge-warning-enhanced ms-3">OpenAI Required</span>
                            {% endif %}
                        </div>
                        
                        <p class="text-muted mb-4 fs-6 lh-lg">
                            Transform your store's accessibility and SEO with intelligent alt text generation. Our AI analyzes each image using advanced computer vision, 
                            understanding context from product descriptions, categories, and visual content to create descriptive, keyword-rich alt text that 
                            improves both search rankings and accessibility compliance.
                        </p>
                        
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-eye text-success me-3 fs-5"></i>
                                    <div>
                                        <h6 class="mb-1 fw-semibold">Vision Analysis</h6>
                                        <small class="text-muted">AI identifies objects, colors, and scenes</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-brain text-info me-3 fs-5"></i>
                                    <div>
                                        <h6 class="mb-1 fw-semibold">Context Awareness</h6>
                                        <small class="text-muted">Uses product data for relevant descriptions</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if openai_available %}
                            <div class="d-flex align-items-center flex-wrap gap-3">
                                <button class="btn btn-primary-gradient" id="startAltTextBtn">
                                    <i class="fas fa-magic me-2"></i>Optimize 25 Images Now
                                </button>
                                <div class="text-muted">
                                    <i class="fas fa-calendar-day me-2"></i>
                                    <small>Daily automation processes 25 images automatically</small>
                                </div>
                            </div>
                        {% else %}
                            <div class="alert alert-warning border-0 rounded-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-key text-warning me-3 fs-4"></i>
                                    <div>
                                        <h6 class="mb-1 fw-semibold">OpenAI API Key Required</h6>
                                        <p class="mb-0 small">Configure your OpenAI API key in settings to enable AI alt text generation. Uses GPT-4 Vision for accurate, cost-effective image analysis.</p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if openai_available %}
                    <div class="col-lg-4 text-center">
                        <div class="progress-circle mb-3 mx-auto">
                            <canvas width="120" height="120" id="altTextProgress"></canvas>
                            <div class="percentage">{{ alt_text_stats.completion_percentage }}%</div>
                        </div>
                        <div class="row text-center g-3">
                            <div class="col-6">
                                <div class="bg-light rounded-3 p-3">
                                    <h6 class="mb-1 text-success fw-bold">{{ alt_text_stats.total_images - alt_text_stats.images_without_alt_text }}</h6>
                                    <small class="text-muted">Optimized</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="bg-light rounded-3 p-3">
                                    <h6 class="mb-1 text-warning fw-bold">{{ alt_text_stats.images_without_alt_text }}</h6>
                                    <small class="text-muted">Remaining</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Breakdown by Content Type -->
    <div class="row mb-5" id="breakdown">
        <div class="col-12">
            <div class="section-card">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-chart-bar me-3"></i>Image Analysis by Content Type
                    </h3>
                    <p class="section-subtitle">Detailed breakdown showing optimization progress across different areas of your store</p>
                </div>
                
                {% if alt_text_stats.total_images > 0 %}
                <div class="row g-4">
                    {% for type_name, type_data in alt_text_stats.breakdown_by_type.items() %}
                    {% if type_data.total > 0 %}
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm h-100 content-type-card" data-type="{{ type_name }}">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    {% if type_name == 'products' %}
                                        <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                                            <i class="fas fa-box text-primary fs-4"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1 fw-bold">Products</h5>
                                            <small class="text-muted">Product images & galleries</small>
                                        </div>
                                    {% elif type_name == 'collections' %}
                                        <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                                            <i class="fas fa-layer-group text-success fs-4"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1 fw-bold">Collections</h5>
                                            <small class="text-muted">Category & collection images</small>
                                        </div>
                                    {% elif type_name == 'pages' %}
                                        <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                                            <i class="fas fa-file-alt text-info fs-4"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1 fw-bold">Pages</h5>
                                            <small class="text-muted">Static page content</small>
                                        </div>
                                    {% elif type_name == 'blog_posts' %}
                                        <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                                            <i class="fas fa-blog text-warning fs-4"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1 fw-bold">Blog Posts</h5>
                                            <small class="text-muted">Article & blog images</small>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="row g-3 mb-3">
                                    <div class="col-6 text-center">
                                        <h4 class="mb-1 fw-bold text-primary">{{ type_data.total }}</h4>
                                        <small class="text-muted">Total Images</small>
                                    </div>
                                    <div class="col-6 text-center">
                                        <h4 class="mb-1 fw-bold text-warning">{{ type_data.missing_alt }}</h4>
                                        <small class="text-muted">Need Alt Text</small>
                                    </div>
                                </div>
                                
                                {% set completion = ((type_data.total - type_data.missing_alt) / type_data.total * 100) if type_data.total > 0 else 100 %}
                                <div class="enhanced-progress mb-3">
                                    <div class="progress-bar" style="width: {{ completion }}%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-semibold text-success">{{ "%.0f"|format(completion) }}% Complete</span>
                                    <button class="btn btn-sm btn-outline-primary" onclick="expandContentType('{{ type_name }}')">
                                        <i class="fas fa-chevron-down me-1"></i>Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-4" style="width: 120px; height: 120px;">
                        <i class="fas fa-search text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h4 class="text-muted mb-3">No Images Found</h4>
                    <p class="text-muted mb-4">This could indicate:</p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item border-0 bg-transparent">
                                    <i class="fas fa-store text-muted me-2"></i>
                                    Your Shopify store doesn't have products or content yet
                                </div>
                                <div class="list-group-item border-0 bg-transparent">
                                    <i class="fas fa-unlink text-muted me-2"></i>
                                    Connection issue with your Shopify store
                                </div>
                                <div class="list-group-item border-0 bg-transparent">
                                    <i class="fas fa-cog text-muted me-2"></i>
                                    Check your API credentials in Settings
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Optimization Activity & Change History -->
    {% if recent_jobs %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="section-card">
                <div class="section-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="section-title">
                                <i class="fas fa-history me-3"></i>Optimization History & Changes
                            </h3>
                            <p class="section-subtitle">Track all alt text optimizations with before/after comparisons and direct links to your Shopify admin</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('table')">
                                <i class="fas fa-table me-1"></i>Table View
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('timeline')">
                                <i class="fas fa-clock me-1"></i>Timeline View
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="tableView">
                    <div class="table-responsive">
                        <table class="table enhanced-table mb-0">
                            <thead>
                                <tr>
                                    <th>Resource</th>
                                    <th>Changes Made</th>
                                    <th>Performance</th>
                                    <th>Status</th>
                                    <th>When</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in recent_jobs %}
                                <tr class="job-row" data-job-id="{{ job.id }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-primary bg-opacity-10 p-2 me-3">
                                                <i class="fas fa-image text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold">
                                                    {% if job.resource_type == 'product' and job.resource_id %}
                                                        Product ID: {{ job.resource_id }}
                                                    {% elif job.task_type == 'alt_text_batch' %}
                                                        Batch Optimization
                                                    {% else %}
                                                        Alt Text Optimization
                                                    {% endif %}
                                                </h6>
                                                <small class="text-muted">{{ job.task_type.replace('_', ' ').title() }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    
                                    <td>
                                        {% if job.result_data %}
                                            {% set result = job.result_data | from_json %}
                                            <div class="collapsible-trigger" onclick="toggleJobDetails({{ job.id }})">
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        {% if result.successful_updates is defined %}
                                                            <span class="badge badge-success-enhanced">{{ result.successful_updates }} Updated</span>
                                                            {% if result.failed_updates and result.failed_updates > 0 %}
                                                                <span class="badge badge-danger-enhanced">{{ result.failed_updates }} Failed</span>
                                                            {% endif %}
                                                        {% else %}
                                                            <span class="badge badge-info-enhanced">Processing</span>
                                                        {% endif %}
                                                    </div>
                                                    <i class="fas fa-chevron-down text-muted"></i>
                                                </div>
                                            </div>
                                            
                                            <div class="collapsible-content mt-3" id="job-details-{{ job.id }}">
                                                {% if result.changes %}
                                                    <div class="changes-preview">
                                                        <h6 class="fw-semibold mb-3">Changes Made:</h6>
                                                        {% for change in result.changes[:3] %}
                                                        <div class="change-item">
                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="mb-1 fw-semibold">{{ change.image_filename or 'Image' }}</h6>
                                                                {% if change.shopify_product_id %}
                                                                    <a href="https://{{ shop_domain }}/admin/products/{{ change.shopify_product_id }}" 
                                                                       target="_blank" class="shopify-link" rel="noopener">
                                                                        <i class="fas fa-external-link-alt"></i>Edit Product
                                                                    </a>
                                                                {% endif %}
                                                            </div>
                                                            <div class="change-before">
                                                                <strong>Before:</strong> {{ change.old_alt_text or '(No alt text)' }}
                                                            </div>
                                                            <div class="change-after">
                                                                <strong>After:</strong> {{ change.new_alt_text }}
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                        
                                                        {% if result.changes|length > 3 %}
                                                        <div class="text-center mt-3">
                                                            <button class="btn btn-sm btn-outline-primary" onclick="showAllChanges({{ job.id }})">
                                                                <i class="fas fa-eye me-1"></i>Show {{ result.changes|length - 3 }} More Changes
                                                            </button>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <span class="text-muted">No changes recorded</span>
                                        {% endif %}
                                    </td>
                                    
                                    <td>
                                        {% if job.updated_at and job.created_at %}
                                            {% set duration = (job.updated_at - job.created_at).total_seconds() %}
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-clock text-muted me-2"></i>
                                                <div>
                                                    <div class="fw-semibold">
                                                        {% if duration > 60 %}
                                                            {{ "%.1f"|format(duration/60) }} min
                                                        {% else %}
                                                            {{ "%.0f"|format(duration) }} sec
                                                        {% endif %}
                                                    </div>
                                                    <small class="text-muted">Duration</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    
                                    <td>
                                        {% if job.status == 'completed' %}
                                            <span class="badge badge-success-enhanced">
                                                <i class="fas fa-check me-1"></i>Completed
                                            </span>
                                        {% elif job.status == 'processing' %}
                                            <span class="badge badge-processing">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Processing
                                            </span>
                                        {% elif job.status == 'failed' %}
                                            <span class="badge badge-danger-enhanced">
                                                <i class="fas fa-times me-1"></i>Failed
                                            </span>
                                        {% else %}
                                            <span class="badge badge-info-enhanced">{{ job.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    
                                    <td>
                                        <div class="text-muted">
                                            <div class="fw-semibold">{{ job.created_at.strftime('%m/%d %H:%M') }}</div>
                                            <small title="{{ job.created_at.strftime('%Y-%m-%d %H:%M:%S') }}">
                                                {{ job.created_at.strftime('%Y-%m-%d') }}
                                            </small>
                                        </div>
                                    </td>
                                    
                                    <td>
                                        <div class="d-flex gap-2">
                                            {% if job.result_data %}
                                                {% set result = job.result_data | from_json %}
                                                {% if result.shopify_product_id %}
                                                    <a href="https://{{ shop_domain }}/admin/products/{{ result.shopify_product_id }}" 
                                                       target="_blank" class="shopify-link" rel="noopener">
                                                        <i class="fas fa-external-link-alt"></i>Edit
                                                    </a>
                                                {% endif %}
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="showJobSummary({{ job.id }})">
                                                <i class="fas fa-info-circle me-1"></i>Details
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Timeline View (Hidden by default) -->
                <div id="timelineView" style="display: none;">
                    <div class="timeline">
                        {% for job in recent_jobs %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="fw-bold mb-0">{{ job.task_type.replace('_', ' ').title() }}</h6>
                                            <span class="text-muted small">{{ job.created_at.strftime('%m/%d %H:%M') }}</span>
                                        </div>
                                        <!-- Timeline content similar to table but formatted for timeline -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" style="position: fixed; top: 80px; right: 20px; z-index: 1050;"></div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Optimization Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="jobDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Progress Circle
    {% if openai_available %}
    function drawProgressCircle() {
        const canvas = document.getElementById('altTextProgress');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = 45;
        const percentage = {{ alt_text_stats.completion_percentage }};
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Background circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.strokeStyle = '#EDF2F7';
        ctx.lineWidth = 8;
        ctx.stroke();
        
        // Progress arc with gradient
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        if (percentage >= 90) {
            gradient.addColorStop(0, '#48BB78');
            gradient.addColorStop(1, '#38A169');
        } else if (percentage >= 50) {
            gradient.addColorStop(0, '#4FD1C7');
            gradient.addColorStop(1, '#319795');
        } else {
            gradient.addColorStop(0, '#ED8936');
            gradient.addColorStop(1, '#DD6B20');
        }
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, (percentage / 100) * 2 * Math.PI);
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 8;
        ctx.lineCap = 'round';
        ctx.stroke();
        
        // Add shadow
        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
        ctx.shadowBlur = 4;
        ctx.shadowOffsetY = 2;
    }
    
    // Animate progress circle on load
    function animateProgressCircle() {
        let currentPercentage = 0;
        const targetPercentage = {{ alt_text_stats.completion_percentage }};
        const increment = targetPercentage / 50; // 50 frames
        
        function animate() {
            if (currentPercentage < targetPercentage) {
                currentPercentage += increment;
                if (currentPercentage > targetPercentage) currentPercentage = targetPercentage;
                
                // Temporarily override the percentage for animation
                const originalPercentage = {{ alt_text_stats.completion_percentage }};
                // Update display
                document.querySelector('.progress-circle .percentage').textContent = Math.round(currentPercentage) + '%';
                
                requestAnimationFrame(animate);
            }
        }
        
        animate();
        drawProgressCircle();
    }
    
    // Initialize on load
    document.addEventListener('DOMContentLoaded', animateProgressCircle);
    {% endif %}
    
    // Enhanced Alt Text Optimization
    {% if openai_available %}
    document.getElementById('startAltTextBtn').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        
        // Enhanced loading state
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing Images...';
        
        // Add processing banner
        showProcessingBanner();
        
        const url = '{{ url_for("ai_modules.start_alt_text_optimization") }}';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('success', `
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-1">${data.message}</h6>
                            <small>This page will refresh automatically to show progress.</small>
                        </div>
                    </div>
                `);
                
                // Start polling for updates
                startProgressPolling();
                
                setTimeout(() => {
                    location.reload();
                }, 3000);
            } else {
                showMessage('danger', `
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-1">Optimization Failed</h6>
                            <small>${data.error}</small>
                        </div>
                    </div>
                `);
                btn.disabled = false;
                btn.innerHTML = originalText;
                hideProcessingBanner();
            }
        })
        .catch(error => {
            showMessage('danger', `
                <div class="d-flex align-items-center">
                    <i class="fas fa-times-circle me-3 fs-4"></i>
                    <div>
                        <h6 class="mb-1">Connection Error</h6>
                        <small>${error.message}</small>
                    </div>
                </div>
            `);
            btn.disabled = false;
            btn.innerHTML = originalText;
            hideProcessingBanner();
        });
    });
    {% endif %}
    
    // Processing Banner Functions
    function showProcessingBanner() {
        const existingBanner = document.querySelector('.processing-banner');
        if (existingBanner) return;
        
        const banner = document.createElement('div');
        banner.className = 'alert alert-info border-0 rounded-4 p-4 processing-banner';
        banner.style.background = 'linear-gradient(135deg, #EBF8FF, #BEE3F8)';
        banner.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="status-indicator status-processing me-3"></div>
                <div class="flex-grow-1">
                    <h6 class="mb-1 fw-bold">
                        <i class="fas fa-cogs me-2"></i>Optimization Starting...
                    </h6>
                    <p class="mb-0 small">AI is analyzing images and generating alt text. This may take a few minutes.</p>
                </div>
                <div class="text-end">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        `;
        
        document.querySelector('.dashboard-container').insertBefore(banner, document.querySelector('.row'));
    }
    
    function hideProcessingBanner() {
        const banner = document.querySelector('.processing-banner');
        if (banner) banner.remove();
    }
    
    // Progress Polling
    function startProgressPolling() {
        const pollInterval = setInterval(() => {
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const newDoc = parser.parseFromString(html, 'text/html');
                    const newBanner = newDoc.querySelector('.alert-info');
                    const currentBanner = document.querySelector('.processing-banner');
                    
                    if (!newBanner && currentBanner) {
                        // Job completed
                        clearInterval(pollInterval);
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Polling error:', error);
                    clearInterval(pollInterval);
                });
        }, 5000); // Poll every 5 seconds
        
        // Stop polling after 5 minutes
        setTimeout(() => clearInterval(pollInterval), 300000);
    }
    
    // Interactive Features
    function scrollToSection(sectionId) {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    function toggleJobDetails(jobId) {
        const content = document.getElementById(`job-details-${jobId}`);
        const icon = content.previousElementSibling.querySelector('.fas');
        
        content.classList.toggle('show');
        icon.classList.toggle('fa-chevron-down');
        icon.classList.toggle('fa-chevron-up');
    }
    
    function expandContentType(typeName) {
        showMessage('info', `
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-3 fs-4"></i>
                <div>
                    <h6 class="mb-1">Content Type Details</h6>
                    <small>Showing detailed breakdown for ${typeName.replace('_', ' ')} images.</small>
                </div>
            </div>
        `);
    }
    
    function toggleView(viewType) {
        const tableView = document.getElementById('tableView');
        const timelineView = document.getElementById('timelineView');
        
        if (viewType === 'table') {
            tableView.style.display = 'block';
            timelineView.style.display = 'none';
        } else {
            tableView.style.display = 'none';
            timelineView.style.display = 'block';
        }
    }
    
    function showAllChanges(jobId) {
        // This would open a modal with all changes
        showMessage('info', `
            <div class="d-flex align-items-center">
                <i class="fas fa-eye me-3 fs-4"></i>
                <div>
                    <h6 class="mb-1">All Changes</h6>
                    <small>Opening detailed view for job ${jobId}...</small>
                </div>
            </div>
        `);
    }
    
    function scrollToJobRow(jobId) {
        const jobRow = document.querySelector(`tr[data-job-id="${jobId}"]`);
        if (jobRow) {
            jobRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // Highlight the row temporarily
            jobRow.style.backgroundColor = '#fff3cd';
            setTimeout(() => {
                jobRow.style.backgroundColor = '';
            }, 2000);
        }
    }
    
    function showJobSummary(jobId) {
        // Enhanced job details modal
        const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
        const content = document.getElementById('jobDetailsContent');
        
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading job details...</p>
            </div>
        `;
        
        modal.show();
        
        // Find the job row in the table to get actual data
        const jobRow = document.querySelector(`tr[data-job-id="${jobId}"]`);
        let jobInfo = {};
        
        if (jobRow) {
            // Extract real data from the table row
            const statusBadge = jobRow.querySelector('.badge');
            const durationText = jobRow.querySelector('td:nth-child(3) .fw-semibold');
            const changesText = jobRow.querySelector('td:nth-child(2) .badge');
            
            jobInfo = {
                status: statusBadge ? statusBadge.textContent.trim() : 'Unknown',
                duration: durationText ? durationText.textContent.trim() : 'N/A',
                changes: changesText ? changesText.textContent.trim() : 'No changes'
            };
        }
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Job Information</h6>
                    <ul class="list-unstyled">
                        <li><strong>Job ID:</strong> ${jobId}</li>
                        <li><strong>Type:</strong> Alt Text Optimization</li>
                        <li><strong>Status:</strong> <span class="badge badge-info-enhanced">${jobInfo.status}</span></li>
                        <li><strong>Duration:</strong> ${jobInfo.duration}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Performance</h6>
                    <ul class="list-unstyled">
                        <li><strong>Changes Made:</strong> ${jobInfo.changes}</li>
                        <li><strong>View Details:</strong> <button class="btn btn-sm btn-outline-primary" onclick="toggleJobDetails(${jobId})">Show in Table</button></li>
                    </ul>
                </div>
            </div>
            <div class="alert alert-info mt-3">
                <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2"></i>View Complete Details</h6>
                <p class="mb-2">For detailed information about all changes made, including before/after comparisons and direct Shopify links:</p>
                <button class="btn btn-primary btn-sm" onclick="document.getElementById('jobDetailsModal').querySelector('.btn-close').click(); toggleJobDetails(${jobId}); scrollToJobRow(${jobId});">
                    <i class="fas fa-table me-1"></i>View in Main Table
                </button>
            </div>
        `;
    }
    
    // Enhanced Message System
    function showMessage(type, message) {
        const container = document.getElementById('messageContainer');
        const alertDiv = document.createElement('div');
        
        // Enhanced styling based on type
        const typeClasses = {
            'success': 'alert-success border-success',
            'danger': 'alert-danger border-danger',
            'warning': 'alert-warning border-warning',
            'info': 'alert-info border-info'
        };
        
        alertDiv.className = `alert ${typeClasses[type] || 'alert-info'} alert-dismissible fade show border-0 rounded-3 shadow-lg`;
        alertDiv.style.cssText = `
            max-width: 400px;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        `;
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(alertDiv);
        
        // Enhanced auto-remove with animation
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.classList.add('fade');
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 150);
            }
        }, 8000);
    }
    
    // Auto-refresh for processing jobs
    {% if recent_jobs and recent_jobs[0].status == 'processing' %}
    let refreshCount = 0;
    const maxRefreshes = 20; // Maximum 20 refreshes (10 minutes at 30s intervals)
    
    function autoRefresh() {
        if (refreshCount < maxRefreshes) {
            refreshCount++;
            setTimeout(() => {
                fetch(window.location.href)
                    .then(response => response.text())
                    .then(html => {
                        const parser = new DOMParser();
                        const newDoc = parser.parseFromString(html, 'text/html');
                        const newStatus = newDoc.querySelector('.processing-banner');
                        
                        if (!newStatus) {
                            // Job completed, reload page
                            location.reload();
                        } else {
                            // Continue checking
                            autoRefresh();
                        }
                    })
                    .catch(error => {
                        console.error('Auto-refresh error:', error);
                        // Continue trying
                        autoRefresh();
                    });
            }, 30000); // Check every 30 seconds
        }
    }
    
    // Start auto-refresh
    autoRefresh();
    {% endif %}
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+R or F5 for manual refresh
        if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
            showMessage('info', `
                <div class="d-flex align-items-center">
                    <i class="fas fa-sync-alt me-3 fs-4"></i>
                    <div>
                        <h6 class="mb-1">Refreshing Dashboard</h6>
                        <small>Updating optimization progress...</small>
                    </div>
                </div>
            `);
        }
        
        // Ctrl+O to start optimization
        {% if openai_available %}
        if (e.ctrlKey && e.key === 'o') {
            e.preventDefault();
            const btn = document.getElementById('startAltTextBtn');
            if (btn && !btn.disabled) {
                btn.click();
            }
        }
        {% endif %}
    });
    
    // Initialize tooltips if Bootstrap is available
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });
</script>
{% endblock %}