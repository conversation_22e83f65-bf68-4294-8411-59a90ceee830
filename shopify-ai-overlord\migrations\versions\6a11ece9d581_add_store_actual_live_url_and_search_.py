"""Add store_actual_live_url and search_console_property fields

Revision ID: 6a11ece9d581
Revises: 00aedd0947b6
Create Date: 2025-05-31 14:51:57.999219

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6a11ece9d581'
down_revision = '00aedd0947b6'
branch_labels = None
depends_on = None


def upgrade():
    # Add store_actual_live_url to user_settings table
    op.add_column('user_settings', sa.Column('store_actual_live_url', sa.String(length=255), nullable=True))
    
    # Add search_console_property to google_oauth table
    op.add_column('google_oauth', sa.Column('search_console_property', sa.String(length=255), nullable=True))


def downgrade():
    # Remove columns
    op.drop_column('google_oauth', 'search_console_property')
    op.drop_column('user_settings', 'store_actual_live_url')
