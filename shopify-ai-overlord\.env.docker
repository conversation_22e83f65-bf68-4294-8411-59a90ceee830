# Docker Local Development Environment Variables
# Copy this to .env.local and fill in your API keys

# AI API Keys (Required - get these from your providers)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# Admin Credentials for Local Development
ADMIN_USERNAME=localadmin
ADMIN_PASSWORD=LocalPass123!

# Optional: Override any default settings
# TARGET_COMPLETION_DAYS=90
# MIN_PRODUCTS_PER_DAY=3
# MAX_PRODUCTS_PER_DAY=60
# COLLS_PER_PRODUCT=1.5
# MAX_COLLECTIONS_PER_DAY=20