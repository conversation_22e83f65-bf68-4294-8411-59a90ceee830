from app import db
from datetime import datetime
import json

class AutomationJob(db.Model):
    """Track automation jobs and their status"""
    __tablename__ = 'automation_jobs'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.<PERSON><PERSON>('shops.id'), nullable=False)
    task_type = db.Column(db.String(50), nullable=False)  # product_seo, collection, translate, etc.
    task_id = db.Column(db.String(255))  # Celery task ID
    resource_id = db.Column(db.String(255))  # Shopify resource ID
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    error_message = db.Column(db.Text)
    result_data = db.Column(db.Text)  # JSON data
    job_metadata = db.Column(db.Text)  # Additional JSON data
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    shop = db.relationship('Shop', backref='automation_jobs')
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'shop_id': self.shop_id,
            'task_type': self.task_type,
            'task_id': self.task_id,
            'resource_id': self.resource_id,
            'status': self.status,
            'error_message': self.error_message,
            'result_data': json.loads(self.result_data) if self.result_data else None,
            'metadata': json.loads(self.job_metadata) if self.job_metadata else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_recent_jobs(cls, shop_id, task_type=None, limit=50):
        """Get recent jobs for a shop"""
        query = cls.query.filter_by(shop_id=shop_id)
        if task_type:
            query = query.filter_by(task_type=task_type)
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_job_stats(cls, shop_id, task_type=None):
        """Get job statistics for a shop"""
        query = cls.query.filter_by(shop_id=shop_id)
        if task_type:
            query = query.filter_by(task_type=task_type)
        
        total = query.count()
        completed = query.filter_by(status='completed').count()
        failed = query.filter_by(status='failed').count()
        processing = query.filter_by(status='processing').count()
        pending = query.filter_by(status='pending').count()
        
        return {
            'total': total,
            'completed': completed,
            'failed': failed,
            'processing': processing,
            'pending': pending
        }