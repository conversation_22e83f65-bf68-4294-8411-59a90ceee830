{% extends "base_auth.html" %}

{% block title %}Product SEO Status - Shopify AI Control{% endblock %}

{% block extra_css %}
<style>
    /* Container fixes */
    body, html {
        overflow-x: hidden;
        max-width: 100%;
    }
    
    .container, .container-fluid {
        max-width: 100%;
        overflow-x: hidden;
    }
    
    /* Fix for table layout */
    table {
        width: 100%;
        table-layout: fixed;
    }
    
    /* Ensure proper spacing with sidebar */
    @media (min-width: 769px) {
        .container-fluid {
            padding-left: 2rem;
            padding-right: 2rem;
        }
    }
    
    /* Status badge styles */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        padding: 0.35rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }
    
    .status-optimized {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-processing {
        background-color: #dbeafe;
        color: #1e40af;
    }
    
    .status-pending {
        background-color: #f3f4f6;
        color: #374151;
    }
    
    .status-failed {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    /* Progress indicator */
    .optimization-progress {
        position: relative;
        height: 4px;
        background-color: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }
    
    .optimization-progress-bar {
        height: 100%;
        background-color: #10b981;
        transition: width 0.3s ease;
    }
    
    /* Optimization details card */
    .optimization-details {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }
    
    .optimization-stat {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .optimization-stat:last-child {
        border-bottom: none;
    }
    
    /* Product card styles */
    .product-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .product-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .product-info {
        display: flex;
        gap: 1rem;
        align-items: start;
    }
    
    .product-image {
        width: 50px;
        height: 50px;
        border-radius: 0.375rem;
        object-fit: cover;
        flex-shrink: 0;
    }
    
    .product-details {
        flex-grow: 1;
        min-width: 0;
    }
    
    .product-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .product-meta {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    /* Tag styles */
    .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        margin-top: 0.5rem;
    }
    
    .tag {
        background-color: #e0e7ff;
        color: #3730a3;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .tag.new {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    /* Filter buttons */
    .filter-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-bottom: 1.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #e5e7eb;
        background-color: white;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .filter-btn:hover {
        background-color: #f9fafb;
    }
    
    .filter-btn.active {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
    
    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="mb-2 mb-md-0">
            <i class="fas fa-chart-line me-2"></i>Product SEO Status
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ url_for('ai_modules.seo_dashboard') }}" class="btn btn-primary">
                <i class="fas fa-tachometer-alt me-2"></i>SEO Dashboard
            </a>
            <button class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Info Banner -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Product SEO Status Monitor</strong> - This page shows the optimization status of your products. 
        To optimize products, use the <a href="{{ url_for('ai_modules.seo_dashboard') }}">SEO Dashboard</a> automation controls.
    </div>

    <!-- Overall Progress -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-3">Overall Optimization Progress</h5>
            <div class="optimization-progress mb-3">
                <div class="optimization-progress-bar" style="width: {{ (optimized_count / total_count * 100) if total_count > 0 else 0 }}%"></div>
            </div>
            <div class="row text-center">
                <div class="col-md-3">
                    <h3 class="text-primary mb-0">{{ total_count }}</h3>
                    <small class="text-muted">Total Products</small>
                </div>
                <div class="col-md-3">
                    <h3 class="text-success mb-0">{{ optimized_count }}</h3>
                    <small class="text-muted">Optimized</small>
                </div>
                <div class="col-md-3">
                    <h3 class="text-warning mb-0">{{ processing_count }}</h3>
                    <small class="text-muted">Processing</small>
                </div>
                <div class="col-md-3">
                    <h3 class="text-secondary mb-0">{{ pending_count }}</h3>
                    <small class="text-muted">Not Optimized</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Buttons -->
    <div class="filter-buttons">
        <button class="filter-btn active" data-filter="all">
            All Products ({{ total_count }})
        </button>
        <button class="filter-btn" data-filter="optimized">
            <i class="fas fa-check-circle me-1"></i>Optimized ({{ optimized_count }})
        </button>
        <button class="filter-btn" data-filter="processing">
            <i class="fas fa-spinner me-1"></i>Processing ({{ processing_count }})
        </button>
        <button class="filter-btn" data-filter="pending">
            <i class="fas fa-clock me-1"></i>Not Optimized ({{ pending_count }})
        </button>
    </div>

    <!-- Products List -->
    <div id="products-container">
        {% for product in products %}
        {% set is_optimized = optimization_statuses.get(product.id|string, False) %}
        {% set optimization_data = optimization_details.get(product.id|string, {}) %}
        <div class="product-card" data-status="{{ 'optimized' if is_optimized else 'pending' }}">
            <div class="product-info">
                <!-- Product Image -->
                {% if product.images %}
                <img src="{{ product.images[0].src }}" alt="{{ product.title }}" class="product-image">
                {% else %}
                <div class="product-image bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                    <i class="fas fa-image text-muted"></i>
                </div>
                {% endif %}
                
                <!-- Product Details -->
                <div class="product-details">
                    <h5 class="product-title">{{ product.title }}</h5>
                    
                    <!-- Status Badge -->
                    {% if is_optimized %}
                    <span class="status-badge status-optimized">
                        <i class="fas fa-check-circle"></i>
                        Optimized
                    </span>
                    {% else %}
                    <span class="status-badge status-pending">
                        <i class="fas fa-clock"></i>
                        Not Optimized
                    </span>
                    {% endif %}
                    
                    <!-- Product Meta -->
                    <div class="product-meta">
                        <span><i class="fas fa-tag me-1"></i>{{ product.product_type or 'No type' }}</span>
                        <span><i class="fas fa-dollar-sign me-1"></i>{{ "%.2f"|format(product.variants[0].price|float) if product.variants else 'N/A' }}</span>
                        <span><i class="fas fa-barcode me-1"></i>ID: {{ product.id }}</span>
                    </div>
                    
                    <!-- Tags -->
                    {% if product.tags %}
                    <div class="tag-list">
                        {% for tag in product.tags.split(',')[:5] %}
                        <span class="tag {{ 'new' if optimization_data and tag.strip() in optimization_data.get('new_tags', []) else '' }}">
                            {{ tag.strip() }}
                        </span>
                        {% endfor %}
                        {% if product.tags.split(',')|length > 5 %}
                        <span class="tag">+{{ product.tags.split(',')|length - 5 }} more</span>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- Optimization Details (if optimized) -->
                    {% if is_optimized and optimization_data %}
                    <div class="optimization-details">
                        <h6 class="mb-2">Optimization Details</h6>
                        <div class="optimization-stat">
                            <span><i class="fas fa-calendar me-1"></i>Optimized On</span>
                            <strong>{{ optimization_data.optimized_at|default('N/A') }}</strong>
                        </div>
                        <div class="optimization-stat">
                            <span><i class="fas fa-robot me-1"></i>AI Model</span>
                            <strong>{{ optimization_data.optimized_by|default('Claude 3.5 Sonnet') }}</strong>
                        </div>
                        {% if optimization_data.new_tags %}
                        <div class="optimization-stat">
                            <span><i class="fas fa-tags me-1"></i>New Tags Added</span>
                            <strong>{{ optimization_data.new_tags|length }}</strong>
                        </div>
                        {% endif %}
                        {% if optimization_data.improvements %}
                        <div class="optimization-stat">
                            <span><i class="fas fa-chart-line me-1"></i>Improvements</span>
                            <strong>{{ optimization_data.improvements }}</strong>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% if not products %}
    <div class="empty-state">
        <i class="fas fa-box-open"></i>
        <p>No products found. Add products to your Shopify store to see them here.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter functionality
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Update active state
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        // Filter products
        const filter = this.dataset.filter;
        const products = document.querySelectorAll('.product-card');
        
        products.forEach(product => {
            if (filter === 'all') {
                product.style.display = 'block';
            } else {
                const status = product.dataset.status;
                product.style.display = status === filter ? 'block' : 'none';
            }
        });
    });
});

// Auto-refresh every 30 seconds if there are processing items
const processingCount = {{ processing_count }};
if (processingCount > 0) {
    setTimeout(() => {
        location.reload();
    }, 30000);
}

// Load more products if needed
async function loadAllProducts() {
    const totalCount = {{ total_count }};
    const loadedCount = {{ products|length }};
    
    if (loadedCount < totalCount) {
        try {
            // Show loading indicator
            const container = document.getElementById('products-container');
            const loader = document.createElement('div');
            loader.className = 'text-center py-3';
            loader.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading more products...';
            container.appendChild(loader);
            
            // Load remaining products
            let page = 2;
            while (loadedCount < totalCount) {
                const response = await fetch(`/ai/api/products/batch?page=${page}&limit=250`);
                const data = await response.json();
                
                if (data.success && data.products) {
                    // Add products to the page
                    data.products.forEach(product => {
                        // Create product card HTML (simplified version)
                        // In production, you'd want to match the template structure
                    });
                    
                    if (!data.has_next) break;
                    page++;
                }
            }
            
            loader.remove();
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }
}

// Load products on page load
window.addEventListener('load', loadAllProducts);
</script>
{% endblock %}</content>
</invoke>