{% extends "base.html" %}

{% block title %}Admin Login - Shopify AI Control{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow-sm">
            <div class="card-body p-4">
                <h2 class="text-center mb-4">
                    <i class="fas fa-user-shield text-primary"></i> Admin Login
                </h2>
                <form method="POST" action="{{ url_for('auth.admin_login') }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" autofocus required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                </form>
                <hr class="my-4">
                <div class="text-center">
                    <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                        <i class="fas fa-arrow-left"></i> Back to User Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}