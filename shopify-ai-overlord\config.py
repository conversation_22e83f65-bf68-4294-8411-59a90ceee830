import os
from datetime import timedelta
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://postgres:<EMAIL>:5432/postgres'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session configuration
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=1)
    
    # Shopify Configuration
    # OAuth settings removed - using custom app authentication
    # SHOPIFY_API_KEY and SHOPIFY_API_SECRET no longer needed
    SHOPIFY_APP_URL = os.environ.get('SHOPIFY_APP_URL', 'http://localhost:5000')
    
    # AI Configuration
    ANTHROPIC_API_KEY = os.environ.get('ANTHROPIC_API_KEY')
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')  # Optional, for comparison
    
    # Google OAuth Configuration
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID', '988316679046-ae2puc7vretbc2raedqpvju4ufmu2qpf.apps.googleusercontent.com')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET', '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')
    GOOGLE_OAUTH_REDIRECT_URI = os.environ.get('GOOGLE_OAUTH_REDIRECT_URI', 'http://localhost:5000/api/auth/callback/google')
    
    # Redis Configuration (Upstash)
    REDIS_URL = os.environ.get('REDIS_URL', 'rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE')
    
    # Daily Quota Configuration
    TARGET_COMPLETION_DAYS = int(os.environ.get('TARGET_COMPLETION_DAYS', '90'))
    MIN_PRODUCTS_PER_DAY = int(os.environ.get('MIN_PRODUCTS_PER_DAY', '3'))
    MAX_PRODUCTS_PER_DAY = int(os.environ.get('MAX_PRODUCTS_PER_DAY', '60'))
    COLLS_PER_PRODUCT = float(os.environ.get('COLLS_PER_PRODUCT', '1.5'))
    MAX_COLLECTIONS_PER_DAY = int(os.environ.get('MAX_COLLECTIONS_PER_DAY', '20'))