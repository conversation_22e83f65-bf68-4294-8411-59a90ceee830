"""Add change tracking and optimization status tables

Revision ID: c0ba407f5fad
Revises: 
Create Date: 2025-05-27 14:05:36.759724

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c0ba407f5fad'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('change_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.String(length=100), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=False),
    sa.Column('before_data', sa.JSON(), nullable=True),
    sa.Column('after_data', sa.<PERSON>(), nullable=True),
    sa.Column('change_metadata', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('reverted_at', sa.DateTime(), nullable=True),
    sa.Column('reverted_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['reverted_by'], ['shops.id'], ),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('homepage_seo',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('meta_description', sa.Text(), nullable=True),
    sa.Column('keywords', sa.Text(), nullable=True),
    sa.Column('analysis_data', sa.JSON(), nullable=True),
    sa.Column('competitor_urls', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('shop_id')
    )
    op.create_table('optimization_status',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.String(length=100), nullable=False),
    sa.Column('is_optimized', sa.Boolean(), nullable=True),
    sa.Column('optimization_type', sa.String(length=50), nullable=True),
    sa.Column('optimized_at', sa.DateTime(), nullable=True),
    sa.Column('optimized_by', sa.String(length=100), nullable=True),
    sa.Column('current_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('shop_id', 'resource_type', 'resource_id', 'optimization_type', name='_shop_resource_optimization_uc')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('optimization_status')
    op.drop_table('homepage_seo')
    op.drop_table('change_logs')
    # ### end Alembic commands ###
