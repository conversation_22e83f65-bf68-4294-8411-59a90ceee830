# Shopify AI Control - Complete Setup Guide

## Step 1: Create Shopify Partner Account & App

### 1.1 Create Partner Account
1. Go to https://partners.shopify.com
2. Click "Join now" and create your account
3. Complete your partner profile

### 1.2 Create a New App
1. In Partner Dashboard, click "Apps" → "Create app"
2. Choose "Create app manually"
3. Fill in:
   - **App name**: Shopify AI Control
   - **App URL**: `http://localhost:5000` (for development)
   - **Allowed redirection URL(s)**: `http://localhost:5000/shopify/callback`

### 1.3 Get Credentials
1. Go to your app's settings
2. Copy:
   - **Client ID** (API key)
   - **Client secret** (API secret key)

## Step 2: Get Claude API Key

1. Go to https://console.anthropic.com
2. Sign up or log in
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-ant-api...`)

## Step 3: Set Up Local Environment

### 3.1 Clone and Install
```bash
cd shopify-ai-control
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3.2 Create .env File
Create a `.env` file in the root directory:

```env
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-make-it-random
DATABASE_URL=sqlite:///shopify_ai_control.db

# Shopify App Credentials
SHOPIFY_API_KEY=your-client-id-from-shopify
SHOPIFY_API_SECRET=your-client-secret-from-shopify
SHOPIFY_APP_URL=http://localhost:5000
SHOPIFY_SCOPES=read_products,write_products,read_orders,write_orders,read_customers,write_customers

# Anthropic API Key (REQUIRED for Claude AI features)
ANTHROPIC_API_KEY=sk-ant-api-your-key-here

# Optional
REDIS_URL=redis://localhost:6379/0
```

### 3.3 Initialize Database
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

## Step 4: Run the Application

```bash
python run.py
```

Visit http://localhost:5000

## Step 5: Connect Your Store

1. Click "Connect Your Shopify Store"
2. Enter your store domain (e.g., `mystore` for `mystore.myshopify.com`)
3. Authorize the app in Shopify
4. You'll be redirected to the dashboard

## Features Overview

### SEO Module
- **Product SEO**: AI-powered optimization for product titles, descriptions, and meta tags
- **Collection SEO**: Optimize collection pages for search engines
- **Multi-Language SEO**: Translate and optimize for international markets
- **Technical SEO**: Site structure and performance optimization

### Ads Module
- **Google Ads**: Generate and optimize Google Shopping campaigns
- **Facebook/Meta Ads**: Create compelling ad copy and audiences
- **TikTok Ads**: Trending content and hashtag optimization
- **Analytics**: Cross-platform performance tracking

### Email Marketing
- **Campaigns**: AI-generated email content
- **Automation**: Smart customer journey flows
- **Segmentation**: AI-powered customer grouping
- **Templates**: Conversion-optimized designs

### Customer Support
- **AI Chatbot**: 24/7 automated support
- **Ticket Management**: Smart routing and responses
- **FAQs**: Auto-generated knowledge base
- **Insights**: Customer sentiment analysis

## Troubleshooting

### Common Issues

1. **"Invalid API Key" for Claude**
   - Make sure your ANTHROPIC_API_KEY is correct
   - Check if the key starts with `sk-ant-api`

2. **"Shop not found" error**
   - Ensure you're using the correct shop domain
   - Don't include `https://` or `.myshopify.com` in the connection form

3. **OAuth error**
   - Check that your redirect URL matches exactly
   - Verify your Shopify API credentials

## Production Deployment

For production:
1. Use HTTPS (required by Shopify)
2. Update URLs in both .env and Shopify app settings
3. Use a production database (PostgreSQL recommended)
4. Set up proper logging and monitoring

## Support

For issues or questions:
- Check the logs in the console
- Ensure all API keys are properly set
- Verify Shopify app permissions