from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()


class AccessKey(Base):
    """Model for storing access codes with basic information."""
    __tablename__ = 'access_keys'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = Column(String(255), unique=True, nullable=False, index=True)
    email = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationship to UserSettings
    user_settings = relationship("UserSettings", back_populates="access_key", uselist=False)
    
    def __repr__(self):
        return f"<AccessKey(code='{self.code}', email='{self.email}', is_active={self.is_active})>"


class UserSettings(Base):
    """Model for storing user's API keys and settings."""
    __tablename__ = 'user_settings'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    access_key_id = Column(String(36), ForeignKey('access_keys.id'), unique=True, nullable=False)
    shopify_api_key = Column(Text, nullable=True)
    anthropic_api_key = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationship back to AccessKey
    access_key = relationship("AccessKey", back_populates="user_settings")
    
    def __repr__(self):
        return f"<UserSettings(access_key_id='{self.access_key_id}')>"