from datetime import datetime
from app import db
import json

class ChangeLog(db.Model):
    """Track all changes made to Shopify store items"""
    __tablename__ = 'change_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.<PERSON>teger, db.Foreign<PERSON>ey('shops.id'), nullable=False)
    
    # Change details
    resource_type = db.Column(db.String(50), nullable=False)  # product, collection, page, homepage
    resource_id = db.Column(db.String(100), nullable=False)  # Shopify resource ID
    action = db.Column(db.String(50), nullable=False)  # optimize_seo, update_title, etc.
    
    # Store before and after states
    before_data = db.Column(db.JSON, default={})
    after_data = db.Column(db.JSON, default={})
    
    # Additional metadata
    change_metadata = db.Column(db.JSON, default={})  # Store things like AI model used, keywords, etc.
    
    # Status
    status = db.Column(db.String(20), default='completed')  # completed, reverted, partial
    reverted_at = db.Column(db.DateTime)
    reverted_by = db.Column(db.Integer, db.ForeignKey('shops.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    shop = db.relationship('Shop', foreign_keys=[shop_id], backref='change_logs')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'action': self.action,
            'before_data': self.before_data,
            'after_data': self.after_data,
            'metadata': self.change_metadata,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'reverted_at': self.reverted_at.isoformat() if self.reverted_at else None
        }


class OptimizationStatus(db.Model):
    """Track optimization status for resources"""
    __tablename__ = 'optimization_status'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False)
    resource_type = db.Column(db.String(50), nullable=False)
    resource_id = db.Column(db.String(100), nullable=False)
    
    # Optimization details
    is_optimized = db.Column(db.Boolean, default=False)
    optimization_type = db.Column(db.String(50))  # seo, ads, email, etc.
    optimized_at = db.Column(db.DateTime)
    optimized_by = db.Column(db.String(100))  # AI model used
    
    # Current state
    current_data = db.Column(db.JSON, default={})
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('shop_id', 'resource_type', 'resource_id', 'optimization_type', 
                                         name='_shop_resource_optimization_uc'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'is_optimized': self.is_optimized,
            'optimization_type': self.optimization_type,
            'optimized_at': self.optimized_at.isoformat() if self.optimized_at else None,
            'optimized_by': self.optimized_by,
            'current_data': self.current_data
        }


class TranslationStatus(db.Model):
    """Track translation status for resources"""
    __tablename__ = 'translation_status'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False)
    resource_type = db.Column(db.String(50), nullable=False)  # product, collection, page
    resource_id = db.Column(db.String(100), nullable=False)
    language = db.Column(db.String(10), nullable=False)  # Language code (fr, es, de, etc.)
    
    # Translation details
    is_translated = db.Column(db.Boolean, default=False)
    translated_at = db.Column(db.DateTime)
    translated_by = db.Column(db.String(100))  # AI model used
    
    # Fields that were translated
    translated_fields = db.Column(db.JSON, default=[])  # ['title', 'description', etc.]
    
    # Store actual translated content (including handle)
    translated_data = db.Column(db.JSON, default={})  # {'title': '...', 'handle': '...', etc.}
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('shop_id', 'resource_type', 'resource_id', 'language', 
                                         name='_shop_resource_language_uc'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'language': self.language,
            'is_translated': self.is_translated,
            'translated_at': self.translated_at.isoformat() if self.translated_at else None,
            'translated_by': self.translated_by,
            'translated_fields': self.translated_fields
        }


class HomepageSEO(db.Model):
    """Store homepage SEO settings"""
    __tablename__ = 'homepage_seo'
    
    id = db.Column(db.Integer, primary_key=True)
    shop_id = db.Column(db.Integer, db.ForeignKey('shops.id'), nullable=False, unique=True)
    
    # SEO fields
    title = db.Column(db.String(255))
    meta_description = db.Column(db.Text)
    keywords = db.Column(db.Text)  # Comma-separated
    
    # Analysis data
    analysis_data = db.Column(db.JSON, default={})
    competitor_urls = db.Column(db.JSON, default=[])
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    shop = db.relationship('Shop', backref='homepage_seo', uselist=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'meta_description': self.meta_description,
            'keywords': self.keywords,
            'analysis_data': self.analysis_data,
            'competitor_urls': self.competitor_urls,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }