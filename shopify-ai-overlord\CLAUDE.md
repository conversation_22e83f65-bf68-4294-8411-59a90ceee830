# Global Coding Instructions

## Efficiency Guidelines

For maximum efficiency, whenever you need to perform multiple independent operations, invoke all relevant tools simultaneously rather than sequentially.

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

## CI/CD Pipeline Setup

### Environment Overview
- **Production:** `main` branch → https://shopify-ai-overlord-3vomn.ondigitalocean.app
- **Staging:** `dev` branch → Auto-deployed staging environment
- **Admin credentials stored in Digital Ocean environment variables**

### Digital Ocean Apps
- **Production App ID:** `fe2a03aa-3fa1-4804-a846-107af8cbef7b`
- **Staging App ID:** `6a94e027-b131-449b-b9ea-1e0dfffe0712`

### Git Workflow
```bash
# Development workflow
git checkout dev
git add .
git commit -m "feature description"
git push  # Auto-deploys to staging

# Production deployment
git checkout main
git merge dev
git push  # Auto-deploys to production
```

### Database Configuration

#### Production Database (Original - CORRECTLY CONFIGURED)
- **Project ID:** melucdevsrbljvxesbzo
- **Current Status:** ✅ Already using Shared Pooler (IPv4 compatible)
- **Current URL:** `postgresql://postgres.melucdevsrbljvxesbzo:<EMAIL>:5432/postgres` (Shared Pooler)

#### Dev Database (New - December 2024)
- **Project ID:** sbwmeetdwnwqzrcivobh
- **Name:** shopify-ai-overlord-dev
- **Region:** eu-west-1
- **Cost:** $10/month
- **Password:** Sunde7369!23

**Dev Database Connection Options:**
- **Direct Connection:** `postgresql://postgres:Sunde7369!<EMAIL>:5432/postgres` (Not IPv4 compatible)
- **Transaction Pooler:** `postgres://postgres:Sunde7369!<EMAIL>:6543/postgres` (Not IPv4 compatible)
- **Session Pooler (IPv4):** `postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:6543/postgres` (URL-encoded: ! = %21)
- **Shared Pooler (IPv4):** `postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:5432/postgres` (Alternative)

### Environment Variables (Digital Ocean)
**Production:**
- `FLASK_ENV=production`
- `DATABASE_URL=postgres://postgres.melucdevsrbljvxesbzo:<EMAIL>:6543/postgres` (NEEDS REVERT)
- `REDIS_URL=rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE`
- `SHOPIFY_APP_URL=https://shopify-ai-overlord-3vomn.ondigitalocean.app`
- `GOOGLE_OAUTH_REDIRECT_URI=https://shopify-ai-overlord-3vomn.ondigitalocean.app/api/auth/callback/google`
- `GOOGLE_CLIENT_ID=988316679046-ae2puc7vretbc2raedqpvju4ufmu2qpf.apps.googleusercontent.com`
- `GOOGLE_CLIENT_SECRET=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`
- Admin credentials in environment

**Staging:**
- `FLASK_ENV=staging`
- `DATABASE_URL=postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:5432/postgres` (Dev DB - Shared Pooler)
- `REDIS_URL=rediss://:<EMAIL>:6379?ssl_cert_reqs=CERT_NONE`
- `ADMIN_USERNAME=stagingadmin`
- `ADMIN_PASSWORD=StagingPass123!`

### Application Architecture
- **Web Service:** Flask app (`python run.py`)
- **Worker:** Celery worker for background tasks
- **Scheduler:** Celery beat for scheduled tasks
- **Database:** Supabase PostgreSQL
- **Cache/Queue:** Upstash Redis

### Instance Sizes
- **Production:** 2GB instances for web/worker, 1GB for scheduler
- **Staging:** 1GB instances (cost optimization)

### Auto-Deploy Configuration
- Both environments monitor their respective branches
- Automatic deployment on push
- Build command: `pip install -r requirements.txt`
- No manual intervention required

## Database Connection Pool Fix

### Problem RESOLVED ✅
Both production and staging were hitting "MaxClientsInSessionMode: max clients reached" error due to using incorrect pooler configurations.

### Solution Implemented
1. ✅ **Production**: Confirmed already using correct Shared Pooler configuration
2. ✅ **Dev Database**: Created separate dev database with proper schema matching production
3. ✅ **Staging**: Updated to use Shared Pooler (port 5432) instead of Session Pooler (port 6543)

### Current Configuration (UPDATED JUNE 2025)
**Production (Correct):**
```
postgresql://postgres.melucdevsrbljvxesbzo:<EMAIL>:5432/postgres
```

**Staging (Updated to Transaction Pooler - JUNE 2025):**
```
postgresql://postgres.sbwmeetdwnwqzrcivobh:<EMAIL>:6543/postgres
```

### Why This Works
- **Production**: Uses Shared Pooler (port 5432) - handles moderate traffic efficiently
- **Staging**: Uses Transaction Pooler (port 6543) - higher connection limits for testing/development
- **IPv4 compatible** for Digital Ocean App Platform  
- **Separate databases** prevent production/staging interference
- **Proper schema sync** ensures consistency between environments

### Recent Fixes (June 2025)
✅ **Staging Database Pool**: Switched from Shared Pooler (5432) to Transaction Pooler (6543) to resolve "MaxClientsInSessionMode" errors
✅ **Collections Page**: Fixed AttributeError in seo/collections route by adding null checks for change_metadata
✅ **Translation Automation**: Verified task registration and scheduling - translations are triggered after collection creation
✅ **Password Encoding**: Confirmed correct URL encoding of password (! = %21)